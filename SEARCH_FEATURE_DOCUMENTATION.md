# 分类值搜索功能文档

## 功能概述

为分类值列表接口添加了按 `display_name` 模糊搜索的功能，支持管理员和普通用户接口。

## 修改内容

### 1. Service层修改 (`src/services/tag_classification_service/service.py`)

#### 方法签名更新
```python
def get_classification_values(
    self,
    dimension_id: Optional[int] = None,
    skip: int = 0,
    limit: int = 100,
    parent_id: Optional[int] = None,
    is_active: Optional[bool] = None,
    search: Optional[str] = None,  # 新增参数
) -> Tuple[List[ClassificationValue], int]:
```

#### 搜索逻辑实现
```python
if search is not None and search.strip():
    # 支持按display_name模糊搜索
    search_term = f"%{search.strip()}%"
    query = query.filter(ClassificationValue.display_name.ilike(search_term))
```

### 2. Admin Router修改 (`src/services/tag_classification_service/admin_router.py`)

#### 接口参数更新
```python
@router.get(
    "/classifications/values",
    response_model=PageResponse,
    summary="获取分类值列表",
    description="获取分类值列表，支持按维度筛选、模糊搜索和分页 (需要管理员权限)",
)
async def get_classification_values_admin(
    # ... 其他参数
    search: Optional[str] = Query(default=None, description="搜索关键词，支持按display_name模糊搜索"),
):
```

#### Service调用更新
```python
values, total = classification_service.get_classification_values(
    dimension_id=dimension_id,
    skip=skip,
    limit=size,
    parent_id=parent_id,
    is_active=is_active,
    search=search  # 传递搜索参数
)
```

### 3. 普通Router修改 (`src/services/tag_classification_service/router.py`)

#### 接口参数更新
```python
@router.get(
    "/classifications/dimensions/{dimension_id}/values",
    response_model=PageResponse,
    summary="获取分类值列表",
    description="获取指定维度的分类值分页列表，支持模糊搜索",
)
async def get_classification_values(
    # ... 其他参数
    search: Optional[str] = Query(default=None, description="搜索关键词，支持按display_name模糊搜索"),
):
```

#### Service调用更新
```python
values, total = classification_service.get_classification_values(
    dimension_id=dimension_id, skip=skip, limit=size, parent_id=parent_id, is_active=is_active, search=search
)
```

## API使用示例

### 管理员接口
```bash
# 获取所有分类值
GET /api/v1/admin/classifications/values

# 搜索包含"科技"的分类值
GET /api/v1/admin/classifications/values?search=科技

# 在特定维度中搜索
GET /api/v1/admin/classifications/values?dimension_id=1&search=科技

# 组合搜索条件
GET /api/v1/admin/classifications/values?dimension_id=1&search=科技&is_active=true&page=1&size=20
```

### 普通用户接口
```bash
# 获取指定维度的所有分类值
GET /api/v1/classifications/dimensions/1/values

# 在指定维度中搜索包含"科技"的分类值
GET /api/v1/classifications/dimensions/1/values?search=科技

# 组合搜索条件
GET /api/v1/classifications/dimensions/1/values?search=科技&is_active=true&page=1&size=20
```

## 搜索特性

### 1. 模糊搜索
- 使用 `ILIKE` 操作符进行大小写不敏感的模糊匹配
- 自动在搜索词前后添加 `%` 通配符

### 2. 参数处理
- `search` 参数为可选，默认值为 `None`
- 自动处理空字符串和只包含空格的情况
- 搜索前会自动去除首尾空格

### 3. 兼容性
- 保持与现有API的完全兼容
- 不传 `search` 参数时行为与之前完全一致
- 支持与其他筛选条件组合使用

## 数据库查询示例

当传入搜索参数时，生成的SQL查询类似：
```sql
SELECT * FROM classification_values 
WHERE display_name ILIKE '%科技%'
AND dimension_id = 1
AND is_active = true
ORDER BY sort_order DESC, display_name
LIMIT 20 OFFSET 0;
```

## 注意事项

1. **性能考虑**: 模糊搜索可能影响查询性能，建议在 `display_name` 字段上创建适当的索引
2. **搜索范围**: 目前只支持按 `display_name` 搜索，未来可扩展支持其他字段
3. **权限控制**: 搜索功能遵循现有的权限控制机制
4. **分页**: 搜索结果支持分页，总数统计准确

## 测试建议

1. 测试空搜索参数的处理
2. 测试中文和英文搜索
3. 测试特殊字符的处理
4. 测试搜索结果的分页
5. 测试与其他筛选条件的组合使用
