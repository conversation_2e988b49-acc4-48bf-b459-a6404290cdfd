-- 金十数据快讯 data_processing_pipelines 详细配置
-- 基于成功测试结果和业务需求设计的数据处理管道

-- 1. 创建金十数据专用处理管道
INSERT INTO data_processing_pipelines (
    pipeline_code,
    version,
    pipeline_name,
    description,
    business_data_type,
    source_id,
    url_pattern,
    domain_pattern,
    
    -- 字段映射配置
    field_mapping,
    
    -- 数据提取配置
    data_extraction_config,
    
    -- 数据转换配置
    data_transformation_config,
    
    -- 数据验证配置
    data_validation_config,
    
    -- 数据增强配置
    data_enrichment_config,
    
    priority,
    is_active,
    is_default,
    created_by
) VALUES (
    'jin10_flash_news_specific',
    1,
    '金十数据快讯专用处理管道',
    '专门处理金十数据网站的快讯内容，包含字段映射、时间转换、重要性识别、标签提取等功能',
    'flash_news',
    (SELECT id FROM data_sources WHERE name = '金十数据快讯'),
    '^https://www\.jin10\.com.*',
    'jin10.com',
    
    -- field_mapping: 字段映射规则
    '{
        "title": {
            "source_field": "content",
            "target_field": "title",
            "required": true,
            "max_length": 500
        },
        "content": {
            "source_field": "content", 
            "target_field": "content",
            "required": true,
            "max_length": 2000
        },
        "publish_time": {
            "source_field": "publish_time",
            "target_field": "publish_time", 
            "required": true,
            "format": "time_conversion"
        },
        "data_id": {
            "source_field": "data_id",
            "target_field": "external_id",
            "required": false
        },
        "source": {
            "source_field": "source",
            "target_field": "source_media",
            "default_value": "金十数据"
        },
        "url": {
            "source_field": "url", 
            "target_field": "original_url",
            "required": true
        }
    }',
    
    -- data_extraction_config: 数据提取配置
    '{
        "extraction_rules": {
            "content_cleaning": {
                "enabled": true,
                "remove_html_tags": true,
                "remove_extra_whitespace": true,
                "remove_special_chars": false,
                "trim_content": true,
                "apply_title_cleaning": true,
                "apply_content_cleaning": true,
                "preserve_formatting": false,
                "normalize_unicode": true
            },
            "title_extraction": {
                "enabled": true,
                "title_selectors": [".J_flash_text", ".jin-flash_text-box p", "[data-content]"],
                "fallback_to_content": true,
                "max_title_length": 200,
                "min_title_length": 5,
                "extract_from_first_sentence": true,
                "remove_title_prefixes": true,
                "apply_title_filters": true
            },
            "time_extraction": {
                "enabled": true,
                "time_selectors": [".jin-flash_time", "[class*=\"time\"]"],
                "time_formats": ["HH:mm:ss", "HH:mm"],
                "fallback_to_crawl_time": true,
                "timezone": "Asia/Shanghai",
                "validate_time_range": true
            },
            "content_extraction": {
                "enabled": true,
                "content_selectors": [".J_flash_text", ".jin-flash_text-box p"],
                "min_content_length": 10,
                "max_content_length": 50000,
                "remove_content_prefixes": true,
                "filter_spam_content": true,
                "filter_ad_content": true,
                "apply_content_filters": true,
                "preserve_line_breaks": false
            },
            "metadata_extraction": {
                "enabled": true,
                "extract_data_id": true,
                "extract_css_classes": true,
                "extract_element_attributes": true,
                "extract_importance_indicators": true,
                "extract_urgency_markers": true
            }
        }
    }',
    
    -- data_transformation_config: 数据转换配置
    '{
        "transformations": {
            "time_conversion": {
                "enabled": true,
                "input_format": "HH:mm:ss",
                "output_format": "timestamp",
                "timezone": "Asia/Shanghai",
                "date_completion": {
                    "enabled": true,
                    "use_crawl_date": true,
                    "handle_cross_midnight": true
                }
            },
            "title_cleaning": {
                "enabled": true,
                "jin10_patterns": [
                    "^金十数据\\d+月\\d+日讯[，：:]*\\s*",
                    "^金十数据\\s*\\d+月\\d+日讯[，：:]*\\s*",
                    "^金十数据\\s*\\d+月\\s*\\d+日讯[，：:]*\\s*",
                    "^金十数据\\s*\\d+月\\s*\\d+日\\s*讯[，：:]*\\s*",
                    "^金十数据整理[，：:]*\\s*"
                ],
                "source_prefix_patterns": [
                    "^【.*?】\\s*",
                    "^\\[.*?\\]\\s*",
                    "^\\(.*?\\)\\s*",
                    "^.*?快讯[，：:]*\\s*(?=\\s|$)",
                    "^.*?新闻[，：:]*\\s*(?=\\s|$)",
                    "^.*?资讯[，：:]*\\s*(?=\\s|$)",
                    "^.*?报道[，：:]*\\s*(?=\\s|$)",
                    "^.*?动态[，：:]*\\s*(?=\\s|$)",
                    "^.*?分析[，：:]*\\s*(?=\\s|$)",
                    "^.*?评论[，：:]*\\s*(?=\\s|$)"
                ],
                "protected_patterns": [
                    "^.*?通讯社[：:]*\\s*",
                    "^.*?消息人士[：:]*\\s*",
                    "^.*?社消息[：:]*\\s*",
                    "^.*?社报道[：:]*\\s*",
                    "^.*?社[：:]*\\s*"
                ],
                "allowed_chars": "[-\\u4e00-\\u9fa5a-zA-Z0-9\\s，。！？；：\"\"''（）【】「」『』()\\[\\]{}_.·%°¥$€±÷×+=><≥≤/《》]",
                "punctuation_chars": "[-，。！？；：\"\"''（）【】_.\\\\s]",
                "min_title_length": 5,
                "max_title_length": 200,
                "filter_patterns": [
                    "\\s+",
                    "http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\\\(\\\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+",
                    "www\\.[^\\s]+",
                    "[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}",
                    "\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}"
                ],
                "clean_rules": [
                    "remove_extra_spaces",
                    "remove_special_chars",
                    "normalize_punctuation",
                    "remove_urls",
                    "remove_emails",
                    "remove_ips"
                ],
                "quality_weights": {
                    "length": 0.2,
                    "readability": 0.3,
                    "uniqueness": 0.3,
                    "relevance": 0.2
                },
                "readability_config": {
                    "min_word_length": 2,
                    "max_word_length": 15,
                    "min_sentence_length": 3,
                    "max_sentence_length": 50
                },
                "deduplication_config": {
                    "similarity_threshold": 0.9,
                    "min_hash_length": 16,
                    "max_hash_length": 64
                }
            },
            "content_cleaning": {
                "enabled": true,
                "jin10_patterns": [
                    "^金十数据\\d+月\\d+日讯[，：:]*\\s*",
                    "^金十数据\\s*\\d+月\\d+日讯[，：:]*\\s*",
                    "^金十数据\\s*\\d+月\\s*\\d+日讯[，：:]*\\s*",
                    "^金十数据\\s*\\d+月\\s*\\d+日\\s*讯[，：:]*\\s*",
                    "^金十数据整理[，：:]*\\s*"
                ],
                "source_prefix_patterns": [
                    "^【.*?】\\s*",
                    "^\\[.*?\\]\\s*",
                    "^\\(.*?\\)\\s*",
                    "^.*?快讯[，：:]*\\s*",
                    "^.*?新闻[，：:]*\\s*(?=\\s|$)",
                    "^.*?资讯[，：:]*\\s*(?=\\s|$)",
                    "^.*?报道[，：:]*\\s*(?=\\s|$)",
                    "^.*?动态[，：:]*\\s*(?=\\s|$)",
                    "^.*?分析[，：:]*\\s*(?=\\s|$)",
                    "^.*?评论[，：:]*\\s*(?=\\s|$)"
                ],
                "protected_patterns": [
                    "^.*?通讯社[：:]*\\s*",
                    "^.*?消息人士[：:]*\\s*",
                    "^.*?社消息[：:]*\\s*",
                    "^.*?社报道[：:]*\\s*",
                    "^.*?社[：:]*\\s*"
                ],
                "allowed_chars": "[-\\u4e00-\\u9fa5a-zA-Z0-9\\s，。！？；：\"\"''（）【】「」『』()\\[\\]{}_.·%°¥$€±÷×+=><≥≤/《》]",
                "punctuation_chars": "[-，。！？；：\"\"''（）【】_.\\\\s]",
                "min_content_length": 10,
                "max_content_length": 50000,
                "ad_keywords": [
                    "广告", "推广", "赞助", "sponsored", "advertisement", "promotion",
                    "点击购买", "立即购买", "限时优惠", "特价", "折扣", "优惠券",
                    "免费试用", "注册即送", "投资理财", "高收益", "稳赚不赔",
                    "快速致富", "赚钱项目", "创业机会"
                ],
                "spam_keywords": [
                    "垃圾邮件", "spam", "病毒", "木马", "钓鱼", "诈骗",
                    "中奖", "彩票", "博彩", "赌博", "色情", "成人",
                    "一夜情", "约炮", "援交"
                ],
                "filter_patterns": [
                    "\\s+",
                    "http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\\\(\\\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+",
                    "www\\.[^\\s]+",
                    "[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}",
                    "\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}"
                ],
                "quality_weights": {
                    "length": 0.3,
                    "readability": 0.2,
                    "uniqueness": 0.2,
                    "relevance": 0.3
                },
                "readability_config": {
                    "min_sentence_length": 5,
                    "max_sentence_length": 100,
                    "min_word_length": 2,
                    "max_word_length": 20
                },
                "deduplication_config": {
                    "similarity_threshold": 0.8,
                    "min_hash_length": 32,
                    "max_hash_length": 128
                },
                "enable_html_clean": true,
                "enable_prefix_clean": true,
                "enable_char_clean": true,
                "enable_space_clean": true,
                "enable_punctuation_clean": true
            },
            "importance_detection": {
                "enabled": true,
                "importance_indicators": [
                    "【.*?】",
                    "\\[.*?\\]",
                    "重要|紧急|突发|快讯|breaking|urgent"
                ],
                "keyword_importance": {
                    "high_importance": [
                        "央行", "降准", "降息", "加息", "美联储", "欧央行",
                        "GDP", "CPI", "PMI", "通胀", "就业", "失业率",
                        "暴跌", "暴涨", "熔断", "停牌", "涨停", "跌停"
                    ],
                    "medium_importance": [
                        "股指期货", "国债期货", "原油", "黄金", "比特币",
                        "苹果", "特斯拉", "微软", "腾讯", "阿里"
                    ]
                }
            },
            "urgency_classification": {
                "enabled": true,
                "urgency_rules": {
                    "urgent": {
                        "patterns": ["【.*?】", "紧急", "突发", "breaking"],
                        "level": 3
                    },
                    "important": {
                        "patterns": ["重要", "央行", "降准", "降息"],
                        "level": 2
                    },
                    "normal": {
                        "default": true,
                        "level": 1
                    }
                }
            }
        }
    }',
    
    -- data_validation_config: 数据验证配置
    '{
        "validation_rules": {
            "required_fields": {
                "enabled": true,
                "fields": ["title", "content", "publish_time"],
                "action_on_missing": "reject"
            },
            "title_quality": {
                "enabled": true,
                "min_title_length": 5,
                "max_title_length": 200,
                "min_word_count": 2,
                "reject_test_content": true,
                "test_content_patterns": [
                    "测试", "test", "demo", "样例"
                ],
                "quality_weights": {
                    "length": 0.2,
                    "readability": 0.3,
                    "uniqueness": 0.3,
                    "relevance": 0.2
                },
                "readability_thresholds": {
                    "min_word_length": 2,
                    "max_word_length": 15,
                    "min_sentence_length": 3,
                    "max_sentence_length": 50
                },
                "filter_invalid_chars": true,
                "require_meaningful_content": true
            },
            "content_quality": {
                "enabled": true,
                "min_content_length": 10,
                "max_content_length": 50000,
                "min_word_count": 3,
                "reject_test_content": true,
                "test_content_patterns": [
                    "测试", "test", "demo", "样例"
                ],
                "quality_weights": {
                    "length": 0.3,
                    "readability": 0.2,
                    "uniqueness": 0.2,
                    "relevance": 0.3
                },
                "readability_thresholds": {
                    "min_sentence_length": 5,
                    "max_sentence_length": 100,
                    "min_word_length": 2,
                    "max_word_length": 20
                },
                "filter_invalid_chars": true,
                "require_meaningful_content": true
            },
            "time_validation": {
                "enabled": true,
                "max_future_hours": 1,
                "max_past_hours": 24,
                "require_valid_time": true,
                "timezone": "Asia/Shanghai",
                "validate_format": true
            },
            "duplicate_detection": {
                "enabled": true,
                "check_fields": ["data_id", "content"],
                "title_similarity_threshold": 0.9,
                "content_similarity_threshold": 0.8,
                "time_window_hours": 24,
                "hash_algorithms": ["md5", "sha256"],
                "fuzzy_matching": true
            },
            "spam_filtering": {
                "enabled": true,
                "ad_keywords": [
                    "广告", "推广", "赞助", "sponsored", "advertisement", "promotion",
                    "点击购买", "立即购买", "限时优惠", "特价", "折扣", "优惠券",
                    "免费试用", "注册即送", "投资理财", "高收益", "稳赚不赔",
                    "快速致富", "赚钱项目", "创业机会"
                ],
                "spam_keywords": [
                    "垃圾邮件", "spam", "病毒", "木马", "钓鱼", "诈骗",
                    "中奖", "彩票", "博彩", "赌博", "色情", "成人",
                    "一夜情", "约炮", "援交"
                ],
                "spam_patterns": [
                    ".*广告.*",
                    ".*推广.*",
                    ".*优惠券.*",
                    ".*赚钱.*",
                    ".*投资理财.*",
                    ".*博彩.*",
                    ".*色情.*"
                ],
                "url_filtering": {
                    "enabled": true,
                    "remove_urls": true,
                    "whitelist_domains": ["jin10.com"],
                    "blacklist_domains": []
                },
                "action_on_spam": "reject"
            },
            "character_validation": {
                "enabled": true,
                "allowed_chars_pattern": "[-\\u4e00-\\u9fa5a-zA-Z0-9\\s，。！？；：\"\"''（）【】「」『』()\\[\\]{}_.·%°¥$€±÷×+=><≥≤/《》]",
                "remove_invalid_chars": true,
                "normalize_whitespace": true,
                "max_consecutive_spaces": 2,
                "trim_punctuation": true
            }
        }
    }',
    
    -- data_enrichment_config: 数据增强配置
    '{
        "enrichment_features": {
            "tag_extraction": {
                "enabled": true,
                "extraction_methods": {
                    "financial_entities": {
                        "enabled": true,
                        "entity_types": [
                            "stock_codes", "company_names", "currencies", 
                            "commodities", "financial_instruments"
                        ],
                        "patterns": {
                            "stock_codes": "\\b[A-Z]{2,4}\\d{4,6}\\b|\\b\\d{6}\\b",
                            "currencies": "美元|欧元|英镑|日元|人民币|USD|EUR|GBP|JPY|CNY",
                            "commodities": "原油|黄金|白银|铜|铁矿石|大豆|玉米"
                        }
                    },
                    "keyword_extraction": {
                        "enabled": true,
                        "categories": {
                            "monetary_policy": ["央行", "降准", "降息", "加息", "货币政策"],
                            "economic_indicators": ["GDP", "CPI", "PMI", "通胀", "就业"],
                            "market_events": ["暴跌", "暴涨", "熔断", "停牌", "涨停", "跌停"],
                            "geopolitical": ["制裁", "贸易战", "谈判", "协议"],
                            "corporate": ["财报", "业绩", "并购", "IPO", "分红"]
                        }
                    },
                    "importance_tags": {
                        "enabled": true,
                        "auto_tag_important": true,
                        "importance_threshold": 2
                    }
                }
            },
            "classification": {
                "enabled": true,
                "classification_dimensions": {
                    "content_type": {
                        "categories": ["市场动态", "政策解读", "数据发布", "公司公告", "国际财经"],
                        "auto_classify": true
                    },
                    "market_impact": {
                        "categories": ["利好", "利空", "中性", "不确定"],
                        "sentiment_analysis": true
                    },
                    "urgency_level": {
                        "categories": ["普通", "重要", "紧急"],
                        "based_on_keywords": true
                    }
                }
            },
            "related_content": {
                "enabled": true,
                "extract_related_stocks": true,
                "extract_related_sectors": true,
                "extract_related_keywords": true,
                "similarity_matching": true
            },
            "quality_scoring": {
                "enabled": true,
                "scoring_factors": {
                    "content_length": 0.2,
                    "importance_level": 0.3,
                    "keyword_relevance": 0.3,
                    "timeliness": 0.2
                },
                "min_quality_score": 0.3
            }
        }
    }',
    
    10,  -- 最高优先级
    true,
    false,  -- 不是默认管道，因为是专用管道
    'system'
) ON CONFLICT (pipeline_code, version) DO UPDATE SET
    pipeline_name = EXCLUDED.pipeline_name,
    description = EXCLUDED.description,
    field_mapping = EXCLUDED.field_mapping,
    data_extraction_config = EXCLUDED.data_extraction_config,
    data_transformation_config = EXCLUDED.data_transformation_config,
    data_validation_config = EXCLUDED.data_validation_config,
    data_enrichment_config = EXCLUDED.data_enrichment_config,
    priority = EXCLUDED.priority,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- 2. 创建快讯类型的通用默认管道（作为备用）
INSERT INTO data_processing_pipelines (
    pipeline_code,
    version,
    pipeline_name,
    description,
    business_data_type,
    source_id,
    
    field_mapping,
    data_extraction_config,
    data_transformation_config,
    data_validation_config,
    data_enrichment_config,
    
    priority,
    is_active,
    is_default,
    created_by
) VALUES (
    'flash_news_default',
    1,
    '快讯类型默认处理管道',
    '适用于所有快讯类型数据源的通用处理管道，提供基础的数据处理功能',
    'flash_news',
    NULL,  -- 通用管道，不绑定特定数据源
    
    -- 简化的字段映射
    '{
        "title": {"source_field": "title", "target_field": "title", "required": true},
        "content": {"source_field": "content", "target_field": "content", "required": true},
        "publish_time": {"source_field": "publish_time", "target_field": "publish_time", "required": true}
    }',
    
    -- 基础提取配置
    '{"extraction_rules": {"content_cleaning": {"enabled": true}}}',
    
    -- 基础转换配置
    '{"transformations": {"time_conversion": {"enabled": true}}}',
    
    -- 基础验证配置
    '{"validation_rules": {"required_fields": {"enabled": true, "fields": ["title", "content"]}}}',
    
    -- 基础增强配置
    '{"enrichment_features": {"tag_extraction": {"enabled": true}}}',
    
    5,   -- 中等优先级
    true,
    true,  -- 默认管道
    'system'
) ON CONFLICT (pipeline_code, version) DO UPDATE SET
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- 3. 验证管道配置
SELECT 
    dpp.pipeline_code,
    dpp.version,
    dpp.pipeline_name,
    dpp.business_data_type,
    ds.name as source_name,
    dpp.priority,
    dpp.is_active,
    dpp.is_default,
    dpp.created_at
FROM data_processing_pipelines dpp
LEFT JOIN data_sources ds ON dpp.source_id = ds.id
WHERE dpp.business_data_type = 'flash_news'
ORDER BY dpp.priority DESC, dpp.created_at DESC;

-- 4. 创建处理状态监控视图
CREATE OR REPLACE VIEW jin10_processing_status_view AS
SELECT
    rdr.id as raw_data_id,
    rdr.source_url,
    rdr.crawl_time,
    rdr.processing_status,
    dps.processing_stage,
    dps.progress_percentage,
    dps.processing_result,
    dps.data_quality_score,
    dps.tag_extraction_count,
    dps.error_message,
    fn.id as flash_news_id,
    fn.title,
    fn.urgency_level,
    fn.importance_score
FROM raw_data_records rdr
LEFT JOIN data_processing_status dps ON rdr.id = dps.raw_data_id
LEFT JOIN flash_news fn ON dps.target_table_id = fn.id AND dps.target_table_name = 'flash_news'
WHERE rdr.source_id = (SELECT id FROM data_sources WHERE name = '金十数据快讯')
ORDER BY rdr.crawl_time DESC;

-- 配置说明注释
/*
数据处理管道配置说明：

1. 金十数据专用管道 (jin10_flash_news_specific):
   - 专门处理金十数据的快讯内容
   - 包含完整的字段映射、转换、验证、增强功能
   - 最高优先级，确保金十数据使用此管道
   - 集成了详细的标题和内容清洗功能

2. 字段映射 (field_mapping):
   - title/content: 快讯标题和内容
   - publish_time: 时间字段转换
   - data_id: 外部唯一标识
   - source: 来源媒体

3. 数据提取 (data_extraction_config):
   - 内容清理: 去除HTML标签、多余空格、特殊字符
   - 标题提取: 智能提取标题，应用清洗规则
   - 时间提取: 支持多种时间格式，时区处理
   - 内容提取: 过滤垃圾内容、广告内容
   - 元数据提取: 提取data-id、重要性指标等属性

4. 数据转换 (data_transformation_config):
   - 时间转换: HH:mm:ss -> 完整时间戳
   - 标题清洗:
     * 去除金十数据特定前缀模式
     * 去除通用数据源前缀（【】、[]、快讯等）
     * 保护重要信息（通讯社、消息人士等）
     * 字符过滤和标准化
     * 长度控制和质量评分
   - 内容清洗:
     * 应用与标题相同的清洗规则
     * 额外的垃圾内容和广告过滤
     * 更大的长度限制（50000字符）
     * 去重和相似度检测
   - 重要性检测: 基于【】标识和关键词
   - 紧急度分类: 3级分类系统

5. 数据验证 (data_validation_config):
   - 必填字段检查
   - 标题质量验证:
     * 长度范围检查（5-200字符）
     * 可读性评分
     * 字符有效性验证
   - 内容质量验证:
     * 长度范围检查（10-50000字符）
     * 可读性和相关性评分
     * 测试内容过滤
   - 时间有效性检查
   - 重复内容检测:
     * 标题和内容相似度检测
     * 多种哈希算法
     * 模糊匹配
   - 垃圾内容过滤:
     * 广告关键词检测
     * 垃圾邮件模式识别
     * URL过滤和域名白名单
   - 字符验证:
     * 允许字符模式检查
     * 无效字符移除
     * 空格标准化

6. 数据增强 (data_enrichment_config):
   - 标签提取: 金融实体、关键词、重要性标签
   - 自动分类: 内容类型、市场影响、紧急度
   - 关联内容: 相关股票、行业、关键词
   - 质量评分: 多因子质量评分

清洗功能特性：
- 基于Python配置文件的完整清洗规则
- 支持金十数据特定格式处理
- 多层次的内容质量控制
- 智能的前缀识别和保护机制
- 全面的垃圾内容和广告过滤
- 可配置的字符集和格式标准化
- 重复内容检测和去重
- 质量评分和可读性分析

使用方法：
1. 执行SQL创建管道配置
2. 系统会自动匹配金十数据使用专用管道
3. 其他快讯数据源使用默认管道
4. 可通过jin10_processing_status_view监控处理状态

注意事项：
- 专用管道优先级最高，确保精确处理
- 包含完整的容错和质量控制机制
- 支持版本管理和配置更新
- 配置基于实际测试结果和Python配置文件优化
- 清洗规则可根据实际数据质量情况调整
*/
