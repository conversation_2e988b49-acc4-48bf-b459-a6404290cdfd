"""
标签分类服务模型 V2 - 简化设计
统一TagType和TagCategory为TagClassification
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional

from sqlalchemy import (
    BigInteger, Boolean, Column, Date, DateTime, ForeignKey, Index, Integer,
    String, Text, TIMESTAMP, func
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy import DECIMAL
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()


class TagClassification(Base):
    """统一标签分类模型 - 简化设计，消除概念重叠"""
    
    __tablename__ = "tag_classifications"

    # 基础标识
    id = Column(BigInteger, primary_key=True, comment="分类ID")
    classification_code = Column(
        String(50), unique=True, nullable=False, comment="分类代码，如finance.stock.ipo"
    )
    classification_name = Column(
        String(100), nullable=False, comment="分类名称"
    )
    classification_type = Column(
        String(20), nullable=False, comment="分类类型：domain/category/type"
    )

    # 层次结构
    parent_id = Column(
        BigInteger, 
        ForeignKey("tag_classifications.id"), 
        nullable=True, 
        comment="父分类ID"
    )
    level = Column(Integer, default=1, comment="层级深度")
    path = Column(
        String(500), 
        comment="分类路径，如finance.stock.ipo"
    )

    # 业务属性
    domain = Column(
        String(50), 
        comment="业务域：finance/technology/general"
    )
    description = Column(Text, comment="分类描述")

    # 显示属性
    icon = Column(String(50), comment="图标名称")
    color = Column(String(7), comment="颜色值")
    sort_order = Column(Integer, default=0, comment="排序权重")

    # 配置规则（简化为基础配置）
    business_rules = Column(
        JSONB, 
        default=dict, 
        comment="业务规则配置"
    )

    # 管理字段
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_system = Column(Boolean, default=False, comment="是否系统分类")
    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP, 
        server_default=func.now(), 
        onupdate=func.now(), 
        comment="更新时间"
    )

    # 关系定义
    parent = relationship(
        "TagClassification", 
        remote_side=[id], 
        backref="children"
    )
    
    # 与标签的关系
    tags = relationship("Tag", back_populates="classification")

    # 索引定义
    __table_args__ = (
        Index("idx_tag_classifications_code", "classification_code"),
        Index("idx_tag_classifications_type", "classification_type"),
        Index("idx_tag_classifications_domain", "domain"),
        Index("idx_tag_classifications_parent", "parent_id"),
        Index("idx_tag_classifications_level", "level"),
        Index("idx_tag_classifications_active", "is_active"),
        Index("idx_tag_classifications_sort", "sort_order"),
    )

    def __repr__(self):
        return f"<TagClassification(id={self.id}, code='{self.classification_code}', type='{self.classification_type}')>"

    def get_full_path(self) -> str:
        """获取完整路径"""
        return self.path or self.classification_code

    def is_domain(self) -> bool:
        """是否为域级分类"""
        return self.classification_type == "domain"

    def is_category(self) -> bool:
        """是否为分类级"""
        return self.classification_type == "category"

    def is_type(self) -> bool:
        """是否为类型级"""
        return self.classification_type == "type"

    def get_ancestors(self) -> list:
        """获取所有祖先分类"""
        ancestors = []
        current = self.parent
        while current:
            ancestors.append(current)
            current = current.parent
        return ancestors

    def get_descendants(self) -> list:
        """获取所有后代分类"""
        descendants = []
        for child in self.children:
            descendants.append(child)
            descendants.extend(child.get_descendants())
        return descendants


# 更新Tag模型以使用新的分类系统
class Tag(Base):
    """标签模型 - 更新为使用统一分类"""
    
    __tablename__ = "tags"

    # 基础标识
    id = Column(BigInteger, primary_key=True, comment="标签ID")
    tag_name = Column(String(100), nullable=False, comment="标签名称")
    tag_code = Column(String(50), unique=True, nullable=False, comment="标签代码")
    tag_slug = Column(String(100), unique=True, nullable=False, comment="标签URL友好名称")

    # 层次结构
    parent_id = Column(BigInteger, ForeignKey("tags.id"), nullable=True, comment="父标签ID")
    level = Column(Integer, default=1, comment="层级深度")
    path = Column(String(500), comment="标签路径")

    # 分类关联 - 使用新的统一分类
    classification_id = Column(
        BigInteger, 
        ForeignKey("tag_classifications.id"), 
        nullable=True, 
        comment="分类ID"
    )

    # 保留原有字段以兼容现有数据（后续可删除）
    tag_type_id = Column(BigInteger, nullable=True, comment="标签类型ID（待废弃）")
    tag_category_id = Column(BigInteger, nullable=True, comment="标签分类ID（待废弃）")

    # 业务属性
    description = Column(Text, comment="标签描述")
    synonyms = Column(Text, comment="同义词，JSON格式")
    lifecycle_stage = Column(
        String(20), 
        default="active", 
        comment="生命周期阶段：draft/active/deprecated"
    )

    # 显示属性
    color = Column(String(7), comment="颜色值")
    icon = Column(String(50), comment="图标名称")

    # 权重系统
    base_weight = Column(DECIMAL(3, 2), default=0.50, comment="基础权重")
    popularity_weight = Column(DECIMAL(3, 2), default=0.00, comment="热度权重")
    quality_weight = Column(DECIMAL(3, 2), default=0.50, comment="质量权重")
    temporal_weight = Column(DECIMAL(3, 2), default=0.00, comment="时效权重")
    computed_weight = Column(DECIMAL(3, 2), default=0.00, comment="综合权重")

    # 统计信息
    usage_count = Column(Integer, default=0, comment="使用次数")
    daily_usage_count = Column(Integer, default=0, comment="日使用次数")
    positive_feedback_count = Column(Integer, default=0, comment="正面反馈数")
    negative_feedback_count = Column(Integer, default=0, comment="负面反馈数")

    # 管理字段
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_system = Column(Boolean, default=False, comment="是否系统标签")
    last_used_at = Column(DateTime, comment="最后使用时间")
    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP, 
        server_default=func.now(), 
        onupdate=func.now(), 
        comment="更新时间"
    )

    # 关系定义
    parent = relationship("Tag", remote_side=[id], backref="children")
    classification = relationship("TagClassification", back_populates="tags")

    # 权重计算方法
    def calculate_computed_weight(self):
        """计算综合权重"""
        from decimal import Decimal
        return (
            (self.base_weight or Decimal('0')) * Decimal('0.4') +
            (self.popularity_weight or Decimal('0')) * Decimal('0.3') +
            (self.quality_weight or Decimal('0')) * Decimal('0.2') +
            (self.temporal_weight or Decimal('0')) * Decimal('0.1')
        )

    def update_computed_weight(self):
        """更新综合权重"""
        self.computed_weight = self.calculate_computed_weight()

    # 索引定义
    __table_args__ = (
        Index("idx_tags_code", "tag_code"),
        Index("idx_tags_slug", "tag_slug"),
        Index("idx_tags_name", "tag_name"),
        Index("idx_tags_classification", "classification_id"),
        Index("idx_tags_parent", "parent_id"),
        Index("idx_tags_level", "level"),
        Index("idx_tags_lifecycle", "lifecycle_stage"),
        Index("idx_tags_active", "is_active"),
        Index("idx_tags_system", "is_system"),
        Index("idx_tags_usage", "usage_count"),
        Index("idx_tags_computed_weight", "computed_weight"),
        Index("idx_tags_last_used", "last_used_at"),
    )

    def __repr__(self):
        return f"<Tag(id={self.id}, code='{self.tag_code}', name='{self.tag_name}')>"
