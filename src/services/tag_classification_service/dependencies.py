"""
标签分类服务依赖注入模块
提供服务实例的依赖注入函数
"""

from typing import Annotated

from fastapi import Depends
from sqlalchemy.orm import Session

from ...core.database import get_db
from .domain import (
    BatchService,
    ClassificationService,
    TagClassificationService,
    TagService,
    UserInterestService,
)


def get_tag_classification_service(
    db: Annotated[Session, Depends(get_db)]
) -> TagClassificationService:
    """
    获取标签分类服务实例（合并了TagType和TagCategory服务）

    Args:
        db: 数据库会话

    Returns:
        标签分类服务实例
    """
    return TagClassificationService(db)


# 为了向后兼容，保留原有的函数名
def get_tag_type_service(db: Annotated[Session, Depends(get_db)]) -> TagClassificationService:
    """获取标签类型服务实例（向后兼容）"""
    return TagClassificationService(db)


def get_tag_category_service(
    db: Annotated[Session, Depends(get_db)]
) -> TagClassificationService:
    """获取标签分类服务实例（向后兼容）"""
    return TagClassificationService(db)


def get_tag_service(db: Annotated[Session, Depends(get_db)]) -> TagService:
    """
    获取标签服务实例

    Args:
        db: 数据库会话

    Returns:
        标签服务实例
    """
    return TagService(db)


def get_user_interest_service(
    db: Annotated[Session, Depends(get_db)]
) -> UserInterestService:
    """
    获取用户兴趣服务实例

    Args:
        db: 数据库会话

    Returns:
        用户兴趣服务实例
    """
    return UserInterestService(db)


def get_classification_service(
    db: Annotated[Session, Depends(get_db)]
) -> ClassificationService:
    """
    获取分类服务实例

    Args:
        db: 数据库会话

    Returns:
        分类服务实例
    """
    return ClassificationService(db)


def get_batch_service(db: Annotated[Session, Depends(get_db)]) -> BatchService:
    """
    获取批量操作服务实例

    Args:
        db: 数据库会话

    Returns:
        批量操作服务实例
    """
    return BatchService(db)
