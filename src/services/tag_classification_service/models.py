"""
标签和分类服务数据模型
定义标签和分类相关的数据库模型
"""

from datetime import datetime, timezone
from typing import List

from sqlalchemy import (ARRAY, DECIMAL, TIME, BigInteger, Boolean,
                        CheckConstraint, Column, Date, DateTime, ForeignKey,
                        Index, Integer, String, Text, TIMESTAMP)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from ...core.database import Base


class TagType(Base):
    """
    标签类型表
    """

    __tablename__ = "tag_types"

    id = Column(
        BigInteger, primary_key=True, index=True, comment="标签类型唯一标识符，自增主键"
    )
    type_code = Column(
        String(20),
        nullable=False,
        unique=True,
        comment="标签类型代码，如general/entity/keyword/concept/topic，用于程序识别",
    )
    type_name = Column(
        String(50),
        nullable=False,
        comment="标签类型名称，如通用标签/实体标签/关键词/概念/主题",
    )
    description = Column(Text, comment="标签类型的详细描述，说明该类型标签的用途和特点")
    icon = Column(String(50), comment="标签类型图标名称，用于前端显示")
    color = Column(String(7), comment="标签类型默认颜色，十六进制颜色值")
    sort_order = Column(Integer, default=0, comment="排序权重，数值越大排序越靠前")
    is_active = Column(
        Boolean, default=True, comment="是否启用该标签类型，false表示已废弃"
    )
    created_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        comment="创建时间，记录标签类型的创建时间",
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        onupdate=func.now(),
        comment="最后更新时间，记录标签类型的最后修改时间",
    )

    # 关系定义
    tags = relationship("Tag", back_populates="tag_type", cascade="all, delete-orphan")


class TagCategory(Base):
    """
    标签分类表
    """

    __tablename__ = "tag_categories"

    id = Column(
        BigInteger, primary_key=True, index=True, comment="标签分类唯一标识符，自增主键"
    )
    category_code = Column(
        String(50),
        nullable=False,
        unique=True,
        comment="分类代码，如industry/sentiment/urgency/region，用于程序识别",
    )
    category_name = Column(
        String(100), nullable=False, comment="分类名称，如行业/情感/紧急度/地区"
    )
    parent_id = Column(
        BigInteger,
        ForeignKey("tag_categories.id"),
        comment="父分类ID，支持分类的层次结构",
    )
    level = Column(
        Integer, default=1, comment="分类层级深度，从1开始，用于控制分类树的层次"
    )
    description = Column(
        Text, comment="分类的详细描述，说明该分类的用途和包含的标签类型"
    )
    icon = Column(String(50), comment="分类图标名称，用于前端显示")
    color = Column(String(7), comment="分类默认颜色，十六进制颜色值")
    sort_order = Column(
        Integer, default=0, comment="同级分类的排序权重，数值越大排序越靠前"
    )
    is_active = Column(Boolean, default=True, comment="是否启用该分类，false表示已废弃")
    created_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        comment="创建时间，记录分类的创建时间",
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        onupdate=func.now(),
        comment="最后更新时间，记录分类的最后修改时间",
    )

    # 关系定义
    parent = relationship("TagCategory", remote_side=[id], backref="children")
    tags = relationship("Tag", back_populates="tag_category")


class Tag(Base):
    """
    标签表（系统核心）
    """

    __tablename__ = "tags"

    # 主键和基础信息
    id = Column(
        BigInteger,
        primary_key=True,
        index=True,
        comment="标签唯一标识符，自增主键，全局唯一",
    )
    tag_name = Column(
        String(100),
        nullable=False,
        comment="标签名称，用户可见的标签显示名称，支持中英文",
    )
    tag_code = Column(
        String(50),
        nullable=False,
        unique=True,
        comment="标签代码，系统内部使用的标签标识符，建议使用英文和下划线",
    )
    tag_slug = Column(
        String(50),
        nullable=False,
        unique=True,
        comment="URL友好标识符，用于生成SEO友好的URL，使用连字符分隔",
    )

    # 层次结构
    parent_id = Column(
        BigInteger,
        ForeignKey("tags.id"),
        comment="父标签ID，建立标签的层次关系，NULL表示顶级标签",
    )
    level = Column(
        Integer, default=1, comment="标签层级深度，从1开始，用于查询和显示层次结构"
    )
    path = Column(String(500), comment="标签路径，如finance.stock.ipo，存储完整路径")

    # 标签属性
    tag_type_id = Column(
        BigInteger,
        ForeignKey("tag_types.id"),
        nullable=False,
        comment="标签类型ID，关联tag_types表，定义标签的基本类型",
    )
    tag_category_id = Column(
        BigInteger,
        ForeignKey("tag_categories.id"),
        comment="标签分类ID，关联tag_categories表，定义标签的业务分类",
    )
    color = Column(
        String(7), comment="标签颜色，十六进制颜色值，用于前端显示，如#FF5722"
    )
    icon = Column(String(50), comment="图标名称，用于前端显示的图标标识符")

    # 动态权重系统
    base_weight = Column(
        DECIMAL(3, 2),
        default=1.00,
        comment="基础权重，取值范围0-1，表示标签的基础重要程度",
    )
    popularity_weight = Column(
        DECIMAL(3, 2), default=0.00, comment="热度权重，基于用户使用频率计算的动态权重"
    )
    quality_weight = Column(
        DECIMAL(3, 2),
        default=0.00,
        comment="质量权重，基于标签准确性和用户反馈计算的权重",
    )
    temporal_weight = Column(
        DECIMAL(3, 2), default=0.00, comment="时效权重，基于标签时间相关性计算的权重"
    )
    computed_weight = Column(
        DECIMAL(3, 2),
        default=0.00,
        comment="综合权重，由其他权重计算得出：base_weight * 0.4 + popularity_weight * 0.3 + quality_weight * 0.2 + temporal_weight * 0.1"
    )

    # 统计信息
    usage_count = Column(
        BigInteger, default=0, comment="总使用次数，标签被使用的累计次数"
    )
    daily_usage_count = Column(
        Integer, default=0, comment="今日使用次数，当天标签被使用的次数，每日重置"
    )
    last_used_at = Column(DateTime, comment="最后使用时间，标签最近一次被使用的时间戳")

    # 用户反馈统计
    positive_feedback_count = Column(
        Integer, default=0, comment="正面反馈数，用户对标签给出的正面评价数量"
    )
    negative_feedback_count = Column(
        Integer, default=0, comment="负面反馈数，用户对标签给出的负面评价数量"
    )

    # 生命周期管理
    lifecycle_stage = Column(
        String(20),
        default="active",
        comment="生命周期阶段：emerging新兴/active活跃/mature成熟/declining衰落/deprecated已废弃",
    )
    auto_retirement_date = Column(
        Date, comment="自动退休日期，标签预计被自动标记为废弃的日期"
    )

    # 语义信息
    description = Column(Text, comment="标签描述，标签的详细说明和使用场景介绍")
    synonyms = Column(ARRAY(Text), comment="同义词数组，与该标签意义相近的词汇列表")

    # 管理字段
    is_active = Column(
        Boolean, default=True, comment="是否启用，false表示标签已被禁用或删除"
    )
    is_system = Column(
        Boolean,
        default=False,
        comment="是否为系统标签，true表示系统预定义的标签，不可删除",
    )
    created_at = Column(
        TIMESTAMP, server_default=func.now(), comment="创建时间，标签的创建时间戳"
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        onupdate=func.now(),
        comment="最后更新时间，标签信息的最后修改时间戳",
    )

    def calculate_computed_weight(self):
        """计算综合权重"""
        from decimal import Decimal
        return (
            (self.base_weight or Decimal('0')) * Decimal('0.4') +
            (self.popularity_weight or Decimal('0')) * Decimal('0.3') +
            (self.quality_weight or Decimal('0')) * Decimal('0.2') +
            (self.temporal_weight or Decimal('0')) * Decimal('0.1')
        )

    def update_computed_weight(self):
        """更新综合权重"""
        self.computed_weight = self.calculate_computed_weight()

    # 关系定义
    tag_type = relationship("TagType", back_populates="tags")
    tag_category = relationship("TagCategory", back_populates="tags")
    parent = relationship("Tag", remote_side=[id], backref="children")

    # 标签关系
    source_relationships = relationship(
        "TagRelationship",
        foreign_keys="TagRelationship.tag_id",
        cascade="all, delete-orphan",
    )
    target_relationships = relationship(
        "TagRelationship",
        foreign_keys="TagRelationship.related_tag_id",
        cascade="all, delete-orphan",
    )

    # 用户兴趣关联
    user_interests = relationship(
        "UserInterestTag", back_populates="tag", cascade="all, delete-orphan"
    )

    # 索引定义
    __table_args__ = (
        Index("idx_tags_parent_id", "parent_id"),
        Index("idx_tags_path", "path"),
        Index(
            "idx_tags_weights",
            "base_weight",
            "popularity_weight",
            "quality_weight",
            "temporal_weight",
        ),
        Index("idx_tags_computed_weight", "computed_weight"),
        CheckConstraint(
            "base_weight >= 0 AND base_weight <= 1", name="chk_base_weight_range"
        ),
        CheckConstraint(
            "popularity_weight >= 0 AND popularity_weight <= 1",
            name="chk_popularity_weight_range",
        ),
        CheckConstraint(
            "quality_weight >= 0 AND quality_weight <= 1",
            name="chk_quality_weight_range",
        ),
        CheckConstraint(
            "temporal_weight >= 0 AND temporal_weight <= 1",
            name="chk_temporal_weight_range",
        ),
    )


class TagRelationship(Base):
    """
    标签关系表
    """

    __tablename__ = "tag_relationships"

    id = Column(
        BigInteger, primary_key=True, index=True, comment="标签关系唯一标识符，自增主键"
    )
    tag_id = Column(
        BigInteger,
        ForeignKey("tags.id"),
        nullable=False,
        comment="源标签ID，关系的起始标签",
    )
    related_tag_id = Column(
        BigInteger,
        ForeignKey("tags.id"),
        nullable=False,
        comment="关联标签ID，关系的目标标签",
    )

    relationship_type = Column(
        String(50),
        nullable=False,
        comment="关系类型：synonym同义词/parent_child父子关系/related相关/exclusive互斥/implies蕴含",
    )
    strength = Column(
        DECIMAL(3, 2),
        default=1.0,
        comment="关系强度，取值范围0-1，表示两个标签之间关系的紧密程度",
    )
    confidence = Column(
        DECIMAL(3, 2),
        default=1.0,
        comment="关系置信度，取值范围0-1，表示对该关系准确性的信心程度",
    )

    source = Column(
        String(50),
        default="manual",
        comment="关系来源：manual人工创建/computed计算生成/ml_inferred机器学习推断",
    )
    created_by = Column(String(100), comment="创建者，记录创建该关系的用户或系统标识")
    usage_count = Column(
        Integer, default=0, comment="关系被使用次数，记录该关系在推荐等场景中的使用频率"
    )

    created_at = Column(
        TIMESTAMP, server_default=func.now(), comment="创建时间，关系建立的时间戳"
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        onupdate=func.now(),
        comment="最后更新时间，关系信息的最后修改时间戳",
    )

    # 关系定义
    source_tag = relationship(
        "Tag", foreign_keys=[tag_id], overlaps="source_relationships"
    )
    related_tag = relationship(
        "Tag", foreign_keys=[related_tag_id], overlaps="target_relationships"
    )

    # 索引和约束
    __table_args__ = (
        Index("idx_tag_relationships_tag", "tag_id", "relationship_type"),
        Index(
            "idx_tag_relationships_related",
            "related_tag_id",
            "relationship_type",
            "strength",
        ),
        CheckConstraint("tag_id != related_tag_id", name="chk_no_self_relationship"),
        CheckConstraint("strength >= 0 AND strength <= 1", name="chk_strength_range"),
        CheckConstraint(
            "confidence >= 0 AND confidence <= 1", name="chk_confidence_range"
        ),
    )


class ClassificationDimension(Base):
    """
    分类维度表
    """

    __tablename__ = "classification_dimensions"

    id = Column(
        BigInteger, primary_key=True, index=True, comment="分类维度唯一标识符，自增主键"
    )
    dimension_name = Column(
        String(50),
        nullable=False,
        unique=True,
        comment="维度名称，如industry行业、topic主题、sentiment情感，用于程序识别",
    )
    display_name = Column(
        String(100), nullable=False, comment="显示名称，用户界面显示的维度名称"
    )
    description = Column(Text, comment="维度描述，详细说明该维度的用途和包含的分类内容")

    is_active = Column(
        Boolean, default=True, comment="是否启用，false表示该维度已废弃不再使用"
    )
    sort_order = Column(
        Integer, default=0, comment="排序顺序，数值越大在界面上排序越靠前"
    )
    created_at = Column(
        TIMESTAMP, server_default=func.now(), comment="创建时间，维度创建的时间戳"
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        onupdate=func.now(),
        comment="最后更新时间，维度信息的最后修改时间戳",
    )

    # 关系定义
    values = relationship(
        "ClassificationValue", back_populates="dimension", cascade="all, delete-orphan"
    )
    user_preferences = relationship(
        "UserClassificationPreference",
        back_populates="dimension",
        cascade="all, delete-orphan",
    )


class ClassificationValue(Base):
    """
    分类值表
    """

    __tablename__ = "classification_values"

    id = Column(
        BigInteger, primary_key=True, index=True, comment="分类值唯一标识符，自增主键"
    )
    dimension_id = Column(
        BigInteger,
        ForeignKey("classification_dimensions.id"),
        nullable=False,
        comment="所属维度ID，关联classification_dimensions表",
    )
    value_code = Column(
        String(50),
        nullable=False,
        comment="分类值代码，程序内部使用的标识符，建议使用英文",
    )
    display_name = Column(
        String(100), nullable=False, comment="显示名称，用户界面显示的分类值名称"
    )
    description = Column(Text, comment="分类值描述，详细说明该分类值的含义和适用场景")

    parent_id = Column(
        BigInteger,
        ForeignKey("classification_values.id"),
        comment="父分类ID，支持分类值的层次结构，NULL表示顶级分类",
    )
    level = Column(
        Integer, default=1, comment="层级深度，从1开始，用于控制分类的层次显示"
    )

    sort_order = Column(
        Integer, default=0, comment="排序顺序，同级分类值的显示顺序，数值越大排序越靠前"
    )
    is_active = Column(
        Boolean, default=True, comment="是否启用，false表示该分类值已废弃不再使用"
    )
    created_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        comment="创建时间，分类值创建的时间戳",
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        onupdate=func.now(),
        comment="最后更新时间，分类值信息的最后修改时间戳",
    )

    # 关系定义
    dimension = relationship("ClassificationDimension", back_populates="values")
    parent = relationship("ClassificationValue", remote_side=[id], backref="children")
    user_preferences = relationship(
        "UserClassificationPreference",
        back_populates="value",
        cascade="all, delete-orphan",
    )

    # 索引和约束
    __table_args__ = (
        Index("idx_classification_values_dimension", "dimension_id", "value_code"),
        CheckConstraint("dimension_id IS NOT NULL", name="chk_dimension_id_not_null"),
    )


class UserInterestTag(Base):
    """
    用户兴趣标签表
    """

    __tablename__ = "user_interest_tags"

    id = Column(
        BigInteger,
        primary_key=True,
        index=True,
        comment="用户兴趣标签唯一标识符，自增主键",
    )
    user_id = Column(BigInteger, nullable=False, comment="用户ID，关联用户表的主键")
    tag_id = Column(
        BigInteger,
        ForeignKey("tags.id"),
        nullable=False,
        comment="标签ID，关联tags表，表示用户感兴趣的标签",
    )

    # 多维度兴趣强度
    explicit_interest = Column(
        DECIMAL(3, 2),
        default=0.0,
        comment="显式兴趣强度，用户主动表达的兴趣程度，取值范围0-1",
    )
    implicit_interest = Column(
        DECIMAL(3, 2),
        default=0.0,
        comment="隐式兴趣强度，根据用户行为分析得出的兴趣程度，取值范围0-1",
    )

    # 时间衰减
    last_reinforced_at = Column(
        DateTime,
        default=func.current_timestamp(),
        comment="最后增强时间，该兴趣最近一次被强化的时间戳",
    )
    reinforcement_count = Column(
        Integer, default=1, comment="增强次数，该兴趣被强化的总次数"
    )
    daily_decay_rate = Column(
        DECIMAL(5, 4), default=0.9990, comment="日衰减率，兴趣强度每日衰减的比率"
    )

    # 行为统计
    click_count = Column(
        Integer, default=0, comment="点击次数，用户点击相关内容的累计次数"
    )
    view_time_seconds = Column(
        BigInteger, default=0, comment="浏览时长，用户浏览相关内容的累计秒数"
    )
    share_count = Column(
        Integer, default=0, comment="分享次数，用户分享相关内容的累计次数"
    )

    source = Column(
        String(50),
        default="behavior",
        comment="兴趣来源：behavior行为分析/survey问卷调查/manual手动设置",
    )
    confidence = Column(
        DECIMAL(3, 2),
        default=0.5,
        comment="置信度，对该兴趣准确性的信心程度，取值范围0-1",
    )

    created_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        comment="创建时间，兴趣记录创建的时间戳",
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        onupdate=func.now(),
        comment="最后更新时间，兴趣信息最后修改的时间戳",
    )

    # 关系定义
    tag = relationship("Tag", back_populates="user_interests")

    # 索引和约束
    __table_args__ = (
        Index(
            "idx_user_interest_tags_user_computed",
            "user_id",
            "explicit_interest",
            "implicit_interest",
        ),
        CheckConstraint(
            "explicit_interest >= 0 AND explicit_interest <= 1",
            name="chk_explicit_interest_range",
        ),
        CheckConstraint(
            "implicit_interest >= 0 AND implicit_interest <= 1",
            name="chk_implicit_interest_range",
        ),
        CheckConstraint(
            "confidence >= 0 AND confidence <= 1", name="chk_confidence_range"
        ),
        CheckConstraint(
            "daily_decay_rate >= 0 AND daily_decay_rate <= 1",
            name="chk_daily_decay_rate_range",
        ),
    )


class UserClassificationPreference(Base):
    """
    用户分类偏好表
    """

    __tablename__ = "user_classification_preferences"

    id = Column(
        BigInteger,
        primary_key=True,
        index=True,
        comment="用户分类偏好唯一标识符，自增主键",
    )
    user_id = Column(BigInteger, nullable=False, comment="用户ID，关联用户表的主键")
    dimension_id = Column(
        BigInteger,
        ForeignKey("classification_dimensions.id"),
        nullable=False,
        comment="分类维度ID，关联classification_dimensions表",
    )
    value_id = Column(
        BigInteger,
        ForeignKey("classification_values.id"),
        nullable=False,
        comment="分类值ID，关联classification_values表",
    )

    preference_score = Column(
        DECIMAL(3, 2),
        default=0.5,
        comment="偏好分数，用户对该分类的偏好程度，取值范围0-1",
    )
    source = Column(
        String(50),
        default="behavior",
        comment="偏好来源：behavior行为分析/survey问卷调查/manual手动设置",
    )
    last_updated = Column(
        DateTime,
        default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="最后更新时间，偏好信息最后修改的时间戳",
    )

    # 关系定义
    dimension = relationship(
        "ClassificationDimension", back_populates="user_preferences"
    )
    value = relationship("ClassificationValue", back_populates="user_preferences")

    # 索引和约束
    __table_args__ = (
        Index(
            "idx_user_classification_preferences_user",
            "user_id",
            "dimension_id",
            "value_id",
        ),
        CheckConstraint(
            "preference_score >= 0 AND preference_score <= 1",
            name="chk_preference_score_range",
        ),
    )


class UserProfileSnapshot(Base):
    """
    用户画像快照表
    """

    __tablename__ = "user_profile_snapshots"

    id = Column(
        BigInteger,
        primary_key=True,
        index=True,
        comment="用户画像快照唯一标识符，自增主键",
    )
    user_id = Column(BigInteger, nullable=False, comment="用户ID，关联用户表的主键")

    snapshot_date = Column(
        Date,
        nullable=False,
        default=func.current_date(),
        comment="快照日期，用户画像数据的生成日期",
    )
    top_interests = Column(
        JSONB,
        nullable=False,
        comment="Top 20兴趣标签及权重，存储用户最感兴趣的标签列表和对应权重",
    )
    interest_categories = Column(
        JSONB,
        nullable=False,
        comment="各分类兴趣分布，存储用户在不同分类维度上的兴趣分布情况",
    )
    behavioral_patterns = Column(
        JSONB, comment="行为模式分析，存储用户的行为特征和模式分析结果"
    )

    recommendation_weights = Column(
        JSONB, default=lambda: {}, comment="推荐权重配置，存储个性化推荐算法的权重参数"
    )
    content_filters = Column(
        JSONB, default=lambda: {}, comment="内容过滤器配置，存储用户的内容过滤偏好设置"
    )

    created_at = Column(
        TIMESTAMP, server_default=func.now(), comment="创建时间，快照生成的时间戳"
    )

    # 索引和约束
    __table_args__ = (
        Index("idx_user_profile_snapshots_user_date", "user_id", "snapshot_date"),
    )
