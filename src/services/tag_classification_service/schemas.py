"""
标签和分类服务数据传输对象(DTO)
定义API请求和响应的数据格式
"""

from datetime import date, datetime, timezone
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, field_validator


class LifecycleStage(str, Enum):
    """标签生命周期阶段枚举"""

    EMERGING = "emerging"  # 新兴
    ACTIVE = "active"  # 活跃
    MATURE = "mature"  # 成熟
    DECLINING = "declining"  # 衰落
    DEPRECATED = "deprecated"  # 已废弃


class RelationshipType(str, Enum):
    """标签关系类型枚举"""

    SYNONYM = "synonym"  # 同义词
    PARENT_CHILD = "parent_child"  # 父子关系
    RELATED = "related"  # 相关
    EXCLUSIVE = "exclusive"  # 互斥
    IMPLIES = "implies"  # 蕴含


class InterestSource(str, Enum):
    """兴趣来源枚举"""

    BEHAVIOR = "behavior"  # 行为分析
    SURVEY = "survey"  # 问卷调查
    MANUAL = "manual"  # 手动设置


# ==================== 标签类型相关 ====================


class TagTypeCreate(BaseModel):
    """标签类型创建请求"""

    type_code: str = Field(..., description="标签类型代码", max_length=20)
    type_name: str = Field(..., description="标签类型名称", max_length=50)
    description: Optional[str] = Field(None, description="类型描述")
    icon: Optional[str] = Field(None, description="图标名称", max_length=50)
    color: Optional[str] = Field(
        None, description="颜色值", pattern=r"^#[0-9A-Fa-f]{6}$"
    )
    sort_order: int = Field(default=0, description="排序权重")

    @field_validator("type_code")
    def validate_type_code(cls, v):
        """验证类型代码格式"""
        if not v.replace("_", "").isalnum():
            raise ValueError(
                "Type code can only contain letters, numbers and underscores"
            )
        return v.lower()


class TagTypeUpdate(BaseModel):
    """标签类型更新请求"""

    type_name: Optional[str] = Field(None, description="标签类型名称", max_length=50)
    description: Optional[str] = Field(None, description="类型描述")
    icon: Optional[str] = Field(None, description="图标名称", max_length=50)
    color: Optional[str] = Field(
        None, description="颜色值", pattern=r"^#[0-9A-Fa-f]{6}$"
    )
    sort_order: Optional[int] = Field(None, description="排序权重")
    is_active: Optional[bool] = Field(None, description="是否启用")


class TagTypeResponse(BaseModel):
    """标签类型响应"""

    id: int
    type_code: str
    type_name: str
    description: Optional[str]
    icon: Optional[str]
    color: Optional[str]
    sort_order: int
    is_active: bool
    created_at: datetime
    updated_at: datetime

    @field_validator("created_at", "updated_at", mode="before")
    @classmethod
    def validate_timestamps(cls, v):
        """验证时间戳字段，如果为None则设置为当前时间"""
        if v is None:
            return datetime.now(timezone.utc)
        return v

    class Config:
        from_attributes = True


# ==================== 标签分类相关 ====================


class TagCategoryCreate(BaseModel):
    """标签分类创建请求"""

    category_code: str = Field(..., description="分类代码", max_length=50)
    category_name: str = Field(..., description="分类名称", max_length=100)
    parent_id: Optional[int] = Field(None, description="父分类ID")
    description: Optional[str] = Field(None, description="分类描述")
    icon: Optional[str] = Field(None, description="图标名称", max_length=50)
    color: Optional[str] = Field(
        None, description="颜色值", pattern=r"^#[0-9A-Fa-f]{6}$"
    )
    sort_order: int = Field(default=0, description="排序权重")

    @field_validator("category_code")
    def validate_category_code(cls, v):
        """验证分类代码格式"""
        if not v.replace("_", "").isalnum():
            raise ValueError(
                "Category code can only contain letters, numbers and underscores"
            )
        return v.lower()


class TagCategoryUpdate(BaseModel):
    """标签分类更新请求"""

    category_name: Optional[str] = Field(None, description="分类名称", max_length=100)
    parent_id: Optional[int] = Field(None, description="父分类ID")
    description: Optional[str] = Field(None, description="分类描述")
    icon: Optional[str] = Field(None, description="图标名称", max_length=50)
    color: Optional[str] = Field(
        None, description="颜色值", pattern=r"^#[0-9A-Fa-f]{6}$"
    )
    sort_order: Optional[int] = Field(None, description="排序权重")
    is_active: Optional[bool] = Field(None, description="是否启用")


class TagCategoryResponse(BaseModel):
    """标签分类响应"""

    id: int
    category_code: str
    category_name: str
    parent_id: Optional[int]
    level: int
    description: Optional[str]
    icon: Optional[str]
    color: Optional[str]
    sort_order: int
    is_active: bool
    created_at: datetime
    updated_at: datetime
    children: Optional[List["TagCategoryResponse"]] = None

    @field_validator("created_at", "updated_at", mode="before")
    @classmethod
    def validate_timestamps(cls, v):
        """验证时间戳字段，如果为None则设置为当前时间"""
        if v is None:
            return datetime.now(timezone.utc)
        return v

    class Config:
        from_attributes = True


# ==================== 标签相关 ====================


class TagCreate(BaseModel):
    """标签创建请求"""

    tag_name: str = Field(..., description="标签名称", max_length=100)
    tag_code: str = Field(..., description="标签代码", max_length=50)
    tag_slug: str = Field(..., description="URL友好标识符", max_length=50)
    tag_type_id: int = Field(..., description="标签类型ID")
    tag_category_id: Optional[int] = Field(None, description="标签分类ID")
    parent_id: Optional[int] = Field(None, description="父标签ID")
    description: Optional[str] = Field(None, description="标签描述")
    color: Optional[str] = Field(
        None, description="标签颜色", pattern=r"^#[0-9A-Fa-f]{6}$"
    )
    icon: Optional[str] = Field(None, description="图标名称", max_length=50)
    synonyms: Optional[List[str]] = Field(default=[], description="同义词列表")
    base_weight: float = Field(default=1.0, description="基础权重", ge=0, le=1)
    lifecycle_stage: LifecycleStage = Field(
        default=LifecycleStage.ACTIVE, description="生命周期阶段"
    )

    @field_validator("tag_code")
    def validate_tag_code(cls, v):
        """验证标签代码格式"""
        if not v.replace("_", "").isalnum():
            raise ValueError(
                "Tag code can only contain letters, numbers and underscores"
            )
        return v.lower()

    @field_validator("tag_slug")
    def validate_tag_slug(cls, v):
        """验证URL标识符格式"""
        if not v.replace("-", "").isalnum():
            raise ValueError("Tag slug can only contain letters, numbers and hyphens")
        return v.lower()


class TagUpdate(BaseModel):
    """标签更新请求"""

    tag_name: Optional[str] = Field(None, description="标签名称", max_length=100)
    tag_category_id: Optional[int] = Field(None, description="标签分类ID")
    parent_id: Optional[int] = Field(None, description="父标签ID")
    description: Optional[str] = Field(None, description="标签描述")
    color: Optional[str] = Field(
        None, description="标签颜色", pattern=r"^#[0-9A-Fa-f]{6}$"
    )
    icon: Optional[str] = Field(None, description="图标名称", max_length=50)
    synonyms: Optional[List[str]] = Field(None, description="同义词列表")
    base_weight: Optional[float] = Field(None, description="基础权重", ge=0, le=1)
    lifecycle_stage: Optional[LifecycleStage] = Field(None, description="生命周期阶段")
    is_active: Optional[bool] = Field(None, description="是否启用")


class TagResponse(BaseModel):
    """标签响应"""

    id: int
    tag_name: str
    tag_code: str
    tag_slug: str
    parent_id: Optional[int]
    level: int
    path: Optional[str]
    tag_type_id: int
    tag_category_id: Optional[int]
    color: Optional[str]
    icon: Optional[str]
    base_weight: Decimal
    popularity_weight: Decimal
    quality_weight: Decimal
    temporal_weight: Decimal
    usage_count: int
    daily_usage_count: int
    last_used_at: Optional[datetime]
    positive_feedback_count: int
    negative_feedback_count: int
    lifecycle_stage: str
    auto_retirement_date: Optional[date]
    description: Optional[str]
    synonyms: Optional[List[str]]
    is_active: bool
    is_system: bool
    created_at: datetime
    updated_at: datetime
    # 关联对象
    tag_type: Optional[TagTypeResponse] = None
    tag_category: Optional[TagCategoryResponse] = None
    children: Optional[List["TagResponse"]] = None

    @field_validator("created_at", "updated_at", mode="before")
    @classmethod
    def validate_timestamps(cls, v):
        """验证时间戳字段，如果为None则设置为当前时间"""
        if v is None:
            return datetime.now(timezone.utc)
        return v

    @field_validator("quality_weight", "temporal_weight", mode="before")
    @classmethod
    def validate_decimal_fields(cls, v):
        """验证Decimal字段，如果为None则设置为0.00"""
        if v is None:
            return Decimal("0.00")
        return v

    @field_validator("daily_usage_count", "positive_feedback_count", "negative_feedback_count", mode="before")
    @classmethod
    def validate_integer_fields(cls, v):
        """验证整数字段，如果为None则设置为0"""
        if v is None:
            return 0
        return v

    @field_validator("lifecycle_stage", mode="before")
    @classmethod
    def validate_lifecycle_stage(cls, v):
        """验证生命周期阶段字段，如果为None则设置为active"""
        if v is None:
            return "active"
        return v

    @field_validator("is_active", "is_system", mode="before")
    @classmethod
    def validate_boolean_fields(cls, v):
        """验证布尔字段，如果为None则设置为False"""
        if v is None:
            return False
        return v

    class Config:
        from_attributes = True


class TagListQuery(BaseModel):
    """标签列表查询参数"""

    page: int = Field(default=1, description="页码", ge=1)
    size: int = Field(default=20, description="每页数量", ge=1, le=100)
    search: Optional[str] = Field(None, description="搜索关键词")
    tag_type_id: Optional[int] = Field(None, description="标签类型ID")
    tag_category_id: Optional[int] = Field(None, description="标签分类ID")
    parent_id: Optional[int] = Field(None, description="父标签ID")
    lifecycle_stage: Optional[LifecycleStage] = Field(None, description="生命周期阶段")
    is_active: Optional[bool] = Field(None, description="是否启用")
    order_by: str = Field(default="created_at", description="排序字段")
    order_direction: str = Field(
        default="desc", description="排序方向", pattern="^(asc|desc)$"
    )


# ==================== 通用响应模型 ====================


class PageResponse(BaseModel):
    """分页响应模型"""

    items: List[Any]
    total: int
    page: int
    size: int
    pages: int


class MessageResponse(BaseModel):
    """消息响应模型"""

    message: str
    success: bool = True


# 更新前向引用
TagCategoryResponse.model_rebuild()
TagResponse.model_rebuild()


# ==================== 分类维度相关 ====================


class ClassificationDimensionCreate(BaseModel):
    """分类维度创建请求"""

    dimension_name: str = Field(..., description="维度名称", max_length=50)
    display_name: str = Field(..., description="显示名称", max_length=100)
    description: Optional[str] = Field(None, description="维度描述")
    sort_order: int = Field(default=0, description="排序顺序")

    @field_validator("dimension_name")
    def validate_dimension_name(cls, v):
        """验证维度名称格式"""
        if not v.replace("_", "").isalnum():
            raise ValueError(
                "Dimension name can only contain letters, numbers and underscores"
            )
        return v.lower()


class ClassificationDimensionUpdate(BaseModel):
    """分类维度更新请求"""

    display_name: Optional[str] = Field(None, description="显示名称", max_length=100)
    description: Optional[str] = Field(None, description="维度描述")
    sort_order: Optional[int] = Field(None, description="排序顺序")
    is_active: Optional[bool] = Field(None, description="是否启用")


class ClassificationDimensionResponse(BaseModel):
    """分类维度响应"""

    id: int
    dimension_name: str
    display_name: str
    description: Optional[str]
    is_active: bool
    sort_order: int
    created_at: datetime
    updated_at: datetime
    values: Optional[List["ClassificationValueResponse"]] = None

    @field_validator("created_at", "updated_at", mode="before")
    @classmethod
    def validate_timestamps(cls, v):
        """验证时间戳字段，如果为None则设置为当前时间"""
        if v is None:
            return datetime.now(timezone.utc)
        return v

    class Config:
        from_attributes = True


# ==================== 分类值相关 ====================


class ClassificationValueCreate(BaseModel):
    """分类值创建请求"""

    dimension_id: int = Field(..., description="所属维度ID")
    value_code: str = Field(..., description="分类值代码", max_length=50)
    display_name: str = Field(..., description="显示名称", max_length=100)
    description: Optional[str] = Field(None, description="分类值描述")
    parent_id: Optional[int] = Field(None, description="父分类ID")
    sort_order: int = Field(default=0, description="排序顺序")

    @field_validator("value_code")
    def validate_value_code(cls, v):
        """验证分类值代码格式"""
        if not v.replace("_", "").isalnum():
            raise ValueError(
                "Value code can only contain letters, numbers and underscores"
            )
        return v.lower()


class ClassificationValueUpdate(BaseModel):
    """分类值更新请求"""

    display_name: Optional[str] = Field(None, description="显示名称", max_length=100)
    description: Optional[str] = Field(None, description="分类值描述")
    parent_id: Optional[int] = Field(None, description="父分类ID")
    sort_order: Optional[int] = Field(None, description="排序顺序")
    is_active: Optional[bool] = Field(None, description="是否启用")


class ClassificationValueResponse(BaseModel):
    """分类值响应"""

    id: int
    dimension_id: int
    value_code: str
    display_name: str
    description: Optional[str]
    parent_id: Optional[int]
    level: int
    sort_order: int
    is_active: bool
    created_at: datetime
    updated_at: datetime
    children: Optional[List["ClassificationValueResponse"]] = None

    @field_validator("created_at", "updated_at", mode="before")
    @classmethod
    def validate_timestamps(cls, v):
        """验证时间戳字段，如果为None则设置为当前时间"""
        if v is None:
            return datetime.now(timezone.utc)
        return v

    class Config:
        from_attributes = True


# ==================== 用户分类偏好相关 ====================


class UserClassificationPreferenceCreate(BaseModel):
    """用户分类偏好创建请求"""

    dimension_id: int = Field(..., description="分类维度ID")
    value_id: int = Field(..., description="分类值ID")
    preference_score: float = Field(default=0.5, description="偏好分数", ge=0, le=1)
    source: str = Field(default="behavior", description="偏好来源")


class UserClassificationPreferenceCreateInternal(BaseModel):
    """用户分类偏好创建请求(内部使用，包含user_id)"""

    user_id: int = Field(..., description="用户ID")
    dimension_id: int = Field(..., description="分类维度ID")
    value_id: int = Field(..., description="分类值ID")
    preference_score: float = Field(default=0.5, description="偏好分数", ge=0, le=1)
    source: str = Field(default="behavior", description="偏好来源")


class UserClassificationPreferenceUpdate(BaseModel):
    """用户分类偏好更新请求"""

    preference_score: Optional[float] = Field(None, description="偏好分数", ge=0, le=1)
    source: Optional[str] = Field(None, description="偏好来源")


class UserClassificationPreferenceResponse(BaseModel):
    """用户分类偏好响应"""

    id: int
    user_id: int
    dimension_id: int
    value_id: int
    preference_score: Decimal
    source: str
    last_updated: datetime
    dimension: Optional[ClassificationDimensionResponse] = None
    value: Optional[ClassificationValueResponse] = None

    @field_validator("last_updated", mode="before")
    @classmethod
    def validate_timestamps(cls, v):
        """验证时间戳字段，如果为None则设置为当前时间"""
        if v is None:
            return datetime.now(timezone.utc)
        return v

    class Config:
        from_attributes = True


# ==================== 分类查询参数 ====================


class ClassificationListQuery(BaseModel):
    """分类列表查询参数"""

    page: int = Field(default=1, description="页码", ge=1)
    size: int = Field(default=20, description="每页数量", ge=1, le=100)
    is_active: Optional[bool] = Field(None, description="是否只获取活跃的分类")
    order_by: str = Field(default="sort_order", description="排序字段")
    order_direction: str = Field(
        default="desc", description="排序方向", pattern="^(asc|desc)$"
    )


# ==================== AI标签匹配相关 ====================

class BusinessDataType(str, Enum):
    """业务数据类型枚举"""
    FLASH_NEWS = "flash_news"
    NEWS_ARTICLE = "news_article"
    RESEARCH_REPORT = "research_report"
    ECONOMIC_DATA = "economic_data"
    COMPANY_ANNOUNCEMENT = "company_announcement"
    SOCIAL_SENTIMENT = "social_sentiment"


class AITagMatchCreate(BaseModel):
    """AI标签匹配创建请求"""
    ai_tag_text: str = Field(..., description="AI生成的标签文本", max_length=100)
    standard_tag_id: Optional[int] = Field(None, description="匹配的标准标签ID")
    confidence_score: Decimal = Field(..., description="匹配置信度", ge=0, le=1)
    match_method: str = Field(..., description="匹配方法")
    similarity_score: Optional[Decimal] = Field(None, description="相似度分数", ge=0, le=1)


class AITagMatchResponse(BaseModel):
    """AI标签匹配响应"""
    id: int = Field(..., description="匹配记录ID")
    ai_tag_text: str = Field(..., description="AI标签文本")
    standard_tag_id: Optional[int] = Field(None, description="标准标签ID")
    confidence_score: Decimal = Field(..., description="置信度")
    match_method: str = Field(..., description="匹配方法")
    similarity_score: Optional[Decimal] = Field(None, description="相似度")
    is_verified: bool = Field(..., description="是否已验证")
    usage_count: int = Field(..., description="使用次数")
    created_at: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True


class TagMatchResult(BaseModel):
    """标签匹配结果"""
    ai_tag: str = Field(..., description="AI标签")
    standard_tag: 'TagResponse' = Field(..., description="匹配的标准标签")
    confidence_score: Decimal = Field(..., description="置信度")
    match_method: str = Field(..., description="匹配方法")
    similarity_score: Decimal = Field(..., description="相似度")


class AITagMatchRequest(BaseModel):
    """AI标签匹配请求"""
    ai_tags: List[str] = Field(..., description="AI生成的标签列表")
    content_id: Optional[int] = Field(None, description="内容ID")
    content_type: Optional[BusinessDataType] = Field(None, description="内容类型")


# ==================== 统一内容标签关联相关 ====================

class UnifiedContentTagCreate(BaseModel):
    """统一内容标签关联创建请求"""
    content_type: BusinessDataType = Field(..., description="内容类型")
    content_id: int = Field(..., description="内容ID")
    tag_id: int = Field(..., description="标签ID")
    relevance_score: Decimal = Field(default=1.0, description="相关性评分", ge=0, le=1)
    confidence_score: Decimal = Field(default=1.0, description="置信度评分", ge=0, le=1)
    importance_score: Decimal = Field(default=0.5, description="重要性评分", ge=0, le=1)
    source: str = Field(default="ai", description="标签来源")
    context: Optional[str] = Field(None, description="标签上下文")


class UnifiedContentTagResponse(BaseModel):
    """统一内容标签关联响应"""
    id: int = Field(..., description="关联ID")
    content_type: BusinessDataType = Field(..., description="内容类型")
    content_id: int = Field(..., description="内容ID")
    tag_id: int = Field(..., description="标签ID")
    tag: Optional['TagResponse'] = Field(None, description="标签信息")
    relevance_score: Decimal = Field(..., description="相关性评分")
    confidence_score: Decimal = Field(..., description="置信度评分")
    importance_score: Decimal = Field(..., description="重要性评分")
    source: str = Field(..., description="标签来源")
    is_verified: bool = Field(..., description="是否已验证")
    created_at: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True


# ==================== 用户行为记录相关 ====================

class UserBehaviorLogCreate(BaseModel):
    """用户行为记录创建请求"""
    user_id: int = Field(..., description="用户ID")
    content_type: BusinessDataType = Field(..., description="内容类型")
    content_id: int = Field(..., description="内容ID")
    action_type: str = Field(..., description="行为类型")
    duration_seconds: Optional[int] = Field(None, description="停留时长")
    scroll_percentage: Optional[Decimal] = Field(None, description="滚动百分比", ge=0, le=1)
    channel: Optional[str] = Field(None, description="访问渠道")
    device_type: Optional[str] = Field(None, description="设备类型")


class UserBehaviorLogResponse(BaseModel):
    """用户行为记录响应"""
    id: int = Field(..., description="行为记录ID")
    user_id: int = Field(..., description="用户ID")
    content_type: BusinessDataType = Field(..., description="内容类型")
    content_id: int = Field(..., description="内容ID")
    action_type: str = Field(..., description="行为类型")
    duration_seconds: Optional[int] = Field(None, description="停留时长")
    channel: Optional[str] = Field(None, description="访问渠道")
    created_at: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True


# 更新前向引用
ClassificationDimensionResponse.model_rebuild()
ClassificationValueResponse.model_rebuild()
TagMatchResult.model_rebuild()
UnifiedContentTagResponse.model_rebuild()
