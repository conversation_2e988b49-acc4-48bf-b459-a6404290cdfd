"""
数据迁移脚本：将TagType和TagCategory合并为TagClassification
"""

import logging
from datetime import datetime
from typing import Dict, List

from sqlalchemy import text
from sqlalchemy.orm import Session

from ..models import Tag, TagCategory, TagType
from ..models_v2 import TagClassification


class UnifiedClassificationMigrator:
    """统一分类迁移器"""

    def __init__(self, db: Session):
        self.db = db
        self.migration_log = []

    def log_migration(self, message: str, level: str = "INFO"):
        """记录迁移日志"""
        log_entry = f"[{datetime.now()}] {level}: {message}"
        self.migration_log.append(log_entry)
        if level == "ERROR":
            logging.error(message)
        elif level == "WARNING":
            logging.warning(message)
        else:
            logging.info(message)

    def create_tag_classifications_table(self) -> bool:
        """创建新的统一分类表"""
        try:
            # 创建表的SQL
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS tag_classifications (
                id BIGSERIAL PRIMARY KEY,
                classification_code VARCHAR(50) NOT NULL UNIQUE,
                classification_name VARCHAR(100) NOT NULL,
                classification_type VARCHAR(20) NOT NULL,
                parent_id BIGINT REFERENCES tag_classifications(id),
                level INTEGER DEFAULT 1,
                path VARCHAR(500),
                domain VARCHAR(50),
                description TEXT,
                icon VARCHAR(50),
                color VARCHAR(7),
                sort_order INTEGER DEFAULT 0,
                business_rules JSONB DEFAULT '{}',
                is_active BOOLEAN DEFAULT TRUE,
                is_system BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            );
            """

            # 创建索引的SQL
            create_indexes_sql = [
                "CREATE INDEX IF NOT EXISTS idx_tag_classifications_code ON tag_classifications(classification_code);",
                "CREATE INDEX IF NOT EXISTS idx_tag_classifications_type ON tag_classifications(classification_type);",
                "CREATE INDEX IF NOT EXISTS idx_tag_classifications_domain ON tag_classifications(domain);",
                "CREATE INDEX IF NOT EXISTS idx_tag_classifications_parent ON tag_classifications(parent_id);",
                "CREATE INDEX IF NOT EXISTS idx_tag_classifications_level ON tag_classifications(level);",
                "CREATE INDEX IF NOT EXISTS idx_tag_classifications_active ON tag_classifications(is_active);",
                "CREATE INDEX IF NOT EXISTS idx_tag_classifications_sort ON tag_classifications(sort_order);",
            ]

            # 执行创建表
            self.db.execute(text(create_table_sql))
            
            # 执行创建索引
            for index_sql in create_indexes_sql:
                self.db.execute(text(index_sql))

            self.db.commit()
            self.log_migration("Created tag_classifications table and indexes")
            return True

        except Exception as e:
            self.db.rollback()
            self.log_migration(f"Failed to create tag_classifications table: {e}", "ERROR")
            return False

    def migrate_tag_types(self) -> Dict[int, int]:
        """迁移TagType数据到TagClassification"""
        type_id_mapping = {}
        
        try:
            # 获取所有TagType数据
            tag_types = self.db.query(TagType).all()
            self.log_migration(f"Found {len(tag_types)} tag types to migrate")

            for tag_type in tag_types:
                # 创建对应的TagClassification记录
                classification_code = f"general.{tag_type.type_code}"
                
                classification = TagClassification(
                    classification_code=classification_code,
                    classification_name=tag_type.type_name,
                    classification_type="type",
                    domain="general",
                    description=tag_type.description,
                    icon=tag_type.icon,
                    color=tag_type.color,
                    sort_order=tag_type.sort_order,
                    is_active=tag_type.is_active,
                    is_system=True,  # TagType都标记为系统分类
                    level=1,
                    path=classification_code
                )

                self.db.add(classification)
                self.db.flush()  # 获取ID

                type_id_mapping[tag_type.id] = classification.id
                self.log_migration(f"Migrated TagType {tag_type.type_code} -> {classification_code}")

            self.db.commit()
            self.log_migration(f"Successfully migrated {len(type_id_mapping)} tag types")
            return type_id_mapping

        except Exception as e:
            self.db.rollback()
            self.log_migration(f"Failed to migrate tag types: {e}", "ERROR")
            return {}

    def migrate_tag_categories(self) -> Dict[int, int]:
        """迁移TagCategory数据到TagClassification"""
        category_id_mapping = {}
        
        try:
            # 按层级顺序获取TagCategory数据
            tag_categories = (
                self.db.query(TagCategory)
                .order_by(TagCategory.level, TagCategory.sort_order)
                .all()
            )
            self.log_migration(f"Found {len(tag_categories)} tag categories to migrate")

            for tag_category in tag_categories:
                # 确定父分类ID
                parent_classification_id = None
                if tag_category.parent_id:
                    parent_classification_id = category_id_mapping.get(tag_category.parent_id)

                # 确定业务域
                domain = self._determine_domain(tag_category.category_code)
                
                # 计算层级和路径
                level = tag_category.level
                path = tag_category.category_code

                classification = TagClassification(
                    classification_code=tag_category.category_code,
                    classification_name=tag_category.category_name,
                    classification_type="category",
                    parent_id=parent_classification_id,
                    level=level,
                    path=path,
                    domain=domain,
                    description=tag_category.description,
                    icon=tag_category.icon,
                    color=tag_category.color,
                    sort_order=tag_category.sort_order,
                    is_active=tag_category.is_active,
                    is_system=False
                )

                self.db.add(classification)
                self.db.flush()  # 获取ID

                category_id_mapping[tag_category.id] = classification.id
                self.log_migration(f"Migrated TagCategory {tag_category.category_code}")

            self.db.commit()
            self.log_migration(f"Successfully migrated {len(category_id_mapping)} tag categories")
            return category_id_mapping

        except Exception as e:
            self.db.rollback()
            self.log_migration(f"Failed to migrate tag categories: {e}", "ERROR")
            return {}

    def _determine_domain(self, category_code: str) -> str:
        """根据分类代码确定业务域"""
        if category_code.startswith('finance'):
            return 'finance'
        elif category_code.startswith('tech'):
            return 'technology'
        elif category_code.startswith('general'):
            return 'general'
        else:
            return 'general'

    def update_tags_classification_reference(
        self, 
        type_mapping: Dict[int, int], 
        category_mapping: Dict[int, int]
    ) -> int:
        """更新tags表的分类引用"""
        try:
            # 添加classification_id字段（如果不存在）
            try:
                self.db.execute(text(
                    "ALTER TABLE tags ADD COLUMN IF NOT EXISTS classification_id BIGINT REFERENCES tag_classifications(id)"
                ))
                self.db.commit()
            except Exception:
                # 字段可能已存在
                pass

            updated_count = 0

            # 更新基于tag_type_id的标签
            for old_type_id, new_classification_id in type_mapping.items():
                result = self.db.execute(text(
                    "UPDATE tags SET classification_id = :new_id WHERE tag_type_id = :old_id AND classification_id IS NULL"
                ), {"new_id": new_classification_id, "old_id": old_type_id})
                updated_count += result.rowcount

            # 更新基于tag_category_id的标签（优先级更高）
            for old_category_id, new_classification_id in category_mapping.items():
                result = self.db.execute(text(
                    "UPDATE tags SET classification_id = :new_id WHERE tag_category_id = :old_id"
                ), {"new_id": new_classification_id, "old_id": old_category_id})
                updated_count += result.rowcount

            self.db.commit()
            self.log_migration(f"Updated classification reference for {updated_count} tags")
            return updated_count

        except Exception as e:
            self.db.rollback()
            self.log_migration(f"Failed to update tags classification reference: {e}", "ERROR")
            return 0

    def validate_migration(self) -> bool:
        """验证迁移结果"""
        try:
            # 检查TagClassification表数据
            classification_count = self.db.query(TagClassification).count()
            
            # 检查原始数据数量
            original_type_count = self.db.query(TagType).count()
            original_category_count = self.db.query(TagCategory).count()
            
            # 检查tags表更新情况
            tags_with_classification = self.db.execute(text(
                "SELECT COUNT(*) FROM tags WHERE classification_id IS NOT NULL"
            )).scalar()

            self.log_migration(f"Migration validation:")
            self.log_migration(f"  - TagClassification records: {classification_count}")
            self.log_migration(f"  - Original TagType records: {original_type_count}")
            self.log_migration(f"  - Original TagCategory records: {original_category_count}")
            self.log_migration(f"  - Tags with classification_id: {tags_with_classification}")

            # 基本验证
            expected_count = original_type_count + original_category_count
            if classification_count >= expected_count:
                self.log_migration("Migration validation PASSED")
                return True
            else:
                self.log_migration("Migration validation FAILED", "ERROR")
                return False

        except Exception as e:
            self.log_migration(f"Migration validation error: {e}", "ERROR")
            return False

    def run_migration(self) -> bool:
        """执行完整的迁移流程"""
        self.log_migration("Starting unified classification migration")

        # 1. 创建新表
        if not self.create_tag_classifications_table():
            return False

        # 2. 迁移TagType数据
        type_mapping = self.migrate_tag_types()
        if not type_mapping:
            self.log_migration("TagType migration failed", "ERROR")
            return False

        # 3. 迁移TagCategory数据
        category_mapping = self.migrate_tag_categories()
        if not category_mapping:
            self.log_migration("TagCategory migration failed", "ERROR")
            return False

        # 4. 更新tags表引用
        updated_tags = self.update_tags_classification_reference(type_mapping, category_mapping)
        if updated_tags == 0:
            self.log_migration("No tags were updated with new classification references", "WARNING")

        # 5. 验证迁移结果
        if not self.validate_migration():
            return False

        self.log_migration("Unified classification migration completed successfully")
        return True

    def get_migration_log(self) -> List[str]:
        """获取迁移日志"""
        return self.migration_log
