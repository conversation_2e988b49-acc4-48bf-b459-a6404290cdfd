"""
AI标签匹配服务
实现AI生成标签与标准化标签的匹配算法
"""

from typing import List, Optional, Dict, Tuple
from decimal import Decimal
import difflib
import re
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from .models import Tag, AITagMatches, TagClassification
from .schemas import TagMatchResult, AITagMatchCreate


class AITagMatchingService:
    """AI标签匹配服务"""

    def __init__(self, db: Session):
        self.db = db
        self.similarity_threshold = 0.7  # 相似度阈值
        self.confidence_threshold = 0.6  # 置信度阈值

    async def match_tags(self, ai_tags: List[str], content_id: Optional[int] = None) -> List[TagMatchResult]:
        """
        AI标签与标准化标签匹配
        
        Args:
            ai_tags: AI生成的标签列表
            content_id: 内容ID（可选，用于上下文）
            
        Returns:
            匹配结果列表
        """
        results = []
        
        for ai_tag in ai_tags:
            # 清理和标准化AI标签
            cleaned_tag = self._clean_tag(ai_tag)
            if not cleaned_tag:
                continue
                
            # 尝试各种匹配方法
            match_result = await self._find_best_match(cleaned_tag, content_id)
            
            if match_result:
                results.append(match_result)
                # 记录匹配结果
                await self._record_match(match_result)
        
        return results

    async def _find_best_match(self, ai_tag: str, content_id: Optional[int] = None) -> Optional[TagMatchResult]:
        """找到最佳匹配"""
        
        # 1. 精确匹配
        exact_match = await self._exact_match(ai_tag)
        if exact_match:
            return TagMatchResult(
                ai_tag=ai_tag,
                standard_tag=exact_match,
                confidence_score=1.0,
                match_method="exact",
                similarity_score=1.0
            )
        
        # 2. 同义词匹配
        synonym_match = await self._synonym_match(ai_tag)
        if synonym_match:
            return TagMatchResult(
                ai_tag=ai_tag,
                standard_tag=synonym_match[0],
                confidence_score=0.9,
                match_method="synonym",
                similarity_score=synonym_match[1]
            )
        
        # 3. 语义相似度匹配
        semantic_match = await self._semantic_match(ai_tag)
        if semantic_match:
            return TagMatchResult(
                ai_tag=ai_tag,
                standard_tag=semantic_match[0],
                confidence_score=0.8,
                match_method="semantic",
                similarity_score=semantic_match[1]
            )
        
        # 4. 判断是否创建新标签
        if await self._should_create_new_tag(ai_tag):
            new_tag = await self._create_new_tag(ai_tag)
            if new_tag:
                return TagMatchResult(
                    ai_tag=ai_tag,
                    standard_tag=new_tag,
                    confidence_score=0.7,
                    match_method="new",
                    similarity_score=0.0
                )
        
        return None

    async def _exact_match(self, ai_tag: str) -> Optional[Tag]:
        """精确匹配"""
        return self.db.query(Tag).filter(
            and_(
                or_(
                    func.lower(Tag.tag_name) == ai_tag.lower(),
                    func.lower(Tag.tag_code) == ai_tag.lower()
                ),
                Tag.is_active == True
            )
        ).first()

    async def _synonym_match(self, ai_tag: str) -> Optional[Tuple[Tag, float]]:
        """同义词匹配"""
        # 查询包含同义词的标签
        tags_with_synonyms = self.db.query(Tag).filter(
            and_(
                Tag.synonyms.isnot(None),
                Tag.is_active == True
            )
        ).all()
        
        best_match = None
        best_score = 0.0
        
        for tag in tags_with_synonyms:
            if tag.synonyms:
                for synonym in tag.synonyms:
                    similarity = self._calculate_string_similarity(ai_tag.lower(), synonym.lower())
                    if similarity > best_score and similarity >= self.similarity_threshold:
                        best_score = similarity
                        best_match = tag
        
        return (best_match, best_score) if best_match else None

    async def _semantic_match(self, ai_tag: str) -> Optional[Tuple[Tag, float]]:
        """语义相似度匹配"""
        # 获取所有活跃标签
        active_tags = self.db.query(Tag).filter(Tag.is_active == True).all()
        
        best_match = None
        best_score = 0.0
        
        for tag in active_tags:
            # 计算与标签名称的相似度
            name_similarity = self._calculate_string_similarity(ai_tag.lower(), tag.tag_name.lower())
            
            # 如果有描述，也计算与描述的相似度
            desc_similarity = 0.0
            if tag.description:
                desc_similarity = self._calculate_string_similarity(ai_tag.lower(), tag.description.lower())
            
            # 综合相似度
            combined_similarity = max(name_similarity, desc_similarity * 0.7)
            
            if combined_similarity > best_score and combined_similarity >= self.similarity_threshold:
                best_score = combined_similarity
                best_match = tag
        
        return (best_match, best_score) if best_match else None

    def _calculate_string_similarity(self, str1: str, str2: str) -> float:
        """计算字符串相似度"""
        # 使用difflib计算相似度
        similarity = difflib.SequenceMatcher(None, str1, str2).ratio()
        
        # 考虑包含关系
        if str1 in str2 or str2 in str1:
            similarity = max(similarity, 0.8)
        
        # 考虑编辑距离
        edit_distance = self._levenshtein_distance(str1, str2)
        max_len = max(len(str1), len(str2))
        if max_len > 0:
            edit_similarity = 1 - (edit_distance / max_len)
            similarity = max(similarity, edit_similarity)
        
        return similarity

    def _levenshtein_distance(self, str1: str, str2: str) -> int:
        """计算编辑距离"""
        if len(str1) < len(str2):
            return self._levenshtein_distance(str2, str1)
        
        if len(str2) == 0:
            return len(str1)
        
        previous_row = list(range(len(str2) + 1))
        for i, c1 in enumerate(str1):
            current_row = [i + 1]
            for j, c2 in enumerate(str2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row
        
        return previous_row[-1]

    async def _should_create_new_tag(self, ai_tag: str) -> bool:
        """判断是否应该创建新标签"""
        # 检查标签质量
        if len(ai_tag) < 2 or len(ai_tag) > 50:
            return False
        
        # 检查是否包含特殊字符
        if re.search(r'[^\w\s\u4e00-\u9fff-]', ai_tag):
            return False
        
        # 检查是否为常见停用词
        stop_words = {'的', '了', '在', '是', '有', '和', '与', '或', '但', '而', '因为', '所以'}
        if ai_tag.lower() in stop_words:
            return False
        
        return True

    async def _create_new_tag(self, ai_tag: str) -> Optional[Tag]:
        """创建新标签"""
        try:
            # 生成标签代码
            tag_code = self._generate_tag_code(ai_tag)
            
            # 检查代码是否已存在
            existing = self.db.query(Tag).filter(Tag.tag_code == tag_code).first()
            if existing:
                return None
            
            # 获取默认分类
            default_classification = self.db.query(TagClassification).filter(
                TagClassification.classification_code == "general.keyword"
            ).first()
            
            if not default_classification:
                return None
            
            # 创建新标签
            new_tag = Tag(
                tag_name=ai_tag,
                tag_code=tag_code,
                tag_slug=tag_code.replace('_', '-'),
                classification_id=default_classification.id,
                base_weight=Decimal('0.5'),
                lifecycle_stage="emerging",
                is_system=False
            )
            
            self.db.add(new_tag)
            self.db.commit()
            self.db.refresh(new_tag)
            
            return new_tag
            
        except Exception as e:
            self.db.rollback()
            return None

    def _generate_tag_code(self, tag_name: str) -> str:
        """生成标签代码"""
        # 转换为小写并替换空格为下划线
        code = re.sub(r'[^\w\s\u4e00-\u9fff]', '', tag_name.lower())
        code = re.sub(r'\s+', '_', code.strip())
        
        # 如果包含中文，使用拼音或保持原样
        if re.search(r'[\u4e00-\u9fff]', code):
            # 这里可以集成拼音转换库，暂时保持原样
            pass
        
        return code[:50]  # 限制长度

    def _clean_tag(self, tag: str) -> str:
        """清理和标准化标签"""
        if not tag:
            return ""
        
        # 去除前后空格
        cleaned = tag.strip()
        
        # 去除多余的空格
        cleaned = re.sub(r'\s+', ' ', cleaned)
        
        # 去除特殊字符（保留中文、英文、数字、空格、连字符）
        cleaned = re.sub(r'[^\w\s\u4e00-\u9fff-]', '', cleaned)
        
        return cleaned

    async def _record_match(self, match_result: TagMatchResult) -> None:
        """记录匹配结果"""
        try:
            ai_match = AITagMatches(
                ai_tag_text=match_result.ai_tag,
                standard_tag_id=match_result.standard_tag.id,
                confidence_score=match_result.confidence_score,
                match_method=match_result.match_method,
                similarity_score=match_result.similarity_score,
                usage_count=1
            )
            
            self.db.add(ai_match)
            self.db.commit()
            
        except Exception:
            self.db.rollback()

    async def get_match_history(self, ai_tag: str) -> List[AITagMatches]:
        """获取标签匹配历史"""
        return self.db.query(AITagMatches).filter(
            AITagMatches.ai_tag_text == ai_tag
        ).order_by(AITagMatches.created_at.desc()).all()

    async def update_match_feedback(self, match_id: int, is_correct: bool) -> bool:
        """更新匹配反馈"""
        try:
            match = self.db.query(AITagMatches).filter(AITagMatches.id == match_id).first()
            if not match:
                return False
            
            if is_correct:
                match.success_rate = (match.success_rate or 0) + 0.1
            else:
                match.success_rate = max((match.success_rate or 0) - 0.1, 0)
            
            self.db.commit()
            return True
            
        except Exception:
            self.db.rollback()
            return False
