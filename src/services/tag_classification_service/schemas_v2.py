"""
标签分类服务 V2 Schemas - 统一分类模型
"""

from datetime import datetime
from typing import Dict, List, Optional

from pydantic import BaseModel, Field, validator


class TagClassificationBase(BaseModel):
    """标签分类基础模型"""
    classification_code: str = Field(..., max_length=50, description="分类代码")
    classification_name: str = Field(..., max_length=100, description="分类名称")
    classification_type: str = Field(..., description="分类类型：domain/category/type")
    domain: Optional[str] = Field(None, max_length=50, description="业务域")
    description: Optional[str] = Field(None, description="分类描述")
    icon: Optional[str] = Field(None, max_length=50, description="图标名称")
    color: Optional[str] = Field(None, max_length=7, description="颜色值")
    sort_order: int = Field(default=0, description="排序权重")
    business_rules: Optional[Dict] = Field(default_factory=dict, description="业务规则配置")

    @validator('classification_type')
    def validate_classification_type(cls, v):
        allowed_types = ['domain', 'category', 'type']
        if v not in allowed_types:
            raise ValueError(f'classification_type must be one of {allowed_types}')
        return v

    @validator('color')
    def validate_color(cls, v):
        if v and not v.startswith('#'):
            raise ValueError('color must start with #')
        return v


class TagClassificationCreate(TagClassificationBase):
    """创建标签分类"""
    parent_id: Optional[int] = Field(None, description="父分类ID")

    @validator('classification_code')
    def validate_classification_code(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('classification_code cannot be empty')
        # 检查代码格式
        if not v.replace('.', '').replace('_', '').replace('-', '').isalnum():
            raise ValueError('classification_code can only contain letters, numbers, dots, underscores and hyphens')
        return v.lower()


class TagClassificationUpdate(BaseModel):
    """更新标签分类"""
    classification_name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = Field(None)
    icon: Optional[str] = Field(None, max_length=50)
    color: Optional[str] = Field(None, max_length=7)
    sort_order: Optional[int] = Field(None)
    business_rules: Optional[Dict] = Field(None)
    is_active: Optional[bool] = Field(None)
    parent_id: Optional[int] = Field(None)

    @validator('color')
    def validate_color(cls, v):
        if v and not v.startswith('#'):
            raise ValueError('color must start with #')
        return v


class TagClassificationResponse(TagClassificationBase):
    """标签分类响应"""
    id: int
    parent_id: Optional[int]
    level: int
    path: str
    is_active: bool
    is_system: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class TagClassificationTree(TagClassificationResponse):
    """标签分类树形结构"""
    children: List['TagClassificationTree'] = []

    class Config:
        from_attributes = True


class TagClassificationListQuery(BaseModel):
    """标签分类列表查询"""
    classification_type: Optional[str] = Field(None, description="分类类型过滤")
    domain: Optional[str] = Field(None, description="业务域过滤")
    parent_id: Optional[int] = Field(None, description="父分类ID过滤")
    is_active: Optional[bool] = Field(None, description="激活状态过滤")
    search: Optional[str] = Field(None, description="搜索关键词")


class TagClassificationStats(BaseModel):
    """标签分类统计"""
    total_classifications: int
    domain_count: int
    category_count: int
    type_count: int
    active_count: int
    inactive_count: int
    max_level: int


class DomainStats(BaseModel):
    """业务域统计"""
    domain: str
    classification_count: int
    tag_count: int
    active_tag_count: int


class ClassificationMigrationResult(BaseModel):
    """分类迁移结果"""
    success: bool
    migrated_types: int
    migrated_categories: int
    updated_tags: int
    created_classifications: int
    migration_log: List[str]
    errors: List[str] = []


# 更新TagClassificationTree的前向引用
TagClassificationTree.model_rebuild()


# 兼容性Schemas - 保持向后兼容
class TagTypeResponse(BaseModel):
    """标签类型响应（兼容性）"""
    id: int
    type_code: str
    type_name: str
    description: Optional[str]
    icon: Optional[str]
    color: Optional[str]
    sort_order: int
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class TagCategoryResponse(BaseModel):
    """标签分类响应（兼容性）"""
    id: int
    category_code: str
    category_name: str
    parent_id: Optional[int]
    level: int
    description: Optional[str]
    icon: Optional[str]
    color: Optional[str]
    sort_order: int
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class UnifiedClassificationQuery(BaseModel):
    """统一分类查询"""
    include_types: bool = Field(default=True, description="包含类型级分类")
    include_categories: bool = Field(default=True, description="包含分类级")
    include_domains: bool = Field(default=True, description="包含域级")
    domain_filter: Optional[str] = Field(None, description="域过滤")
    active_only: bool = Field(default=True, description="仅激活的分类")
    max_level: Optional[int] = Field(None, description="最大层级")


class ClassificationPath(BaseModel):
    """分类路径"""
    full_path: str
    path_segments: List[str]
    domain: str
    category: Optional[str] = None
    type: Optional[str] = None


class ClassificationHierarchy(BaseModel):
    """分类层次结构"""
    domains: List[TagClassificationResponse]
    categories: List[TagClassificationResponse]
    types: List[TagClassificationResponse]
    total_count: int
