"""
标签权重计算器模块
实现完整的动态权重计算系统
"""

import logging
from datetime import datetime
from decimal import Decimal
from typing import Dict, Optional

from sqlalchemy.orm import Session

from ..models import Tag


class TagWeightCalculator:
    """标签权重计算器"""

    def __init__(self, db: Session):
        """初始化权重计算器"""
        self.db = db
        self.config = {
            'base_weight_factor': Decimal('0.4'),
            'popularity_weight_factor': Decimal('0.3'),
            'quality_weight_factor': Decimal('0.2'),
            'temporal_weight_factor': Decimal('0.1'),
            'temporal_decay_days': 365,
            'min_feedback_for_confidence': 10,
            'max_usage_for_normalization': 1000
        }

    def calculate_popularity_weight(self, tag: Tag) -> Decimal:
        """
        基于使用频率计算热度权重
        
        Args:
            tag: 标签对象
            
        Returns:
            热度权重 (0-1之间)
        """
        if not tag.usage_count or tag.usage_count == 0:
            return Decimal('0.0')

        # 获取最近30天的使用情况
        recent_usage = tag.daily_usage_count or 0
        total_usage = tag.usage_count or 0

        # 计算热度权重 (最近使用频率 / 总使用频率)
        if total_usage > 0:
            # 使用对数函数来平滑化使用次数的影响
            import math
            
            # 归一化总使用次数
            normalized_total = min(total_usage / self.config['max_usage_for_normalization'], 1.0)
            
            # 计算最近使用比例
            recent_ratio = min(recent_usage / max(total_usage, 1), 1.0)
            
            # 综合计算：总使用量权重70% + 最近使用比例30%
            popularity_score = normalized_total * 0.7 + recent_ratio * 0.3
            
            return Decimal(str(popularity_score)).quantize(Decimal('0.01'))

        return Decimal('0.0')

    def calculate_quality_weight(self, tag: Tag) -> Decimal:
        """
        基于用户反馈计算质量权重
        
        Args:
            tag: 标签对象
            
        Returns:
            质量权重 (0-1之间)
        """
        positive = tag.positive_feedback_count or 0
        negative = tag.negative_feedback_count or 0
        total_feedback = positive + negative

        if total_feedback == 0:
            return Decimal('0.5')  # 默认中性

        # 计算质量比例
        quality_ratio = positive / total_feedback
        
        # 计算置信度（反馈数量越多，置信度越高）
        confidence = min(total_feedback / self.config['min_feedback_for_confidence'], 1.0)
        
        # 最终质量权重 = 质量比例 * 置信度 + 默认权重 * (1 - 置信度)
        final_quality = quality_ratio * confidence + 0.5 * (1 - confidence)
        
        return Decimal(str(final_quality)).quantize(Decimal('0.01'))

    def calculate_temporal_weight(self, tag: Tag) -> Decimal:
        """
        基于时间相关性计算时效权重
        
        Args:
            tag: 标签对象
            
        Returns:
            时效权重 (0-1之间)
        """
        if not tag.last_used_at:
            return Decimal('0.0')

        days_since_last_use = (datetime.now() - tag.last_used_at).days
        decay_days = self.config['temporal_decay_days']

        # 时间衰减函数：指数衰减
        import math
        decay_factor = math.exp(-days_since_last_use / (decay_days / 3))
        decay_factor = max(0, min(decay_factor, 1))
        
        return Decimal(str(decay_factor)).quantize(Decimal('0.01'))

    def calculate_computed_weight(self, tag: Tag) -> Decimal:
        """
        计算最终的综合权重
        
        Args:
            tag: 标签对象
            
        Returns:
            综合权重 (0-1之间)
        """
        weights = {
            'base': tag.base_weight or Decimal('0.5'),
            'popularity': self.calculate_popularity_weight(tag),
            'quality': self.calculate_quality_weight(tag),
            'temporal': self.calculate_temporal_weight(tag)
        }

        # 加权平均计算
        computed_weight = (
            weights['base'] * self.config['base_weight_factor'] +
            weights['popularity'] * self.config['popularity_weight_factor'] +
            weights['quality'] * self.config['quality_weight_factor'] +
            weights['temporal'] * self.config['temporal_weight_factor']
        )

        return computed_weight.quantize(Decimal('0.01'))

    def update_tag_weights(self, tag_id: int) -> bool:
        """
        更新单个标签的权重
        
        Args:
            tag_id: 标签ID
            
        Returns:
            是否更新成功
        """
        try:
            tag = self.db.query(Tag).filter(Tag.id == tag_id).first()
            if not tag:
                logging.warning(f"Tag with ID {tag_id} not found")
                return False

            # 更新各项权重
            tag.popularity_weight = self.calculate_popularity_weight(tag)
            tag.quality_weight = self.calculate_quality_weight(tag)
            tag.temporal_weight = self.calculate_temporal_weight(tag)
            tag.computed_weight = self.calculate_computed_weight(tag)

            self.db.commit()
            logging.info(f"Updated weights for tag {tag_id}: {tag.tag_code}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logging.error(f"Failed to update tag weights for {tag_id}: {e}")
            return False

    def batch_update_weights(self, limit: int = 1000) -> int:
        """
        批量更新标签权重
        
        Args:
            limit: 每批处理的标签数量
            
        Returns:
            成功更新的标签数量
        """
        try:
            tags = (
                self.db.query(Tag)
                .filter(Tag.is_active == True)
                .limit(limit)
                .all()
            )
            
            updated_count = 0
            
            for tag in tags:
                # 更新各项权重
                tag.popularity_weight = self.calculate_popularity_weight(tag)
                tag.quality_weight = self.calculate_quality_weight(tag)
                tag.temporal_weight = self.calculate_temporal_weight(tag)
                tag.computed_weight = self.calculate_computed_weight(tag)
                updated_count += 1

            self.db.commit()
            logging.info(f"Batch updated weights for {updated_count} tags")
            return updated_count
            
        except Exception as e:
            self.db.rollback()
            logging.error(f"Failed to batch update weights: {e}")
            return 0

    def get_weight_analysis(self, tag_id: int) -> Optional[Dict[str, Decimal]]:
        """
        获取标签权重分析
        
        Args:
            tag_id: 标签ID
            
        Returns:
            权重分析结果
        """
        tag = self.db.query(Tag).filter(Tag.id == tag_id).first()
        if not tag:
            return None

        return {
            'base_weight': tag.base_weight or Decimal('0.5'),
            'popularity_weight': self.calculate_popularity_weight(tag),
            'quality_weight': self.calculate_quality_weight(tag),
            'temporal_weight': self.calculate_temporal_weight(tag),
            'computed_weight': self.calculate_computed_weight(tag),
            'usage_count': tag.usage_count,
            'daily_usage_count': tag.daily_usage_count or 0,
            'positive_feedback': tag.positive_feedback_count or 0,
            'negative_feedback': tag.negative_feedback_count or 0,
            'last_used_days_ago': (datetime.now() - tag.last_used_at).days if tag.last_used_at else None
        }

    def update_config(self, new_config: Dict[str, any]) -> None:
        """
        更新权重计算配置
        
        Args:
            new_config: 新的配置参数
        """
        for key, value in new_config.items():
            if key in self.config:
                if isinstance(value, (int, float)):
                    self.config[key] = Decimal(str(value))
                else:
                    self.config[key] = value
                logging.info(f"Updated config {key} to {value}")
            else:
                logging.warning(f"Unknown config key: {key}")

    def get_config(self) -> Dict[str, any]:
        """获取当前配置"""
        return {k: float(v) if isinstance(v, Decimal) else v for k, v in self.config.items()}
