"""
分类服务模块
负责分类维度和分类值的管理
"""

import logging
from typing import List, Optional, Tuple

from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from ..models import ClassificationDimension, ClassificationValue, UserClassificationPreference
from ..schemas import (
    ClassificationDimensionCreate,
    ClassificationDimensionUpdate,
    ClassificationValueCreate,
    ClassificationValueUpdate,
    UserClassificationPreferenceCreate,
    UserClassificationPreferenceUpdate,
)


class ClassificationService:
    """分类服务"""

    def __init__(self, db: Session):
        """初始化服务"""
        self.db = db

    # ==================== 分类维度操作 ====================

    def create_classification_dimension(
        self, dimension_data: ClassificationDimensionCreate
    ) -> ClassificationDimension:
        """创建分类维度"""
        try:
            # 检查维度名称是否已存在
            existing = (
                self.db.query(ClassificationDimension)
                .filter(
                    ClassificationDimension.dimension_name
                    == dimension_data.dimension_name
                )
                .first()
            )

            if existing:
                raise ValueError(
                    f"Classification dimension with name '{dimension_data.dimension_name}' already exists"
                )

            # 创建新维度
            dimension = ClassificationDimension(
                dimension_name=dimension_data.dimension_name,
                display_name=dimension_data.display_name,
                description=dimension_data.description,
                sort_order=dimension_data.sort_order,
            )

            self.db.add(dimension)
            self.db.commit()
            self.db.refresh(dimension)

            logging.info(
                f"Created classification dimension: {dimension.dimension_name}"
            )
            return dimension

        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to create classification dimension: {e}")
            raise ValueError(
                "Failed to create classification dimension due to database constraints"
            )

    def get_classification_dimension_by_id(
        self, dimension_id: int
    ) -> Optional[ClassificationDimension]:
        """根据ID获取分类维度"""
        return (
            self.db.query(ClassificationDimension)
            .filter(ClassificationDimension.id == dimension_id)
            .first()
        )

    def get_classification_dimension_by_name(
        self, dimension_name: str
    ) -> Optional[ClassificationDimension]:
        """根据名称获取分类维度"""
        return (
            self.db.query(ClassificationDimension)
            .filter(ClassificationDimension.dimension_name == dimension_name)
            .first()
        )

    def get_classification_dimensions(
        self, skip: int = 0, limit: int = 100, is_active: Optional[bool] = None
    ) -> Tuple[List[ClassificationDimension], int]:
        """获取分类维度列表"""
        query = self.db.query(ClassificationDimension)

        if is_active is not None:
            query = query.filter(ClassificationDimension.is_active == is_active)

        total = query.count()
        dimensions = (
            query.order_by(ClassificationDimension.sort_order.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

        return dimensions, total

    def update_classification_dimension(
        self, dimension_id: int, update_data: ClassificationDimensionUpdate
    ) -> Optional[ClassificationDimension]:
        """更新分类维度"""
        dimension = self.get_classification_dimension_by_id(dimension_id)
        if not dimension:
            return None

        update_dict = update_data.model_dump(exclude_unset=True)

        for field, value in update_dict.items():
            setattr(dimension, field, value)

        try:
            self.db.commit()
            self.db.refresh(dimension)
            logging.info(f"Updated classification dimension: {dimension.dimension_name}")
            return dimension
        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to update classification dimension: {e}")
            raise ValueError(
                "Failed to update classification dimension due to database constraints"
            )

    def delete_classification_dimension(self, dimension_id: int) -> bool:
        """删除分类维度"""
        dimension = self.get_classification_dimension_by_id(dimension_id)
        if not dimension:
            return False

        # 检查是否有关联的分类值
        value_count = (
            self.db.query(ClassificationValue)
            .filter(ClassificationValue.dimension_id == dimension_id)
            .count()
        )
        if value_count > 0:
            raise ValueError(
                f"Cannot delete dimension: {value_count} classification values exist"
            )

        try:
            self.db.delete(dimension)
            self.db.commit()
            logging.info(f"Deleted classification dimension: {dimension.dimension_name}")
            return True
        except Exception as e:
            self.db.rollback()
            logging.error(f"Failed to delete classification dimension: {e}")
            return False

    # ==================== 分类值操作 ====================

    def create_classification_value(
        self, value_data: ClassificationValueCreate
    ) -> ClassificationValue:
        """创建分类值"""
        try:
            # 验证维度存在
            dimension = self.get_classification_dimension_by_id(value_data.dimension_id)
            if not dimension:
                raise ValueError(
                    f"Classification dimension with ID {value_data.dimension_id} not found"
                )

            # 检查同一维度下的值代码是否已存在
            existing = (
                self.db.query(ClassificationValue)
                .filter(
                    ClassificationValue.dimension_id == value_data.dimension_id,
                    ClassificationValue.value_code == value_data.value_code,
                )
                .first()
            )

            if existing:
                raise ValueError(
                    f"Classification value with code '{value_data.value_code}' already exists in this dimension"
                )

            # 处理层级
            level = 1
            if value_data.parent_id:
                parent = self.get_classification_value_by_id(value_data.parent_id)
                if not parent:
                    raise ValueError(
                        f"Parent classification value with ID {value_data.parent_id} not found"
                    )
                if parent.dimension_id != value_data.dimension_id:
                    raise ValueError(
                        "Parent classification value must belong to the same dimension"
                    )
                level = parent.level + 1

            # 创建新分类值
            value = ClassificationValue(
                dimension_id=value_data.dimension_id,
                value_code=value_data.value_code,
                display_name=value_data.display_name,
                description=value_data.description,
                parent_id=value_data.parent_id,
                level=level,
                sort_order=value_data.sort_order,
            )

            self.db.add(value)
            self.db.commit()
            self.db.refresh(value)

            logging.info(f"Created classification value: {value.value_code}")
            return value

        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to create classification value: {e}")
            raise ValueError(
                "Failed to create classification value due to database constraints"
            )

    def get_classification_value_by_id(
        self, value_id: int
    ) -> Optional[ClassificationValue]:
        """根据ID获取分类值"""
        return (
            self.db.query(ClassificationValue)
            .filter(ClassificationValue.id == value_id)
            .first()
        )

    def get_classification_values(
        self,
        dimension_id: Optional[int] = None,
        parent_id: Optional[int] = None,
        skip: int = 0,
        limit: int = 100,
        is_active: Optional[bool] = None,
    ) -> Tuple[List[ClassificationValue], int]:
        """获取分类值列表"""
        query = self.db.query(ClassificationValue)

        if dimension_id is not None:
            query = query.filter(ClassificationValue.dimension_id == dimension_id)

        if parent_id is not None:
            query = query.filter(ClassificationValue.parent_id == parent_id)

        if is_active is not None:
            query = query.filter(ClassificationValue.is_active == is_active)

        total = query.count()
        values = (
            query.order_by(ClassificationValue.sort_order.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

        return values, total

    def update_classification_value(
        self, value_id: int, update_data: ClassificationValueUpdate
    ) -> Optional[ClassificationValue]:
        """更新分类值"""
        value = self.get_classification_value_by_id(value_id)
        if not value:
            return None

        update_dict = update_data.model_dump(exclude_unset=True)

        # 处理父分类变更
        if "parent_id" in update_dict:
            new_parent_id = update_dict["parent_id"]
            if new_parent_id != value.parent_id:
                # 检查是否会形成循环引用
                if new_parent_id and self._would_create_cycle_value(value_id, new_parent_id):
                    raise ValueError("Cannot set parent: would create circular reference")

                value.parent_id = new_parent_id
                if new_parent_id:
                    parent = self.get_classification_value_by_id(new_parent_id)
                    if not parent:
                        raise ValueError(f"Parent value with ID {new_parent_id} not found")
                    if parent.dimension_id != value.dimension_id:
                        raise ValueError("Parent value must belong to the same dimension")
                    value.level = parent.level + 1
                else:
                    value.level = 1

        # 更新其他字段
        for field, value_data in update_dict.items():
            if field not in ["parent_id"]:  # parent_id已经处理过了
                setattr(value, field, value_data)

        try:
            self.db.commit()
            self.db.refresh(value)
            logging.info(f"Updated classification value: {value.value_code}")
            return value
        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to update classification value: {e}")
            raise ValueError(
                "Failed to update classification value due to database constraints"
            )

    def delete_classification_value(self, value_id: int) -> bool:
        """删除分类值"""
        value = self.get_classification_value_by_id(value_id)
        if not value:
            return False

        # 检查是否有子分类值
        child_count = (
            self.db.query(ClassificationValue)
            .filter(ClassificationValue.parent_id == value_id)
            .count()
        )
        if child_count > 0:
            raise ValueError(f"Cannot delete value: {child_count} child values exist")

        try:
            self.db.delete(value)
            self.db.commit()
            logging.info(f"Deleted classification value: {value.value_code}")
            return True
        except Exception as e:
            self.db.rollback()
            logging.error(f"Failed to delete classification value: {e}")
            return False

    def _would_create_cycle_value(self, value_id: int, new_parent_id: int) -> bool:
        """检查设置新父分类值是否会创建循环引用"""
        current_id = new_parent_id
        while current_id:
            if current_id == value_id:
                return True
            parent = self.get_classification_value_by_id(current_id)
            current_id = parent.parent_id if parent else None
        return False

    # ==================== 用户分类偏好操作 ====================

    def create_user_classification_preference(
        self, preference_data: UserClassificationPreferenceCreate
    ) -> UserClassificationPreference:
        """创建用户分类偏好"""
        try:
            # 验证维度和值存在
            dimension = self.get_classification_dimension_by_id(preference_data.dimension_id)
            if not dimension:
                raise ValueError(
                    f"Classification dimension with ID {preference_data.dimension_id} not found"
                )

            value = self.get_classification_value_by_id(preference_data.value_id)
            if not value:
                raise ValueError(
                    f"Classification value with ID {preference_data.value_id} not found"
                )

            if value.dimension_id != preference_data.dimension_id:
                raise ValueError("Classification value must belong to the specified dimension")

            # 检查是否已存在相同的偏好设置
            existing = (
                self.db.query(UserClassificationPreference)
                .filter(
                    UserClassificationPreference.user_id == preference_data.user_id,
                    UserClassificationPreference.dimension_id == preference_data.dimension_id,
                    UserClassificationPreference.value_id == preference_data.value_id,
                )
                .first()
            )

            if existing:
                # 更新现有偏好
                existing.preference_score = preference_data.preference_score
                existing.source = preference_data.source
                self.db.commit()
                self.db.refresh(existing)
                return existing

            # 创建新偏好
            preference = UserClassificationPreference(
                user_id=preference_data.user_id,
                dimension_id=preference_data.dimension_id,
                value_id=preference_data.value_id,
                preference_score=preference_data.preference_score,
                source=preference_data.source,
            )

            self.db.add(preference)
            self.db.commit()
            self.db.refresh(preference)

            logging.info(f"Created user classification preference for user: {preference_data.user_id}")
            return preference

        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to create user classification preference: {e}")
            raise ValueError(
                "Failed to create user classification preference due to database constraints"
            )

    def get_user_classification_preferences(
        self, user_id: int, dimension_id: Optional[int] = None
    ) -> List[UserClassificationPreference]:
        """获取用户分类偏好"""
        query = self.db.query(UserClassificationPreference).filter(
            UserClassificationPreference.user_id == user_id
        )

        if dimension_id is not None:
            query = query.filter(UserClassificationPreference.dimension_id == dimension_id)

        return query.order_by(UserClassificationPreference.preference_score.desc()).all()
