"""
标签数据验证器
实现完整的业务规则验证
"""

import logging
import re
from typing import List, Optional, Set

from sqlalchemy.orm import Session

from ..models import Tag
from ..models_v2 import TagClassification


class TagValidator:
    """标签数据验证器"""

    def __init__(self, db: Session):
        """初始化验证器"""
        self.db = db
        self.validation_rules = {
            'max_hierarchy_depth': 5,
            'min_tag_name_length': 2,
            'max_tag_name_length': 100,
            'min_tag_code_length': 2,
            'max_tag_code_length': 50,
            'allowed_tag_code_pattern': r'^[a-z0-9_\-\.]+$',
            'max_synonyms_count': 10,
            'weight_min_value': 0.0,
            'weight_max_value': 1.0,
            'max_description_length': 1000
        }

    def validate_tag_creation(self, tag: Tag) -> List[str]:
        """验证标签创建的完整性"""
        errors = []
        
        # 基础字段验证
        errors.extend(self.validate_basic_fields(tag))
        
        # 唯一性验证
        errors.extend(self.validate_tag_uniqueness(tag))
        
        # 层次结构验证
        errors.extend(self.validate_tag_hierarchy(tag))
        
        # 权重一致性验证
        errors.extend(self.validate_weight_consistency(tag))
        
        # 业务规则验证
        errors.extend(self.validate_business_rules(tag))
        
        return errors

    def validate_tag_update(self, tag: Tag, original_tag: Tag) -> List[str]:
        """验证标签更新的完整性"""
        errors = []
        
        # 基础字段验证
        errors.extend(self.validate_basic_fields(tag))
        
        # 唯一性验证（排除自身）
        errors.extend(self.validate_tag_uniqueness(tag))
        
        # 层次结构验证
        errors.extend(self.validate_tag_hierarchy(tag))
        
        # 权重一致性验证
        errors.extend(self.validate_weight_consistency(tag))
        
        # 更新特定验证
        errors.extend(self.validate_update_constraints(tag, original_tag))
        
        return errors

    def validate_basic_fields(self, tag: Tag) -> List[str]:
        """验证基础字段"""
        errors = []
        
        # 标签名称验证
        if not tag.tag_name or len(tag.tag_name.strip()) == 0:
            errors.append("Tag name cannot be empty")
        elif len(tag.tag_name) < self.validation_rules['min_tag_name_length']:
            errors.append(f"Tag name too short (min: {self.validation_rules['min_tag_name_length']})")
        elif len(tag.tag_name) > self.validation_rules['max_tag_name_length']:
            errors.append(f"Tag name too long (max: {self.validation_rules['max_tag_name_length']})")
        
        # 标签代码验证
        if not tag.tag_code or len(tag.tag_code.strip()) == 0:
            errors.append("Tag code cannot be empty")
        elif len(tag.tag_code) < self.validation_rules['min_tag_code_length']:
            errors.append(f"Tag code too short (min: {self.validation_rules['min_tag_code_length']})")
        elif len(tag.tag_code) > self.validation_rules['max_tag_code_length']:
            errors.append(f"Tag code too long (max: {self.validation_rules['max_tag_code_length']})")
        elif not re.match(self.validation_rules['allowed_tag_code_pattern'], tag.tag_code):
            errors.append("Tag code contains invalid characters (only lowercase letters, numbers, underscore, hyphen, dot allowed)")
        
        # 标签slug验证
        if not tag.tag_slug or len(tag.tag_slug.strip()) == 0:
            errors.append("Tag slug cannot be empty")
        
        # 描述长度验证
        if tag.description and len(tag.description) > self.validation_rules['max_description_length']:
            errors.append(f"Description too long (max: {self.validation_rules['max_description_length']})")
        
        # 生命周期阶段验证
        allowed_stages = ['draft', 'active', 'deprecated', 'archived']
        if tag.lifecycle_stage and tag.lifecycle_stage not in allowed_stages:
            errors.append(f"Invalid lifecycle stage. Allowed: {allowed_stages}")
        
        # 颜色格式验证
        if tag.color and not re.match(r'^#[0-9A-Fa-f]{6}$', tag.color):
            errors.append("Color must be in hex format (#RRGGBB)")
        
        return errors

    def validate_tag_hierarchy(self, tag: Tag) -> List[str]:
        """验证标签层次结构的合理性"""
        errors = []

        # 检查层级深度
        if tag.level is not None and tag.level > self.validation_rules['max_hierarchy_depth']:
            errors.append(f"Tag hierarchy too deep: {tag.level} (max: {self.validation_rules['max_hierarchy_depth']})")

        # 检查路径一致性
        if tag.parent_id:
            parent = self.db.query(Tag).filter(Tag.id == tag.parent_id).first()
            if parent:
                expected_path = f"{parent.path}.{tag.tag_code}"
                if tag.path != expected_path:
                    errors.append(f"Path inconsistent: expected {expected_path}, got {tag.path}")
                
                # 检查父子关系的业务逻辑
                if parent.level >= tag.level:
                    errors.append(f"Child tag level ({tag.level}) must be greater than parent level ({parent.level})")
            else:
                errors.append(f"Parent tag {tag.parent_id} not found")
        else:
            # 根标签的路径应该等于tag_code
            if tag.path != tag.tag_code:
                errors.append(f"Root tag path should equal tag_code: {tag.path} != {tag.tag_code}")
            
            # 根标签的层级应该是1
            if tag.level != 1:
                errors.append(f"Root tag level should be 1, got {tag.level}")

        # 检查循环引用
        if self._has_circular_reference(tag):
            errors.append("Circular reference detected in tag hierarchy")

        return errors

    def validate_weight_consistency(self, tag: Tag) -> List[str]:
        """验证权重数据的一致性"""
        errors = []

        # 检查权重范围
        weights = [
            ('base_weight', tag.base_weight),
            ('popularity_weight', tag.popularity_weight),
            ('quality_weight', tag.quality_weight),
            ('temporal_weight', tag.temporal_weight),
            ('computed_weight', tag.computed_weight)
        ]

        for weight_name, weight_value in weights:
            if weight_value is not None:
                if not (self.validation_rules['weight_min_value'] <= float(weight_value) <= self.validation_rules['weight_max_value']):
                    errors.append(f"{weight_name} out of range: {weight_value} (should be {self.validation_rules['weight_min_value']}-{self.validation_rules['weight_max_value']})")

        # 检查反馈数据一致性
        positive = tag.positive_feedback_count or 0
        negative = tag.negative_feedback_count or 0
        if positive < 0 or negative < 0:
            errors.append("Feedback counts cannot be negative")

        # 检查使用统计一致性
        if tag.usage_count is not None and tag.usage_count < 0:
            errors.append("Usage count cannot be negative")
        
        if tag.daily_usage_count is not None and tag.daily_usage_count < 0:
            errors.append("Daily usage count cannot be negative")

        return errors

    def validate_tag_uniqueness(self, tag: Tag) -> List[str]:
        """验证标签唯一性"""
        errors = []

        # 检查tag_code唯一性
        existing_code = (
            self.db.query(Tag)
            .filter(Tag.tag_code == tag.tag_code, Tag.id != tag.id)
            .first()
        )
        if existing_code:
            errors.append(f"Tag code '{tag.tag_code}' already exists")

        # 检查tag_slug唯一性
        existing_slug = (
            self.db.query(Tag)
            .filter(Tag.tag_slug == tag.tag_slug, Tag.id != tag.id)
            .first()
        )
        if existing_slug:
            errors.append(f"Tag slug '{tag.tag_slug}' already exists")

        return errors

    def validate_business_rules(self, tag: Tag) -> List[str]:
        """验证业务规则"""
        errors = []

        # 系统标签保护
        if tag.is_system and not self._is_system_operation():
            errors.append("Cannot modify system tags")

        # 分类关联验证
        if hasattr(tag, 'classification_id') and tag.classification_id:
            classification = (
                self.db.query(TagClassification)
                .filter(TagClassification.id == tag.classification_id)
                .first()
            )
            if not classification:
                errors.append(f"Classification {tag.classification_id} not found")
            elif not classification.is_active:
                errors.append("Cannot associate with inactive classification")

        # 同义词验证
        if tag.synonyms:
            try:
                import json
                synonyms_list = json.loads(tag.synonyms) if isinstance(tag.synonyms, str) else tag.synonyms
                if isinstance(synonyms_list, list):
                    if len(synonyms_list) > self.validation_rules['max_synonyms_count']:
                        errors.append(f"Too many synonyms (max: {self.validation_rules['max_synonyms_count']})")
                    
                    # 检查同义词重复
                    if len(synonyms_list) != len(set(synonyms_list)):
                        errors.append("Duplicate synonyms found")
                    
                    # 检查同义词与标签名称重复
                    if tag.tag_name in synonyms_list:
                        errors.append("Synonym cannot be the same as tag name")
            except (json.JSONDecodeError, TypeError):
                errors.append("Invalid synonyms format")

        return errors

    def validate_update_constraints(self, tag: Tag, original_tag: Tag) -> List[str]:
        """验证更新约束"""
        errors = []

        # 检查是否有子标签，如果有则不能更改某些关键字段
        child_count = self.db.query(Tag).filter(Tag.parent_id == tag.id).count()
        if child_count > 0:
            if tag.tag_code != original_tag.tag_code:
                errors.append("Cannot change tag_code when tag has children")
            
            if tag.is_active != original_tag.is_active and not tag.is_active:
                errors.append("Cannot deactivate tag when it has active children")

        # 检查标签是否被大量使用，如果是则限制某些更改
        if original_tag.usage_count and original_tag.usage_count > 100:
            if tag.tag_code != original_tag.tag_code:
                errors.append("Cannot change tag_code for heavily used tags (usage > 100)")

        return errors

    def _has_circular_reference(self, tag: Tag) -> bool:
        """检查是否存在循环引用"""
        if not tag.parent_id:
            return False
            
        visited = set()
        current_id = tag.parent_id

        while current_id:
            if current_id in visited or current_id == tag.id:
                return True
            visited.add(current_id)

            parent = self.db.query(Tag).filter(Tag.id == current_id).first()
            current_id = parent.parent_id if parent else None

        return False

    def _is_system_operation(self) -> bool:
        """检查是否为系统操作（简化实现，实际应该从上下文获取）"""
        # 这里应该从请求上下文或其他方式判断是否为系统操作
        # 简化实现，返回False
        return False

    def validate_batch_operation(self, tags: List[Tag]) -> dict:
        """批量验证标签"""
        results = {
            'valid_tags': [],
            'invalid_tags': [],
            'total_errors': 0,
            'validation_summary': {}
        }

        for tag in tags:
            errors = self.validate_tag_creation(tag)
            if errors:
                results['invalid_tags'].append({
                    'tag': tag,
                    'errors': errors
                })
                results['total_errors'] += len(errors)
            else:
                results['valid_tags'].append(tag)

        # 生成验证摘要
        results['validation_summary'] = {
            'total_tags': len(tags),
            'valid_count': len(results['valid_tags']),
            'invalid_count': len(results['invalid_tags']),
            'error_count': results['total_errors']
        }

        return results

    def get_validation_rules(self) -> dict:
        """获取当前验证规则"""
        return self.validation_rules.copy()

    def update_validation_rules(self, new_rules: dict) -> None:
        """更新验证规则"""
        for key, value in new_rules.items():
            if key in self.validation_rules:
                self.validation_rules[key] = value
                logging.info(f"Updated validation rule {key} to {value}")
            else:
                logging.warning(f"Unknown validation rule: {key}")
