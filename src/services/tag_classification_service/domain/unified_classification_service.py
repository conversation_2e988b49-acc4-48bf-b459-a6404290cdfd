"""
统一标签分类服务
实现TagClassification的完整管理功能
"""

import logging
from typing import Dict, List, Optional, Tuple

from sqlalchemy import and_, or_
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from ..models_v2 import TagClassification
from ..schemas_v2 import (
    ClassificationHierarchy,
    TagClassificationCreate,
    TagClassificationListQuery,
    TagClassificationStats,
    TagClassificationTree,
    TagClassificationUpdate,
    DomainStats,
)


class UnifiedClassificationService:
    """统一标签分类服务"""

    def __init__(self, db: Session):
        """初始化服务"""
        self.db = db

    def create_classification(self, data: TagClassificationCreate) -> TagClassification:
        """创建标签分类"""
        try:
            # 检查代码唯一性
            existing = (
                self.db.query(TagClassification)
                .filter(TagClassification.classification_code == data.classification_code)
                .first()
            )
            if existing:
                raise ValueError(f"Classification code '{data.classification_code}' already exists")

            # 计算层级和路径
            level = 1
            path = data.classification_code
            
            if data.parent_id:
                parent = self.get_classification_by_id(data.parent_id)
                if not parent:
                    raise ValueError(f"Parent classification {data.parent_id} not found")
                
                # 验证层级关系
                if not self._validate_hierarchy(data.classification_type, parent.classification_type):
                    raise ValueError(f"Invalid hierarchy: {data.classification_type} cannot be child of {parent.classification_type}")
                
                level = parent.level + 1
                path = f"{parent.path}.{data.classification_code.split('.')[-1]}"

            # 自动确定domain
            domain = data.domain or self._determine_domain(data.classification_code, data.classification_type)

            classification = TagClassification(
                classification_code=data.classification_code,
                classification_name=data.classification_name,
                classification_type=data.classification_type,
                parent_id=data.parent_id,
                level=level,
                path=path,
                domain=domain,
                description=data.description,
                icon=data.icon,
                color=data.color,
                sort_order=data.sort_order,
                business_rules=data.business_rules or {}
            )

            self.db.add(classification)
            self.db.commit()
            self.db.refresh(classification)

            logging.info(f"Created classification: {classification.classification_code}")
            return classification

        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to create classification: {e}")
            raise ValueError("Failed to create classification due to database constraints")

    def _validate_hierarchy(self, child_type: str, parent_type: str) -> bool:
        """验证分类层级关系"""
        valid_hierarchies = {
            'category': ['domain'],
            'type': ['domain', 'category']
        }
        
        if child_type == 'domain':
            return parent_type is None
        
        return parent_type in valid_hierarchies.get(child_type, [])

    def _determine_domain(self, code: str, classification_type: str) -> str:
        """自动确定业务域"""
        if classification_type == 'domain':
            return code
        
        # 从代码中提取域
        parts = code.split('.')
        if len(parts) > 0:
            first_part = parts[0]
            if first_part in ['finance', 'technology', 'general']:
                return first_part
        
        return 'general'

    def get_classification_by_id(self, classification_id: int) -> Optional[TagClassification]:
        """根据ID获取分类"""
        return (
            self.db.query(TagClassification)
            .filter(TagClassification.id == classification_id)
            .first()
        )

    def get_classification_by_code(self, code: str) -> Optional[TagClassification]:
        """根据代码获取分类"""
        return (
            self.db.query(TagClassification)
            .filter(TagClassification.classification_code == code)
            .first()
        )

    def get_classifications(
        self, 
        query: TagClassificationListQuery,
        skip: int = 0, 
        limit: int = 100
    ) -> Tuple[List[TagClassification], int]:
        """获取分类列表"""
        db_query = self.db.query(TagClassification)

        # 应用过滤条件
        if query.classification_type:
            db_query = db_query.filter(
                TagClassification.classification_type == query.classification_type
            )

        if query.domain:
            db_query = db_query.filter(TagClassification.domain == query.domain)

        if query.parent_id is not None:
            db_query = db_query.filter(TagClassification.parent_id == query.parent_id)

        if query.is_active is not None:
            db_query = db_query.filter(TagClassification.is_active == query.is_active)

        if query.search:
            db_query = db_query.filter(
                or_(
                    TagClassification.classification_name.ilike(f"%{query.search}%"),
                    TagClassification.classification_code.ilike(f"%{query.search}%"),
                    TagClassification.description.ilike(f"%{query.search}%")
                )
            )

        total = db_query.count()
        
        classifications = (
            db_query.order_by(
                TagClassification.level,
                TagClassification.sort_order.desc(),
                TagClassification.classification_name
            )
            .offset(skip)
            .limit(limit)
            .all()
        )

        return classifications, total

    def get_classifications_tree(self, domain: Optional[str] = None) -> List[TagClassificationTree]:
        """获取分类树形结构"""
        query = self.db.query(TagClassification).filter(TagClassification.is_active == True)
        
        if domain:
            query = query.filter(TagClassification.domain == domain)
        
        classifications = query.order_by(
            TagClassification.level,
            TagClassification.sort_order.desc()
        ).all()

        # 构建树形结构
        return self._build_tree(classifications)

    def _build_tree(self, classifications: List[TagClassification]) -> List[TagClassificationTree]:
        """构建树形结构"""
        classification_dict = {c.id: c for c in classifications}
        tree = []

        for classification in classifications:
            if classification.parent_id is None:
                # 根节点
                tree_node = TagClassificationTree.from_orm(classification)
                tree_node.children = self._build_children(classification.id, classification_dict)
                tree.append(tree_node)

        return tree

    def _build_children(
        self, 
        parent_id: int, 
        classification_dict: Dict[int, TagClassification]
    ) -> List[TagClassificationTree]:
        """构建子节点"""
        children = []
        
        for classification in classification_dict.values():
            if classification.parent_id == parent_id:
                tree_node = TagClassificationTree.from_orm(classification)
                tree_node.children = self._build_children(classification.id, classification_dict)
                children.append(tree_node)
        
        return sorted(children, key=lambda x: (-x.sort_order, x.classification_name))

    def update_classification(
        self, 
        classification_id: int, 
        update_data: TagClassificationUpdate
    ) -> Optional[TagClassification]:
        """更新分类"""
        classification = self.get_classification_by_id(classification_id)
        if not classification:
            return None

        update_dict = update_data.model_dump(exclude_unset=True)

        # 处理父分类变更
        if "parent_id" in update_dict:
            new_parent_id = update_dict["parent_id"]
            if new_parent_id != classification.parent_id:
                if new_parent_id and self._would_create_cycle(classification_id, new_parent_id):
                    raise ValueError("Cannot set parent: would create circular reference")

                classification.parent_id = new_parent_id
                if new_parent_id:
                    parent = self.get_classification_by_id(new_parent_id)
                    if not parent:
                        raise ValueError(f"Parent classification {new_parent_id} not found")
                    classification.level = parent.level + 1
                    # 重新计算路径
                    code_part = classification.classification_code.split('.')[-1]
                    classification.path = f"{parent.path}.{code_part}"
                else:
                    classification.level = 1
                    classification.path = classification.classification_code

        # 更新其他字段
        for field, value in update_dict.items():
            if field not in ["parent_id"]:
                setattr(classification, field, value)

        try:
            self.db.commit()
            self.db.refresh(classification)
            logging.info(f"Updated classification: {classification.classification_code}")
            return classification
        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to update classification: {e}")
            raise ValueError("Failed to update classification due to database constraints")

    def delete_classification(self, classification_id: int) -> bool:
        """删除分类"""
        classification = self.get_classification_by_id(classification_id)
        if not classification:
            return False

        # 检查是否有子分类
        child_count = (
            self.db.query(TagClassification)
            .filter(TagClassification.parent_id == classification_id)
            .count()
        )
        if child_count > 0:
            raise ValueError(f"Cannot delete classification: {child_count} child classifications exist")

        # 检查是否有关联的标签
        from ..models_v2 import Tag
        tag_count = (
            self.db.query(Tag)
            .filter(Tag.classification_id == classification_id)
            .count()
        )
        if tag_count > 0:
            raise ValueError(f"Cannot delete classification: {tag_count} tags are using this classification")

        try:
            self.db.delete(classification)
            self.db.commit()
            logging.info(f"Deleted classification: {classification.classification_code}")
            return True
        except Exception as e:
            self.db.rollback()
            logging.error(f"Failed to delete classification: {e}")
            return False

    def _would_create_cycle(self, classification_id: int, new_parent_id: int) -> bool:
        """检查是否会创建循环引用"""
        current_id = new_parent_id
        while current_id:
            if current_id == classification_id:
                return True
            parent = self.get_classification_by_id(current_id)
            current_id = parent.parent_id if parent else None
        return False

    def get_classification_stats(self) -> TagClassificationStats:
        """获取分类统计信息"""
        total = self.db.query(TagClassification).count()
        domain_count = (
            self.db.query(TagClassification)
            .filter(TagClassification.classification_type == 'domain')
            .count()
        )
        category_count = (
            self.db.query(TagClassification)
            .filter(TagClassification.classification_type == 'category')
            .count()
        )
        type_count = (
            self.db.query(TagClassification)
            .filter(TagClassification.classification_type == 'type')
            .count()
        )
        active_count = (
            self.db.query(TagClassification)
            .filter(TagClassification.is_active == True)
            .count()
        )
        inactive_count = total - active_count
        
        max_level = (
            self.db.query(TagClassification.level)
            .order_by(TagClassification.level.desc())
            .first()
        )
        max_level = max_level[0] if max_level else 0

        return TagClassificationStats(
            total_classifications=total,
            domain_count=domain_count,
            category_count=category_count,
            type_count=type_count,
            active_count=active_count,
            inactive_count=inactive_count,
            max_level=max_level
        )
