"""
用户兴趣服务模块
负责用户兴趣标签管理、行为记录和画像生成
"""

import json
import logging
from datetime import datetime, timezone
from decimal import Decimal
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, func
from sqlalchemy.orm import Session

from ..models import Tag, UserInterestTag, UserProfileSnapshot


class UserInterestService:
    """用户兴趣服务"""

    def __init__(self, db: Session):
        """初始化服务"""
        self.db = db

    def get_user_interests(
        self, user_id: int, limit: int = 50
    ) -> List[UserInterestTag]:
        """获取用户兴趣标签"""
        return (
            self.db.query(UserInterestTag)
            .filter(UserInterestTag.user_id == user_id)
            .order_by(UserInterestTag.computed_interest.desc())
            .limit(limit)
            .all()
        )

    def update_user_interest(
        self,
        user_id: int,
        tag_id: int,
        explicit_interest: Optional[Decimal] = None,
        implicit_interest: Optional[Decimal] = None,
        source: str = "behavior",
    ) -> UserInterestTag:
        """更新用户兴趣"""
        # 查找现有兴趣记录
        interest = (
            self.db.query(UserInterestTag)
            .filter(
                and_(
                    UserInterestTag.user_id == user_id, UserInterestTag.tag_id == tag_id
                )
            )
            .first()
        )

        if not interest:
            # 创建新的兴趣记录
            interest = UserInterestTag(
                user_id=user_id,
                tag_id=tag_id,
                explicit_interest=explicit_interest or Decimal("0.0"),
                implicit_interest=implicit_interest or Decimal("0.0"),
                source=source,
            )
            self.db.add(interest)
        else:
            # 更新现有记录
            if explicit_interest is not None:
                interest.explicit_interest = explicit_interest
            if implicit_interest is not None:
                interest.implicit_interest = implicit_interest

            interest.last_reinforced_at = datetime.now()
            interest.reinforcement_count += 1

        try:
            self.db.commit()
            self.db.refresh(interest)
            return interest
        except Exception as e:
            self.db.rollback()
            logging.error(f"Failed to update user interest: {e}")
            raise

    def record_user_behavior(
        self,
        user_id: int,
        tag_id: int,
        action: str,
        weight: float = 0.1,
    ) -> bool:
        """记录用户行为并更新兴趣"""
        try:
            # 根据行为类型计算兴趣增量
            interest_increment = self._calculate_interest_increment(action, weight)

            # 获取或创建用户兴趣记录
            interest = (
                self.db.query(UserInterestTag)
                .filter(
                    and_(
                        UserInterestTag.user_id == user_id,
                        UserInterestTag.tag_id == tag_id,
                    )
                )
                .first()
            )

            if not interest:
                interest = UserInterestTag(
                    user_id=user_id,
                    tag_id=tag_id,
                    explicit_interest=Decimal("0.0"),
                    implicit_interest=Decimal(str(interest_increment)),
                    source="behavior",
                )
                self.db.add(interest)
            else:
                # 更新隐式兴趣
                new_implicit = min(
                    Decimal("1.0"),
                    interest.implicit_interest + Decimal(str(interest_increment))
                )
                interest.implicit_interest = new_implicit
                interest.last_reinforced_at = datetime.now()
                interest.reinforcement_count += 1

            # 更新行为统计
            if action == "click":
                interest.click_count += 1
            elif action == "view":
                interest.view_time_seconds += int(weight * 60)  # weight作为分钟数
            elif action == "share":
                interest.share_count += 1

            self.db.commit()
            logging.info(f"Recorded user behavior: user={user_id}, tag={tag_id}, action={action}")
            return True

        except Exception as e:
            self.db.rollback()
            logging.error(f"Failed to record user behavior: {e}")
            return False

    def _calculate_interest_increment(self, action: str, weight: float) -> float:
        """根据行为类型计算兴趣增量"""
        action_weights = {
            "view": 0.01,
            "click": 0.05,
            "share": 0.1,
            "like": 0.08,
            "comment": 0.12,
            "bookmark": 0.15,
        }
        
        base_weight = action_weights.get(action, 0.01)
        return base_weight * weight

    def get_user_top_interests(
        self, user_id: int, limit: int = 20, min_interest: float = 0.1
    ) -> List[Dict[str, Any]]:
        """获取用户Top兴趣标签"""
        interests = (
            self.db.query(UserInterestTag, Tag)
            .join(Tag, UserInterestTag.tag_id == Tag.id)
            .filter(
                and_(
                    UserInterestTag.user_id == user_id,
                    UserInterestTag.computed_interest >= min_interest,
                    Tag.is_active == True,
                )
            )
            .order_by(UserInterestTag.computed_interest.desc())
            .limit(limit)
            .all()
        )

        result = []
        for interest, tag in interests:
            result.append({
                "tag_id": tag.id,
                "tag_name": tag.tag_name,
                "tag_code": tag.tag_code,
                "interest_score": float(interest.computed_interest),
                "explicit_interest": float(interest.explicit_interest),
                "implicit_interest": float(interest.implicit_interest),
                "click_count": interest.click_count,
                "view_time_seconds": interest.view_time_seconds,
                "share_count": interest.share_count,
            })

        return result

    def generate_profile_snapshot(self, user_id: int) -> UserProfileSnapshot:
        """生成用户画像快照"""
        try:
            # 获取用户Top兴趣
            top_interests = self.get_user_top_interests(user_id, limit=20)

            # 计算兴趣分类分布
            interest_categories = self._calculate_interest_categories(user_id)

            # 分析行为模式
            behavioral_patterns = self._analyze_behavioral_patterns(user_id)

            # 统计信息
            total_interactions = (
                self.db.query(func.sum(UserInterestTag.click_count + UserInterestTag.share_count))
                .filter(UserInterestTag.user_id == user_id)
                .scalar() or 0
            )

            active_days_count = (
                self.db.query(func.count(func.distinct(func.date(UserInterestTag.last_reinforced_at))))
                .filter(UserInterestTag.user_id == user_id)
                .scalar() or 0
            )

            # 创建快照
            snapshot = UserProfileSnapshot(
                user_id=user_id,
                snapshot_date=datetime.now(timezone.utc).date(),
                top_interests=top_interests,
                interest_categories=interest_categories,
                behavioral_patterns=behavioral_patterns,
                total_interactions=total_interactions,
                active_days_count=active_days_count,
            )

            self.db.add(snapshot)
            self.db.commit()
            self.db.refresh(snapshot)

            logging.info(f"Generated profile snapshot for user: {user_id}")
            return snapshot

        except Exception as e:
            self.db.rollback()
            logging.error(f"Failed to create user profile snapshot: {e}")
            raise

    def _calculate_interest_categories(self, user_id: int) -> Dict[str, Any]:
        """计算用户兴趣分类分布"""
        # 这里可以根据标签的分类来统计用户在各个分类上的兴趣分布
        # 简化实现，返回基本统计
        return {
            "total_tags": self.db.query(UserInterestTag).filter(UserInterestTag.user_id == user_id).count(),
            "active_tags": self.db.query(UserInterestTag).filter(
                and_(
                    UserInterestTag.user_id == user_id,
                    UserInterestTag.computed_interest > 0.1
                )
            ).count(),
        }

    def _analyze_behavioral_patterns(self, user_id: int) -> Dict[str, Any]:
        """分析用户行为模式"""
        # 简化实现，返回基本行为统计
        total_clicks = (
            self.db.query(func.sum(UserInterestTag.click_count))
            .filter(UserInterestTag.user_id == user_id)
            .scalar() or 0
        )

        total_shares = (
            self.db.query(func.sum(UserInterestTag.share_count))
            .filter(UserInterestTag.user_id == user_id)
            .scalar() or 0
        )

        total_view_time = (
            self.db.query(func.sum(UserInterestTag.view_time_seconds))
            .filter(UserInterestTag.user_id == user_id)
            .scalar() or 0
        )

        return {
            "total_clicks": total_clicks,
            "total_shares": total_shares,
            "total_view_time_minutes": total_view_time // 60,
            "engagement_level": "high" if total_clicks > 100 else "medium" if total_clicks > 20 else "low",
        }
