"""
标签分类服务模块
合并原TagTypeService和TagCategoryService，提供统一的分类管理
"""

import logging
from typing import List, Optional, Tuple

from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from ..models import TagCategory, TagType
from ..schemas import (
    TagCategoryCreate,
    TagCategoryUpdate,
    TagTypeCreate,
    TagTypeUpdate,
)


class TagClassificationService:
    """标签分类服务 - 合并标签类型和标签分类管理"""

    def __init__(self, db: Session):
        """初始化服务"""
        self.db = db

    # ==================== 标签类型管理 ====================

    def create_tag_type(self, tag_type_data: TagTypeCreate) -> TagType:
        """
        创建标签类型

        Args:
            tag_type_data: 标签类型创建数据

        Returns:
            创建的标签类型对象

        Raises:
            ValueError: 当标签类型代码已存在时
        """
        try:
            # 检查代码是否已存在
            existing = (
                self.db.query(TagType)
                .filter(TagType.type_code == tag_type_data.type_code)
                .first()
            )

            if existing:
                raise ValueError(
                    f"Tag type with code '{tag_type_data.type_code}' already exists"
                )

            # 创建新标签类型
            tag_type = TagType(
                type_code=tag_type_data.type_code,
                type_name=tag_type_data.type_name,
                description=tag_type_data.description,
                icon=tag_type_data.icon,
                color=tag_type_data.color,
                sort_order=tag_type_data.sort_order,
            )

            self.db.add(tag_type)
            self.db.commit()
            self.db.refresh(tag_type)

            logging.info(f"Created tag type: {tag_type.type_code}")
            return tag_type

        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to create tag type: {e}")
            raise ValueError("Failed to create tag type due to database constraints")

    def get_tag_type_by_id(self, tag_type_id: int) -> Optional[TagType]:
        """根据ID获取标签类型"""
        return self.db.query(TagType).filter(TagType.id == tag_type_id).first()

    def get_tag_type_by_code(self, type_code: str) -> Optional[TagType]:
        """根据代码获取标签类型"""
        return self.db.query(TagType).filter(TagType.type_code == type_code).first()

    def get_tag_types(
        self, skip: int = 0, limit: int = 100, is_active: Optional[bool] = None
    ) -> Tuple[List[TagType], int]:
        """获取标签类型列表"""
        query = self.db.query(TagType)

        if is_active is not None:
            query = query.filter(TagType.is_active == is_active)

        total = query.count()
        tag_types = query.order_by(TagType.sort_order.desc()).offset(skip).limit(limit).all()

        return tag_types, total

    def update_tag_type(
        self, tag_type_id: int, update_data: TagTypeUpdate
    ) -> Optional[TagType]:
        """更新标签类型"""
        tag_type = self.get_tag_type_by_id(tag_type_id)
        if not tag_type:
            return None

        update_dict = update_data.model_dump(exclude_unset=True)

        for field, value in update_dict.items():
            setattr(tag_type, field, value)

        try:
            self.db.commit()
            self.db.refresh(tag_type)
            logging.info(f"Updated tag type: {tag_type.type_code}")
            return tag_type
        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to update tag type: {e}")
            raise ValueError("Failed to update tag type due to database constraints")

    def delete_tag_type(self, tag_type_id: int) -> bool:
        """删除标签类型"""
        tag_type = self.get_tag_type_by_id(tag_type_id)
        if not tag_type:
            return False

        # 检查是否有关联的标签
        from ..models import Tag
        tag_count = self.db.query(Tag).filter(Tag.tag_type_id == tag_type_id).count()
        if tag_count > 0:
            raise ValueError(f"Cannot delete tag type: {tag_count} tags are using this type")

        try:
            self.db.delete(tag_type)
            self.db.commit()
            logging.info(f"Deleted tag type: {tag_type.type_code}")
            return True
        except Exception as e:
            self.db.rollback()
            logging.error(f"Failed to delete tag type: {e}")
            return False

    # ==================== 标签分类管理 ====================

    def create_tag_category(self, category_data: TagCategoryCreate) -> TagCategory:
        """创建标签分类"""
        try:
            # 检查代码是否已存在
            existing = (
                self.db.query(TagCategory)
                .filter(TagCategory.category_code == category_data.category_code)
                .first()
            )

            if existing:
                raise ValueError(
                    f"Tag category with code '{category_data.category_code}' already exists"
                )

            # 处理层级和父分类
            level = 1
            if category_data.parent_id:
                parent = self.get_tag_category_by_id(category_data.parent_id)
                if not parent:
                    raise ValueError(
                        f"Parent category with ID {category_data.parent_id} not found"
                    )
                level = parent.level + 1

            # 创建新标签分类
            category = TagCategory(
                category_code=category_data.category_code,
                category_name=category_data.category_name,
                parent_id=category_data.parent_id,
                level=level,
                description=category_data.description,
                icon=category_data.icon,
                color=category_data.color,
                sort_order=category_data.sort_order,
            )

            self.db.add(category)
            self.db.commit()
            self.db.refresh(category)

            logging.info(f"Created tag category: {category.category_code}")
            return category

        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to create tag category: {e}")
            raise ValueError("Failed to create tag category due to database constraints")

    def get_tag_category_by_id(self, category_id: int) -> Optional[TagCategory]:
        """根据ID获取标签分类"""
        return self.db.query(TagCategory).filter(TagCategory.id == category_id).first()

    def get_tag_category_by_code(self, category_code: str) -> Optional[TagCategory]:
        """根据代码获取标签分类"""
        return (
            self.db.query(TagCategory)
            .filter(TagCategory.category_code == category_code)
            .first()
        )

    def get_tag_categories(
        self,
        skip: int = 0,
        limit: int = 100,
        parent_id: Optional[int] = None,
        is_active: Optional[bool] = None,
    ) -> Tuple[List[TagCategory], int]:
        """获取标签分类列表"""
        query = self.db.query(TagCategory)

        if parent_id is not None:
            query = query.filter(TagCategory.parent_id == parent_id)

        if is_active is not None:
            query = query.filter(TagCategory.is_active == is_active)

        total = query.count()
        categories = (
            query.order_by(TagCategory.sort_order.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

        return categories, total

    def get_tag_categories_tree(self) -> List[TagCategory]:
        """获取标签分类树形结构"""
        return (
            self.db.query(TagCategory)
            .filter(TagCategory.is_active == True)
            .order_by(TagCategory.level, TagCategory.sort_order.desc())
            .all()
        )

    def update_tag_category(
        self, category_id: int, update_data: TagCategoryUpdate
    ) -> Optional[TagCategory]:
        """更新标签分类"""
        category = self.get_tag_category_by_id(category_id)
        if not category:
            return None

        update_dict = update_data.model_dump(exclude_unset=True)

        # 处理父分类变更
        if "parent_id" in update_dict:
            new_parent_id = update_dict["parent_id"]
            if new_parent_id != category.parent_id:
                # 检查是否会形成循环引用
                if new_parent_id and self._would_create_cycle(category_id, new_parent_id):
                    raise ValueError("Cannot set parent: would create circular reference")

                category.parent_id = new_parent_id
                if new_parent_id:
                    parent = self.get_tag_category_by_id(new_parent_id)
                    if not parent:
                        raise ValueError(f"Parent category with ID {new_parent_id} not found")
                    category.level = parent.level + 1
                else:
                    category.level = 1

        # 更新其他字段
        for field, value in update_dict.items():
            if field not in ["parent_id"]:  # parent_id已经处理过了
                setattr(category, field, value)

        try:
            self.db.commit()
            self.db.refresh(category)
            logging.info(f"Updated tag category: {category.category_code}")
            return category
        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to update tag category: {e}")
            raise ValueError("Failed to update tag category due to database constraints")

    def delete_tag_category(self, category_id: int) -> bool:
        """删除标签分类"""
        category = self.get_tag_category_by_id(category_id)
        if not category:
            return False

        # 检查是否有子分类
        child_count = (
            self.db.query(TagCategory)
            .filter(TagCategory.parent_id == category_id)
            .count()
        )
        if child_count > 0:
            raise ValueError(f"Cannot delete category: {child_count} child categories exist")

        # 检查是否有关联的标签
        from ..models import Tag
        tag_count = self.db.query(Tag).filter(Tag.tag_category_id == category_id).count()
        if tag_count > 0:
            raise ValueError(f"Cannot delete category: {tag_count} tags are using this category")

        try:
            self.db.delete(category)
            self.db.commit()
            logging.info(f"Deleted tag category: {category.category_code}")
            return True
        except Exception as e:
            self.db.rollback()
            logging.error(f"Failed to delete tag category: {e}")
            return False

    def _would_create_cycle(self, category_id: int, new_parent_id: int) -> bool:
        """检查设置新父分类是否会创建循环引用"""
        current_id = new_parent_id
        while current_id:
            if current_id == category_id:
                return True
            parent = self.get_tag_category_by_id(current_id)
            current_id = parent.parent_id if parent else None
        return False
