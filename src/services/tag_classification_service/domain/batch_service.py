"""
批量操作服务模块
负责标签和分类的批量操作，如权重更新、数据清理等
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional

from sqlalchemy import and_, func
from sqlalchemy.orm import Session

from ..models import Tag, UserInterestTag
from .tag_weight_calculator import TagWeightCalculator


class BatchService:
    """批量操作服务"""

    def __init__(self, db: Session):
        """初始化服务"""
        self.db = db
        self.weight_calculator = TagWeightCalculator(db)

    def update_all_computed_weights(self) -> int:
        """批量更新所有标签的综合权重"""
        try:
            updated_count = 0

            # 分批处理，避免一次性加载太多数据
            batch_size = 1000
            offset = 0

            while True:
                tags = (
                    self.db.query(Tag)
                    .filter(Tag.is_active == True)
                    .offset(offset)
                    .limit(batch_size)
                    .all()
                )

                if not tags:
                    break

                # 使用权重计算器批量更新
                batch_updated = self.weight_calculator.batch_update_weights(len(tags))
                updated_count += batch_updated

                offset += batch_size

                logging.info(f"Updated computed weights for {batch_updated} tags (total: {updated_count})")

            logging.info(f"Completed batch update of computed weights for {updated_count} tags")
            return updated_count

        except Exception as e:
            self.db.rollback()
            logging.error(f"Failed to update computed weights: {e}")
            raise

    def batch_create_tags(self, tags_data: List[dict]) -> List[Tag]:
        """批量创建标签"""
        created_tags = []

        try:
            for tag_data in tags_data:
                tag = Tag(**tag_data)
                # 使用权重计算器设置初始权重
                tag.popularity_weight = self.weight_calculator.calculate_popularity_weight(tag)
                tag.quality_weight = self.weight_calculator.calculate_quality_weight(tag)
                tag.temporal_weight = self.weight_calculator.calculate_temporal_weight(tag)
                tag.computed_weight = self.weight_calculator.calculate_computed_weight(tag)

                self.db.add(tag)
                created_tags.append(tag)

            self.db.commit()

            # 刷新所有对象以获取ID
            for tag in created_tags:
                self.db.refresh(tag)

            logging.info(f"Batch created {len(created_tags)} tags")
            return created_tags

        except Exception as e:
            self.db.rollback()
            logging.error(f"Batch create tags failed: {e}")
            raise

    def batch_deactivate_tags(self, tag_ids: List[int]) -> int:
        """批量停用标签"""
        try:
            result = self.db.query(Tag).filter(
                Tag.id.in_(tag_ids)
            ).update(
                {Tag.is_active: False},
                synchronize_session=False
            )

            self.db.commit()
            logging.info(f"Batch deactivated {result} tags")
            return result

        except Exception as e:
            self.db.rollback()
            logging.error(f"Batch deactivate tags failed: {e}")
            return 0

    def batch_update_tag_weights(self, tag_ids: List[int]) -> int:
        """批量更新指定标签的权重"""
        updated_count = 0

        try:
            for tag_id in tag_ids:
                if self.weight_calculator.update_tag_weights(tag_id):
                    updated_count += 1

            logging.info(f"Batch updated weights for {updated_count} tags")
            return updated_count

        except Exception as e:
            logging.error(f"Batch update tag weights failed: {e}")
            return 0

    def update_tag_popularity_weights(self, days: int = 30) -> int:
        """根据使用统计更新标签热度权重"""
        try:
            # 计算过去N天的使用统计
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # 获取使用统计
            usage_stats = (
                self.db.query(
                    Tag.id,
                    func.coalesce(Tag.usage_count, 0).label('total_usage'),
                    func.coalesce(Tag.daily_usage_count, 0).label('daily_usage')
                )
                .filter(Tag.is_active == True)
                .all()
            )
            
            if not usage_stats:
                return 0
            
            # 计算最大使用次数用于归一化
            max_usage = max(stat.total_usage for stat in usage_stats) or 1
            max_daily = max(stat.daily_usage for stat in usage_stats) or 1
            
            updated_count = 0
            
            for stat in usage_stats:
                # 计算热度权重 (0-1之间)
                usage_score = stat.total_usage / max_usage
                daily_score = stat.daily_usage / max_daily
                
                # 综合评分：总使用量70% + 日使用量30%
                popularity_weight = usage_score * 0.7 + daily_score * 0.3
                
                # 更新标签
                tag = self.db.query(Tag).filter(Tag.id == stat.id).first()
                if tag:
                    tag.popularity_weight = min(1.0, popularity_weight)
                    tag.update_computed_weight()
                    updated_count += 1
            
            self.db.commit()
            logging.info(f"Updated popularity weights for {updated_count} tags")
            return updated_count
            
        except Exception as e:
            self.db.rollback()
            logging.error(f"Failed to update popularity weights: {e}")
            raise

    def cleanup_inactive_tags(self, days_threshold: int = 365) -> int:
        """清理长期未使用的标签"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_threshold)
            
            # 查找长期未使用的非系统标签
            inactive_tags = (
                self.db.query(Tag)
                .filter(
                    and_(
                        Tag.is_active == True,
                        Tag.is_system == False,
                        Tag.usage_count == 0,
                        Tag.created_at < cutoff_date
                    )
                )
                .all()
            )
            
            deactivated_count = 0
            
            for tag in inactive_tags:
                # 检查是否有用户兴趣关联
                interest_count = (
                    self.db.query(UserInterestTag)
                    .filter(UserInterestTag.tag_id == tag.id)
                    .count()
                )
                
                if interest_count == 0:
                    tag.is_active = False
                    tag.lifecycle_stage = "deprecated"
                    deactivated_count += 1
            
            self.db.commit()
            logging.info(f"Deactivated {deactivated_count} inactive tags")
            return deactivated_count
            
        except Exception as e:
            self.db.rollback()
            logging.error(f"Failed to cleanup inactive tags: {e}")
            raise

    def reset_daily_usage_counts(self) -> int:
        """重置所有标签的日使用计数"""
        try:
            result = (
                self.db.query(Tag)
                .filter(Tag.daily_usage_count > 0)
                .update({Tag.daily_usage_count: 0})
            )
            
            self.db.commit()
            logging.info(f"Reset daily usage counts for {result} tags")
            return result
            
        except Exception as e:
            self.db.rollback()
            logging.error(f"Failed to reset daily usage counts: {e}")
            raise

    def update_tag_quality_weights(self) -> int:
        """根据用户反馈更新标签质量权重"""
        try:
            updated_count = 0
            
            # 分批处理标签
            batch_size = 1000
            offset = 0
            
            while True:
                tags = (
                    self.db.query(Tag)
                    .filter(Tag.is_active == True)
                    .offset(offset)
                    .limit(batch_size)
                    .all()
                )
                
                if not tags:
                    break
                
                for tag in tags:
                    total_feedback = tag.positive_feedback_count + tag.negative_feedback_count
                    
                    if total_feedback > 0:
                        # 计算质量权重：正面反馈比例
                        quality_score = tag.positive_feedback_count / total_feedback
                        
                        # 考虑反馈数量的置信度
                        confidence = min(1.0, total_feedback / 10)  # 10个反馈达到满置信度
                        
                        # 最终质量权重
                        tag.quality_weight = quality_score * confidence
                        tag.update_computed_weight()
                        updated_count += 1
                    else:
                        # 没有反馈的标签使用默认质量权重
                        tag.quality_weight = 0.5
                        tag.update_computed_weight()
                        updated_count += 1
                
                self.db.commit()
                offset += batch_size
                
                logging.info(f"Updated quality weights for {len(tags)} tags (total: {updated_count})")
            
            logging.info(f"Completed quality weight update for {updated_count} tags")
            return updated_count
            
        except Exception as e:
            self.db.rollback()
            logging.error(f"Failed to update quality weights: {e}")
            raise

    def update_temporal_weights(self, decay_days: int = 30) -> int:
        """更新标签的时效权重"""
        try:
            current_time = datetime.now()
            updated_count = 0
            
            # 分批处理标签
            batch_size = 1000
            offset = 0
            
            while True:
                tags = (
                    self.db.query(Tag)
                    .filter(Tag.is_active == True)
                    .offset(offset)
                    .limit(batch_size)
                    .all()
                )
                
                if not tags:
                    break
                
                for tag in tags:
                    if tag.last_used_at:
                        # 计算距离最后使用的天数
                        days_since_use = (current_time - tag.last_used_at).days
                        
                        # 时效权重：指数衰减
                        temporal_weight = max(0.0, 1.0 - (days_since_use / decay_days))
                    else:
                        # 从未使用的标签
                        temporal_weight = 0.0
                    
                    tag.temporal_weight = temporal_weight
                    tag.update_computed_weight()
                    updated_count += 1
                
                self.db.commit()
                offset += batch_size
                
                logging.info(f"Updated temporal weights for {len(tags)} tags (total: {updated_count})")
            
            logging.info(f"Completed temporal weight update for {updated_count} tags")
            return updated_count
            
        except Exception as e:
            self.db.rollback()
            logging.error(f"Failed to update temporal weights: {e}")
            raise

    def batch_update_all_weights(self) -> dict:
        """批量更新所有类型的权重"""
        try:
            results = {}
            
            # 更新热度权重
            results['popularity'] = self.update_tag_popularity_weights()
            
            # 更新质量权重
            results['quality'] = self.update_tag_quality_weights()
            
            # 更新时效权重
            results['temporal'] = self.update_temporal_weights()
            
            # 最后更新综合权重
            results['computed'] = self.update_all_computed_weights()
            
            logging.info(f"Completed batch weight update: {results}")
            return results
            
        except Exception as e:
            logging.error(f"Failed to batch update weights: {e}")
            raise
