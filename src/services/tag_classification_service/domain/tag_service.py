"""
标签服务模块
负责标签的核心CRUD操作、搜索和权重管理
"""

import json
import logging
from datetime import datetime
from decimal import Decimal
from typing import List, Optional, Tuple

from sqlalchemy import and_, asc, desc, or_
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session, joinedload, selectinload

from ..models import Tag, TagCategory, TagType
from ..schemas import TagCreate, TagListQuery, TagUpdate
from .tag_validator import TagValidator
from .tag_weight_calculator import TagWeightCalculator


class TagService:
    """标签服务"""

    def __init__(self, db: Session):
        """初始化服务"""
        self.db = db
        self.weight_calculator = TagWeightCalculator(db)
        self.validator = TagValidator(db)

    def create_tag(self, tag_data: TagCreate) -> Tag:
        """创建标签"""
        try:
            # 处理父标签和层级
            level = 1
            path = tag_data.tag_code
            if tag_data.parent_id:
                parent = self.get_tag_by_id(tag_data.parent_id)
                if not parent:
                    raise ValueError(f"Parent tag with ID {tag_data.parent_id} not found")
                level = parent.level + 1
                path = f"{parent.path}.{tag_data.tag_code}"

            # 创建临时标签对象进行验证
            temp_tag = Tag(
                tag_name=tag_data.tag_name,
                tag_code=tag_data.tag_code,
                tag_slug=tag_data.tag_slug,
                parent_id=tag_data.parent_id,
                level=level,
                path=path,
                tag_type_id=tag_data.tag_type_id,
                tag_category_id=tag_data.tag_category_id,
                base_weight=Decimal(str(tag_data.base_weight)),
                description=tag_data.description,
                lifecycle_stage=tag_data.lifecycle_stage,
            )

            # 验证标签数据
            validation_errors = self.validator.validate_tag_creation(temp_tag)
            if validation_errors:
                raise ValueError(f"Tag validation failed: {'; '.join(validation_errors)}")
            # 检查代码和slug的唯一性
            if self.db.query(Tag).filter(Tag.tag_code == tag_data.tag_code).first():
                raise ValueError(f"Tag with code '{tag_data.tag_code}' already exists")

            if self.db.query(Tag).filter(Tag.tag_slug == tag_data.tag_slug).first():
                raise ValueError(f"Tag with slug '{tag_data.tag_slug}' already exists")

            # 验证关联对象存在性
            if (
                not self.db.query(TagType)
                .filter(TagType.id == tag_data.tag_type_id)
                .first()
            ):
                raise ValueError(f"Tag type with ID {tag_data.tag_type_id} not found")

            if tag_data.tag_category_id:
                if (
                    not self.db.query(TagCategory)
                    .filter(TagCategory.id == tag_data.tag_category_id)
                    .first()
                ):
                    raise ValueError(
                        f"Tag category with ID {tag_data.tag_category_id} not found"
                    )



            # 处理synonyms字段，转换为JSON字符串以兼容SQLite
            synonyms_json = None
            if tag_data.synonyms:
                synonyms_json = (
                    json.dumps(tag_data.synonyms)
                    if isinstance(tag_data.synonyms, list)
                    else tag_data.synonyms
                )

            # 创建新标签
            tag = Tag(
                tag_name=tag_data.tag_name,
                tag_code=tag_data.tag_code,
                tag_slug=tag_data.tag_slug,
                parent_id=tag_data.parent_id,
                level=level,
                path=path,
                tag_type_id=tag_data.tag_type_id,
                tag_category_id=tag_data.tag_category_id,
                color=tag_data.color,
                icon=tag_data.icon,
                base_weight=Decimal(str(tag_data.base_weight)),
                description=tag_data.description,
                synonyms=synonyms_json,
                lifecycle_stage=tag_data.lifecycle_stage,
                is_system=False,  # 用户创建的标签默认不是系统标签
            )

            # 使用权重计算器计算综合权重
            tag.popularity_weight = self.weight_calculator.calculate_popularity_weight(tag)
            tag.quality_weight = self.weight_calculator.calculate_quality_weight(tag)
            tag.temporal_weight = self.weight_calculator.calculate_temporal_weight(tag)
            tag.computed_weight = self.weight_calculator.calculate_computed_weight(tag)

            self.db.add(tag)
            self.db.commit()
            self.db.refresh(tag)

            logging.info(f"Created tag: {tag.tag_code}")
            return tag

        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to create tag: {e}")
            raise ValueError("Failed to create tag due to database constraints")

    def get_tag_by_id(
        self, tag_id: int, include_relations: bool = False
    ) -> Optional[Tag]:
        """根据ID获取标签"""
        query = self.db.query(Tag)

        if include_relations:
            query = query.options(
                joinedload(Tag.tag_type),
                joinedload(Tag.tag_category),
                joinedload(Tag.parent),
                selectinload(Tag.children),
            )

        return query.filter(Tag.id == tag_id).first()

    def get_tag_by_code(self, tag_code: str) -> Optional[Tag]:
        """根据代码获取标签"""
        return self.db.query(Tag).filter(Tag.tag_code == tag_code).first()

    def search_tags(self, query: str, limit: int = 10) -> List[Tag]:
        """搜索标签"""
        return (
            self.db.query(Tag)
            .filter(
                and_(
                    Tag.is_active == True,
                    or_(
                        Tag.tag_name.ilike(f"%{query}%"),
                        Tag.tag_code.ilike(f"%{query}%"),
                        Tag.description.ilike(f"%{query}%"),
                    ),
                )
            )
            .order_by(Tag.computed_weight.desc(), Tag.usage_count.desc())
            .limit(limit)
            .all()
        )

    def get_popular_tags(self, limit: int = 20, time_range: str = "all") -> List[Tag]:
        """获取热门标签"""
        query = self.db.query(Tag).filter(Tag.is_active == True)

        if time_range == "daily":
            query = query.order_by(Tag.daily_usage_count.desc())
        else:
            query = query.order_by(Tag.usage_count.desc())

        return query.limit(limit).all()

    def get_tags(
        self, query: TagListQuery, skip: int = 0, limit: int = 100
    ) -> Tuple[List[Tag], int]:
        """获取标签列表"""
        db_query = self.db.query(Tag)

        # 应用过滤条件
        if query.search:
            db_query = db_query.filter(
                or_(
                    Tag.tag_name.ilike(f"%{query.search}%"),
                    Tag.tag_code.ilike(f"%{query.search}%"),
                    Tag.description.ilike(f"%{query.search}%"),
                )
            )

        if query.tag_type_id:
            db_query = db_query.filter(Tag.tag_type_id == query.tag_type_id)

        if query.tag_category_id:
            db_query = db_query.filter(Tag.tag_category_id == query.tag_category_id)

        if query.parent_id is not None:
            db_query = db_query.filter(Tag.parent_id == query.parent_id)

        if query.lifecycle_stage:
            db_query = db_query.filter(Tag.lifecycle_stage == query.lifecycle_stage)

        if query.is_active is not None:
            db_query = db_query.filter(Tag.is_active == query.is_active)

        total = db_query.count()

        # 应用排序
        if query.order_by == "weight":
            order_column = Tag.computed_weight
        elif query.order_by == "usage":
            order_column = Tag.usage_count
        elif query.order_by == "name":
            order_column = Tag.tag_name
        else:
            order_column = Tag.created_at

        if query.order_direction == "asc":
            db_query = db_query.order_by(asc(order_column))
        else:
            db_query = db_query.order_by(desc(order_column))

        tags = db_query.offset(skip).limit(limit).all()

        return tags, total

    def update_tag(self, tag_id: int, update_data: TagUpdate) -> Optional[Tag]:
        """更新标签"""
        tag = self.get_tag_by_id(tag_id)
        if not tag:
            return None

        # 处理路径更新
        update_dict = update_data.model_dump(exclude_unset=True)

        if "parent_id" in update_dict:
            new_parent_id = update_dict["parent_id"]
            if new_parent_id != tag.parent_id:
                tag.parent_id = new_parent_id  # 首先设置parent_id
                if new_parent_id:
                    parent = self.get_tag_by_id(new_parent_id)
                    if not parent:
                        raise ValueError(
                            f"Parent tag with ID {new_parent_id} not found"
                        )
                    tag.level = parent.level + 1
                    tag.path = f"{parent.path}.{tag.tag_code}"
                else:
                    tag.level = 1
                    tag.path = tag.tag_code

        # 更新其他字段
        for field, value in update_dict.items():
            if field not in ["parent_id"]:  # parent_id已经处理过了
                setattr(tag, field, value)

        # 重新计算综合权重
        self.weight_calculator.update_tag_weights(tag.id)

        try:
            self.db.commit()
            self.db.refresh(tag)
            logging.info(f"Updated tag: {tag.tag_code}")
            return tag
        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to update tag: {e}")
            raise ValueError("Failed to update tag due to database constraints")

    def delete_tag(self, tag_id: int) -> bool:
        """删除标签"""
        tag = self.get_tag_by_id(tag_id)
        if not tag:
            return False

        # 检查是否为系统标签
        if tag.is_system:
            raise ValueError("Cannot delete system tag")

        # 检查是否有子标签
        child_count = self.db.query(Tag).filter(Tag.parent_id == tag_id).count()
        if child_count > 0:
            raise ValueError(f"Cannot delete tag: {child_count} child tags exist")

        try:
            self.db.delete(tag)
            self.db.commit()
            logging.info(f"Deleted tag: {tag.tag_code}")
            return True
        except Exception as e:
            self.db.rollback()
            logging.error(f"Failed to delete tag: {e}")
            return False

    def increment_tag_usage(self, tag_id: int) -> bool:
        """增加标签使用次数"""
        try:
            tag = self.get_tag_by_id(tag_id)
            if not tag:
                return False

            tag.usage_count += 1
            tag.daily_usage_count += 1
            tag.last_used_at = datetime.now()

            # 重新计算权重（使用次数可能影响热度权重）
            self.weight_calculator.update_tag_weights(tag.id)

            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            logging.error(f"Failed to increment tag usage: {e}")
            return False

    def get_tag_weight_analysis(self, tag_id: int) -> Optional[dict]:
        """获取标签权重分析"""
        return self.weight_calculator.get_weight_analysis(tag_id)

    def update_tag_weights(self, tag_id: int) -> bool:
        """更新标签权重"""
        return self.weight_calculator.update_tag_weights(tag_id)
