"""
标签缓存服务
实现多层缓存策略，提升查询性能
"""

import json
import logging
from typing import Any, Dict, List, Optional

from sqlalchemy import func
from sqlalchemy.orm import Session

from ..models import Tag, UserInterestTag


class TagCacheService:
    """标签缓存服务"""

    def __init__(self, redis_client, db: Session):
        """初始化缓存服务"""
        self.redis = redis_client
        self.db = db
        self.cache_config = {
            'popular_tags_ttl': 3600,      # 1小时
            'user_profile_ttl': 7200,      # 2小时
            'tag_tree_ttl': 1800,          # 30分钟
            'search_results_ttl': 900,     # 15分钟
            'tag_details_ttl': 3600,       # 1小时
            'classification_tree_ttl': 1800, # 30分钟
            'user_interests_ttl': 1800,    # 30分钟
            'tag_stats_ttl': 600,          # 10分钟
        }

    def get_popular_tags(self, limit: int = 100, time_range: str = "all") -> List[Tag]:
        """获取热门标签（带缓存）"""
        cache_key = f"popular_tags:{limit}:{time_range}"
        cached = self.redis.get(cache_key)

        if cached:
            try:
                tag_data = json.loads(cached)
                return self._deserialize_tags(tag_data)
            except (json.JSONDecodeError, KeyError) as e:
                logging.warning(f"Failed to deserialize cached popular tags: {e}")

        # 从数据库获取
        tags = self._fetch_popular_tags_from_db(limit, time_range)
        
        # 缓存结果
        try:
            serialized_tags = self._serialize_tags(tags)
            self.redis.setex(
                cache_key,
                self.cache_config['popular_tags_ttl'],
                json.dumps(serialized_tags)
            )
        except Exception as e:
            logging.error(f"Failed to cache popular tags: {e}")

        return tags

    def get_user_profile(self, user_id: int) -> Optional[dict]:
        """获取用户画像（带缓存）"""
        cache_key = f"user_profile:{user_id}"
        cached = self.redis.get(cache_key)

        if cached:
            try:
                return json.loads(cached)
            except json.JSONDecodeError as e:
                logging.warning(f"Failed to deserialize cached user profile: {e}")

        # 从数据库获取
        profile = self._build_user_profile(user_id)
        if profile:
            try:
                self.redis.setex(
                    cache_key,
                    self.cache_config['user_profile_ttl'],
                    json.dumps(profile, default=str)
                )
            except Exception as e:
                logging.error(f"Failed to cache user profile: {e}")

        return profile

    def get_tag_details(self, tag_id: int) -> Optional[dict]:
        """获取标签详情（带缓存）"""
        cache_key = f"tag_details:{tag_id}"
        cached = self.redis.get(cache_key)

        if cached:
            try:
                return json.loads(cached)
            except json.JSONDecodeError as e:
                logging.warning(f"Failed to deserialize cached tag details: {e}")

        # 从数据库获取
        tag_details = self._fetch_tag_details(tag_id)
        if tag_details:
            try:
                self.redis.setex(
                    cache_key,
                    self.cache_config['tag_details_ttl'],
                    json.dumps(tag_details, default=str)
                )
            except Exception as e:
                logging.error(f"Failed to cache tag details: {e}")

        return tag_details

    def get_user_interests(self, user_id: int, limit: int = 50) -> List[dict]:
        """获取用户兴趣（带缓存）"""
        cache_key = f"user_interests:{user_id}:{limit}"
        cached = self.redis.get(cache_key)

        if cached:
            try:
                return json.loads(cached)
            except json.JSONDecodeError as e:
                logging.warning(f"Failed to deserialize cached user interests: {e}")

        # 从数据库获取
        interests = self._fetch_user_interests(user_id, limit)
        
        try:
            self.redis.setex(
                cache_key,
                self.cache_config['user_interests_ttl'],
                json.dumps(interests, default=str)
            )
        except Exception as e:
            logging.error(f"Failed to cache user interests: {e}")

        return interests

    def get_tag_search_results(self, query: str, limit: int = 20) -> List[dict]:
        """获取标签搜索结果（带缓存）"""
        cache_key = f"tag_search:{hash(query)}:{limit}"
        cached = self.redis.get(cache_key)

        if cached:
            try:
                return json.loads(cached)
            except json.JSONDecodeError as e:
                logging.warning(f"Failed to deserialize cached search results: {e}")

        # 从数据库搜索
        results = self._search_tags_in_db(query, limit)
        
        try:
            self.redis.setex(
                cache_key,
                self.cache_config['search_results_ttl'],
                json.dumps(results, default=str)
            )
        except Exception as e:
            logging.error(f"Failed to cache search results: {e}")

        return results

    def get_tag_stats(self) -> dict:
        """获取标签统计信息（带缓存）"""
        cache_key = "tag_stats:global"
        cached = self.redis.get(cache_key)

        if cached:
            try:
                return json.loads(cached)
            except json.JSONDecodeError as e:
                logging.warning(f"Failed to deserialize cached tag stats: {e}")

        # 从数据库计算
        stats = self._calculate_tag_stats()
        
        try:
            self.redis.setex(
                cache_key,
                self.cache_config['tag_stats_ttl'],
                json.dumps(stats, default=str)
            )
        except Exception as e:
            logging.error(f"Failed to cache tag stats: {e}")

        return stats

    def invalidate_tag_cache(self, tag_id: int):
        """失效标签相关缓存"""
        patterns = [
            f"popular_tags:*",
            f"tag_tree:*",
            f"search_results:*",
            f"tag_details:{tag_id}",
            f"tag_stats:*"
        ]

        for pattern in patterns:
            try:
                keys = self.redis.keys(pattern)
                if keys:
                    self.redis.delete(*keys)
                    logging.info(f"Invalidated cache pattern: {pattern}")
            except Exception as e:
                logging.error(f"Failed to invalidate cache pattern {pattern}: {e}")

    def invalidate_user_cache(self, user_id: int):
        """失效用户相关缓存"""
        patterns = [
            f"user_profile:{user_id}",
            f"user_interests:{user_id}:*",
            f"user_recommendations:{user_id}:*"
        ]

        for pattern in patterns:
            try:
                keys = self.redis.keys(pattern)
                if keys:
                    self.redis.delete(*keys)
                    logging.info(f"Invalidated cache pattern: {pattern}")
            except Exception as e:
                logging.error(f"Failed to invalidate cache pattern {pattern}: {e}")

    def invalidate_all_cache(self):
        """清空所有缓存"""
        try:
            keys = self.redis.keys("*")
            if keys:
                self.redis.delete(*keys)
                logging.info("Invalidated all cache")
        except Exception as e:
            logging.error(f"Failed to invalidate all cache: {e}")

    def warm_up_cache(self):
        """预热缓存"""
        try:
            # 预热热门标签
            self.get_popular_tags(100, "all")
            self.get_popular_tags(50, "daily")
            
            # 预热标签统计
            self.get_tag_stats()
            
            logging.info("Cache warm-up completed")
        except Exception as e:
            logging.error(f"Cache warm-up failed: {e}")

    # 私有方法 - 数据库操作
    def _fetch_popular_tags_from_db(self, limit: int, time_range: str) -> List[Tag]:
        """从数据库获取热门标签"""
        query = self.db.query(Tag).filter(Tag.is_active == True)

        if time_range == "daily":
            query = query.order_by(Tag.daily_usage_count.desc())
        else:
            query = query.order_by(Tag.usage_count.desc())

        return query.limit(limit).all()

    def _build_user_profile(self, user_id: int) -> Optional[dict]:
        """构建用户画像"""
        # 获取用户兴趣标签
        interests = (
            self.db.query(UserInterestTag, Tag)
            .join(Tag, UserInterestTag.tag_id == Tag.id)
            .filter(UserInterestTag.user_id == user_id)
            .order_by(UserInterestTag.computed_interest.desc())
            .limit(20)
            .all()
        )

        if not interests:
            return None

        # 构建画像数据
        profile = {
            'user_id': user_id,
            'top_interests': [],
            'interest_categories': {},
            'behavioral_patterns': {},
            'activity_summary': {}
        }

        total_clicks = 0
        total_shares = 0
        total_view_time = 0

        for interest, tag in interests:
            profile['top_interests'].append({
                'tag_id': tag.id,
                'tag_name': tag.tag_name,
                'tag_code': tag.tag_code,
                'interest_score': float(interest.computed_interest),
                'click_count': interest.click_count,
                'view_time': interest.view_time_seconds,
                'share_count': interest.share_count
            })

            total_clicks += interest.click_count
            total_shares += interest.share_count
            total_view_time += interest.view_time_seconds

        profile['activity_summary'] = {
            'total_clicks': total_clicks,
            'total_shares': total_shares,
            'total_view_time_minutes': total_view_time // 60,
            'engagement_level': self._calculate_engagement_level(total_clicks, total_shares)
        }

        return profile

    def _fetch_tag_details(self, tag_id: int) -> Optional[dict]:
        """获取标签详情"""
        tag = self.db.query(Tag).filter(Tag.id == tag_id).first()
        if not tag:
            return None

        return {
            'id': tag.id,
            'tag_name': tag.tag_name,
            'tag_code': tag.tag_code,
            'tag_slug': tag.tag_slug,
            'description': tag.description,
            'level': tag.level,
            'path': tag.path,
            'usage_count': tag.usage_count,
            'daily_usage_count': tag.daily_usage_count,
            'computed_weight': float(tag.computed_weight) if tag.computed_weight else 0,
            'is_active': tag.is_active,
            'lifecycle_stage': tag.lifecycle_stage,
            'created_at': tag.created_at.isoformat() if tag.created_at else None
        }

    def _fetch_user_interests(self, user_id: int, limit: int) -> List[dict]:
        """获取用户兴趣"""
        interests = (
            self.db.query(UserInterestTag, Tag)
            .join(Tag, UserInterestTag.tag_id == Tag.id)
            .filter(UserInterestTag.user_id == user_id)
            .order_by(UserInterestTag.computed_interest.desc())
            .limit(limit)
            .all()
        )

        return [
            {
                'tag_id': tag.id,
                'tag_name': tag.tag_name,
                'tag_code': tag.tag_code,
                'interest_score': float(interest.computed_interest),
                'explicit_interest': float(interest.explicit_interest),
                'implicit_interest': float(interest.implicit_interest)
            }
            for interest, tag in interests
        ]

    def _search_tags_in_db(self, query: str, limit: int) -> List[dict]:
        """在数据库中搜索标签"""
        from sqlalchemy import or_
        
        tags = (
            self.db.query(Tag)
            .filter(
                Tag.is_active == True,
                or_(
                    Tag.tag_name.ilike(f"%{query}%"),
                    Tag.tag_code.ilike(f"%{query}%"),
                    Tag.description.ilike(f"%{query}%")
                )
            )
            .order_by(Tag.computed_weight.desc())
            .limit(limit)
            .all()
        )

        return [
            {
                'id': tag.id,
                'tag_name': tag.tag_name,
                'tag_code': tag.tag_code,
                'description': tag.description,
                'computed_weight': float(tag.computed_weight) if tag.computed_weight else 0
            }
            for tag in tags
        ]

    def _calculate_tag_stats(self) -> dict:
        """计算标签统计信息"""
        total_tags = self.db.query(Tag).count()
        active_tags = self.db.query(Tag).filter(Tag.is_active == True).count()
        system_tags = self.db.query(Tag).filter(Tag.is_system == True).count()
        
        avg_usage = (
            self.db.query(func.avg(Tag.usage_count))
            .filter(Tag.is_active == True)
            .scalar() or 0
        )

        return {
            'total_tags': total_tags,
            'active_tags': active_tags,
            'inactive_tags': total_tags - active_tags,
            'system_tags': system_tags,
            'user_tags': total_tags - system_tags,
            'average_usage': float(avg_usage)
        }

    def _serialize_tags(self, tags: List[Tag]) -> List[dict]:
        """序列化标签列表"""
        return [
            {
                'id': tag.id,
                'tag_name': tag.tag_name,
                'tag_code': tag.tag_code,
                'usage_count': tag.usage_count,
                'computed_weight': float(tag.computed_weight) if tag.computed_weight else 0
            }
            for tag in tags
        ]

    def _deserialize_tags(self, tag_data: List[dict]) -> List[Tag]:
        """反序列化标签列表（简化版本，仅用于缓存）"""
        # 注意：这里返回的是简化的Tag对象，仅包含基本信息
        # 实际使用中可能需要从数据库重新获取完整对象
        tags = []
        for data in tag_data:
            tag = Tag()
            tag.id = data['id']
            tag.tag_name = data['tag_name']
            tag.tag_code = data['tag_code']
            tag.usage_count = data['usage_count']
            tag.computed_weight = data['computed_weight']
            tags.append(tag)
        return tags

    def _calculate_engagement_level(self, clicks: int, shares: int) -> str:
        """计算用户参与度等级"""
        total_engagement = clicks + shares * 5  # 分享权重更高
        
        if total_engagement > 100:
            return "high"
        elif total_engagement > 20:
            return "medium"
        else:
            return "low"

    def get_cache_stats(self) -> dict:
        """获取缓存统计信息"""
        try:
            info = self.redis.info()
            return {
                'used_memory': info.get('used_memory_human', 'N/A'),
                'connected_clients': info.get('connected_clients', 0),
                'total_commands_processed': info.get('total_commands_processed', 0),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0),
                'hit_rate': self._calculate_hit_rate(info)
            }
        except Exception as e:
            logging.error(f"Failed to get cache stats: {e}")
            return {}

    def _calculate_hit_rate(self, info: dict) -> float:
        """计算缓存命中率"""
        hits = info.get('keyspace_hits', 0)
        misses = info.get('keyspace_misses', 0)
        total = hits + misses
        return (hits / total * 100) if total > 0 else 0
