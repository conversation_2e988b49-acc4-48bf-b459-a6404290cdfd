"""
标签和分类服务API路由（C端）
定义标签和分类相关的查询HTTP接口
"""

from math import ceil
from typing import Annotated, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Request, status

from ..user_service.dependencies import get_current_active_user
from ..user_service.models import User
from .dependencies import (
    get_classification_service,
    get_tag_category_service,
    get_tag_service,
    get_tag_type_service,
)
from .schemas import (  # 标签类型相关; 标签分类相关; 标签相关; 分类相关; 通用响应
    ClassificationDimensionResponse,
    ClassificationValueResponse,
    PageResponse,
    TagCategoryResponse,
    TagResponse,
    TagTypeResponse,
    UserClassificationPreferenceResponse,
)
from .service import (
    ClassificationService,
    TagCategoryService,
    TagService,
    TagTypeService,
)

# 创建路由器
router = APIRouter()


def get_client_info(request: Request) -> tuple[str, str]:
    """
    获取客户端信息

    Args:
        request: FastAPI请求对象

    Returns:
        IP地址和用户代理的元组
    """
    ip_address = request.client.host if request.client else None
    user_agent = request.headers.get("User-Agent", "")
    return ip_address, user_agent


# ==================== 标签类型查询接口 ====================


@router.get(
    "/tags/types",
    response_model=PageResponse,
    summary="获取标签类型列表",
    description="获取标签类型分页列表",
)
async def get_tag_types(
    tag_type_service: Annotated[TagTypeService, Depends(get_tag_type_service)],
    page: int = Query(default=1, ge=1, description="页码"),
    size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    is_active: Optional[bool] = Query(default=None, description="是否只获取活跃类型"),
):
    """获取标签类型列表"""
    try:
        skip = (page - 1) * size
        tag_types, total = tag_type_service.get_tag_types(skip, size, is_active)

        return PageResponse(
            items=[TagTypeResponse.model_validate(tt) for tt in tag_types],
            total=total,
            page=page,
            size=size,
            pages=ceil(total / size),
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get tag types: {str(e)}",
        )


@router.get(
    "/tags/types/{tag_type_id}",
    response_model=TagTypeResponse,
    summary="获取标签类型详情",
    description="根据ID获取标签类型详情",
)
async def get_tag_type(
    tag_type_id: int,
    tag_type_service: Annotated[TagTypeService, Depends(get_tag_type_service)],
):
    """获取标签类型详情"""
    tag_type = tag_type_service.get_tag_type_by_id(tag_type_id)

    if not tag_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Tag type not found"
        )

    return TagTypeResponse.model_validate(tag_type)


# ==================== 标签分类查询接口 ====================


@router.get(
    "/tags/categories/tree",
    response_model=List[TagCategoryResponse],
    summary="获取标签分类树",
    description="获取标签分类的树形结构",
)
async def get_tag_categories_tree(
    category_service: Annotated[TagCategoryService, Depends(get_tag_category_service)],
    is_active: Optional[bool] = Query(default=None, description="是否只获取活跃分类"),
):
    """获取标签分类树形结构"""
    try:
        categories = category_service.get_tag_categories_tree(is_active)
        return [TagCategoryResponse.model_validate(cat) for cat in categories]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get tag categories tree: {str(e)}",
        )


@router.get(
    "/tags/categories",
    response_model=PageResponse,
    summary="获取标签分类列表",
    description="获取标签分类分页列表",
)
async def get_tag_categories(
    category_service: Annotated[TagCategoryService, Depends(get_tag_category_service)],
    page: int = Query(default=1, ge=1, description="页码"),
    size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    parent_id: Optional[int] = Query(default=None, description="父分类ID"),
    is_active: Optional[bool] = Query(default=None, description="是否只获取活跃分类"),
):
    """获取标签分类列表"""
    try:
        skip = (page - 1) * size
        categories, total = category_service.get_tag_categories(
            skip, size, parent_id, is_active
        )

        return PageResponse(
            items=[TagCategoryResponse.model_validate(cat) for cat in categories],
            total=total,
            page=page,
            size=size,
            pages=ceil(total / size),
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get tag categories: {str(e)}",
        )


@router.get(
    "/tags/categories/{category_id}",
    response_model=TagCategoryResponse,
    summary="获取标签分类详情",
    description="根据ID获取标签分类详情",
)
async def get_tag_category(
    category_id: int,
    category_service: Annotated[TagCategoryService, Depends(get_tag_category_service)],
):
    """获取标签分类详情"""
    category = category_service.get_tag_category_by_id(category_id)

    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Tag category not found"
        )

    return TagCategoryResponse.model_validate(category)


# ==================== 标签查询接口 ====================


@router.get(
    "/tags",
    response_model=PageResponse,
    summary="获取标签列表",
    description="根据查询条件获取标签分页列表",
)
async def get_tags(
    tag_service: Annotated[TagService, Depends(get_tag_service)],
    page: int = Query(default=1, ge=1, description="页码"),
    size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(default=None, description="搜索关键词"),
    tag_type_id: Optional[int] = Query(default=None, description="标签类型ID"),
    tag_category_id: Optional[int] = Query(default=None, description="标签分类ID"),
    parent_id: Optional[int] = Query(default=None, description="父标签ID"),
    lifecycle_stage: Optional[str] = Query(default=None, description="生命周期阶段"),
    is_active: Optional[bool] = Query(default=None, description="是否启用"),
    order_by: str = Query(default="created_at", description="排序字段"),
    order_direction: str = Query(default="desc", description="排序方向"),
):
    """获取标签列表"""
    try:
        skip = (page - 1) * size
        tags, total = tag_service.get_tags(
            skip=skip,
            limit=size,
            search=search,
            tag_type_id=tag_type_id,
            tag_category_id=tag_category_id,
            parent_id=parent_id,
            lifecycle_stage=lifecycle_stage,
            is_active=is_active,
            order_by=order_by,
            order_direction=order_direction,
        )

        return PageResponse(
            items=[TagResponse.model_validate(tag) for tag in tags],
            total=total,
            page=page,
            size=size,
            pages=ceil(total / size),
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get tags: {str(e)}",
        )


@router.get(
    "/tags/search",
    response_model=List[TagResponse],
    summary="搜索标签",
    description="根据关键词搜索标签",
)
async def search_tags(
    tag_service: Annotated[TagService, Depends(get_tag_service)],
    q: str = Query(..., description="搜索关键词"),
    limit: int = Query(default=10, ge=1, le=50, description="返回数量限制"),
):
    """搜索标签"""
    try:
        tags = tag_service.search_tags(q, limit)
        return [TagResponse.model_validate(tag) for tag in tags]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search tags: {str(e)}",
        )


@router.get(
    "/tags/popular",
    response_model=List[TagResponse],
    summary="获取热门标签",
    description="获取热门标签列表",
)
async def get_popular_tags(
    tag_service: Annotated[TagService, Depends(get_tag_service)],
    limit: int = Query(default=20, ge=1, le=100, description="返回数量限制"),
    time_range: str = Query(
        default="all", description="时间范围: daily/weekly/monthly/all"
    ),
):
    """获取热门标签"""
    try:
        tags = tag_service.get_popular_tags(limit, time_range)
        return [TagResponse.model_validate(tag) for tag in tags]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get popular tags: {str(e)}",
        )


@router.get(
    "/tags/{tag_id}",
    response_model=TagResponse,
    summary="获取标签详情",
    description="根据ID获取标签详情",
)
async def get_tag(
    tag_id: int,
    tag_service: Annotated[TagService, Depends(get_tag_service)],
    include_relations: bool = Query(default=False, description="是否包含关联对象"),
):
    """获取标签详情"""
    tag = tag_service.get_tag_by_id(tag_id, include_relations=include_relations)

    if not tag:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Tag not found"
        )

    return TagResponse.model_validate(tag)


# ==================== 分类维度查询接口 ====================


@router.get(
    "/classifications/dimensions",
    response_model=PageResponse,
    summary="获取分类维度列表",
    description="获取分类维度分页列表",
)
async def get_classification_dimensions(
    classification_service: Annotated[
        ClassificationService, Depends(get_classification_service)
    ],
    page: int = Query(default=1, ge=1, description="页码"),
    size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    is_active: Optional[bool] = Query(default=None, description="是否只获取活跃维度"),
):
    """获取分类维度列表"""
    try:
        skip = (page - 1) * size
        dimensions, total = classification_service.get_classification_dimensions(
            skip, size, is_active
        )

        return PageResponse(
            items=[
                ClassificationDimensionResponse.model_validate(d) for d in dimensions
            ],
            total=total,
            page=page,
            size=size,
            pages=ceil(total / size),
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get classification dimensions: {str(e)}",
        )


@router.get(
    "/classifications/dimensions/{dimension_id}",
    response_model=ClassificationDimensionResponse,
    summary="获取分类维度详情",
    description="根据ID获取分类维度详情",
)
async def get_classification_dimension(
    dimension_id: int,
    classification_service: Annotated[
        ClassificationService, Depends(get_classification_service)
    ],
):
    """获取分类维度详情"""
    dimension = classification_service.get_classification_dimension_by_id(dimension_id)

    if not dimension:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Classification dimension not found",
        )

    return ClassificationDimensionResponse.model_validate(dimension)


# ==================== 分类值查询接口 ====================


@router.get(
    "/classifications/dimensions/{dimension_id}/values",
    response_model=PageResponse,
    summary="获取分类值列表",
    description="获取指定维度的分类值分页列表，支持模糊搜索",
)
async def get_classification_values(
    dimension_id: int,
    classification_service: Annotated[
        ClassificationService, Depends(get_classification_service)
    ],
    page: int = Query(default=1, ge=1, description="页码"),
    size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    parent_id: Optional[int] = Query(default=None, description="父分类值ID"),
    is_active: Optional[bool] = Query(default=None, description="是否只获取活跃值"),
    search: Optional[str] = Query(default=None, description="搜索关键词，支持按display_name模糊搜索"),
):
    """获取分类值列表"""
    try:
        skip = (page - 1) * size
        values, total = classification_service.get_classification_values(
            dimension_id=dimension_id, skip=skip, limit=size, parent_id=parent_id, is_active=is_active, search=search
        )

        return PageResponse(
            items=[ClassificationValueResponse.model_validate(v) for v in values],
            total=total,
            page=page,
            size=size,
            pages=ceil(total / size),
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get classification values: {str(e)}",
        )


@router.get(
    "/classifications/dimensions/{dimension_id}/values/tree",
    response_model=List[ClassificationValueResponse],
    summary="获取分类值树",
    description="获取指定维度的分类值树形结构",
)
async def get_classification_values_tree(
    dimension_id: int,
    classification_service: Annotated[
        ClassificationService, Depends(get_classification_service)
    ],
    is_active: Optional[bool] = Query(default=None, description="是否只获取活跃值"),
):
    """获取分类值树形结构"""
    try:
        values = classification_service.get_classification_values_tree(
            dimension_id, is_active
        )
        return [ClassificationValueResponse.model_validate(v) for v in values]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get classification values tree: {str(e)}",
        )


@router.get(
    "/classifications/values/{value_id}",
    response_model=ClassificationValueResponse,
    summary="获取分类值详情",
    description="根据ID获取分类值详情",
)
async def get_classification_value(
    value_id: int,
    classification_service: Annotated[
        ClassificationService, Depends(get_classification_service)
    ],
):
    """获取分类值详情"""
    value = classification_service.get_classification_value_by_id(value_id)

    if not value:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Classification value not found",
        )

    return ClassificationValueResponse.model_validate(value)


# ==================== 用户分类偏好查询接口 ====================


@router.get(
    "/my/classification-preferences",
    response_model=List[UserClassificationPreferenceResponse],
    summary="获取我的分类偏好",
    description="获取当前用户的分类偏好列表",
)
async def get_my_classification_preferences(
    current_user: Annotated[User, Depends(get_current_active_user)],
    classification_service: Annotated[
        ClassificationService, Depends(get_classification_service)
    ],
    dimension_id: Optional[int] = Query(default=None, description="分类维度ID"),
):
    """获取我的分类偏好"""
    try:
        # 检查用户权限：只能查看自己的偏好
        preferences = classification_service.get_user_classification_preferences(
            current_user.id, dimension_id
        )

        return [
            UserClassificationPreferenceResponse.model_validate(pref)
            for pref in preferences
        ]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get user classification preferences: {str(e)}",
        )


@router.get(
    "/recommendations/tags",
    response_model=List[TagResponse],
    summary="获取标签推荐",
    description="基于用户偏好获取标签推荐",
)
async def get_tag_recommendations(
    current_user: Annotated[User, Depends(get_current_active_user)],
    tag_service: Annotated[TagService, Depends(get_tag_service)],
    limit: int = Query(default=10, ge=1, le=50, description="推荐数量"),
):
    """获取标签推荐"""
    try:
        # 基于用户偏好获取推荐标签
        tags = tag_service.get_recommended_tags_for_user(current_user.id, limit)
        return [TagResponse.model_validate(tag) for tag in tags]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get tag recommendations: {str(e)}",
        )
