"""
用户画像相关模型 - 结构化存储
将JSONB字段结构化为具体表，提高查询性能和数据一致性
"""

from datetime import date, datetime
from decimal import Decimal

from sqlalchemy import (
    BigInteger, Boolean, Column, Date, DateTime, ForeignKey, Index, Integer,
    String, Text, TIMESTAMP, func
)
from sqlalchemy import DECIMAL
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()


class UserInterestProfile(Base):
    """用户兴趣画像表"""
    
    __tablename__ = "user_interest_profiles"

    id = Column(BigInteger, primary_key=True, comment="画像ID")
    user_id = Column(BigInteger, nullable=False, index=True, comment="用户ID")
    profile_date = Column(Date, nullable=False, comment="画像日期")

    # 兴趣向量化表示
    interest_vector = Column(Text, comment="用户兴趣向量，用于相似度计算")

    # 行为统计
    total_clicks = Column(Integer, default=0, comment="总点击次数")
    total_views = Column(Integer, default=0, comment="总浏览次数")
    total_shares = Column(Integer, default=0, comment="总分享次数")
    total_bookmarks = Column(Integer, default=0, comment="总收藏次数")
    total_comments = Column(Integer, default=0, comment="总评论次数")
    
    # 时间统计
    avg_view_duration = Column(DECIMAL(10, 2), default=0, comment="平均浏览时长（秒）")
    total_view_time = Column(Integer, default=0, comment="总浏览时间（秒）")
    
    # 活跃度评分
    activity_score = Column(DECIMAL(3, 2), default=0, comment="活跃度评分 (0-1)")
    engagement_score = Column(DECIMAL(3, 2), default=0, comment="参与度评分 (0-1)")
    consistency_score = Column(DECIMAL(3, 2), default=0, comment="一致性评分 (0-1)")
    
    # 兴趣多样性
    interest_diversity = Column(DECIMAL(3, 2), default=0, comment="兴趣多样性 (0-1)")
    primary_interests_count = Column(Integer, default=0, comment="主要兴趣数量")
    
    # 时间模式
    most_active_hour = Column(Integer, comment="最活跃小时 (0-23)")
    most_active_day = Column(Integer, comment="最活跃星期 (1-7)")
    
    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")

    # 索引定义
    __table_args__ = (
        Index("idx_user_interest_profiles_user_date", "user_id", "profile_date"),
        Index("idx_user_interest_profiles_date", "profile_date"),
        Index("idx_user_interest_profiles_activity", "activity_score"),
        Index("idx_user_interest_profiles_engagement", "engagement_score"),
    )

    def __repr__(self):
        return f"<UserInterestProfile(user_id={self.user_id}, date={self.profile_date})>"


class UserBehaviorPattern(Base):
    """用户行为模式表"""
    
    __tablename__ = "user_behavior_patterns"

    id = Column(BigInteger, primary_key=True, comment="模式ID")
    user_id = Column(BigInteger, nullable=False, index=True, comment="用户ID")

    pattern_type = Column(String(50), nullable=False, comment="模式类型：click/view/share/search/time")
    pattern_name = Column(String(100), nullable=False, comment="模式名称")
    pattern_description = Column(Text, comment="模式描述")

    # 模式特征
    frequency_score = Column(DECIMAL(3, 2), default=0, comment="频率得分 (0-1)")
    consistency_score = Column(DECIMAL(3, 2), default=0, comment="一致性得分 (0-1)")
    recency_score = Column(DECIMAL(3, 2), default=0, comment="时效性得分 (0-1)")
    intensity_score = Column(DECIMAL(3, 2), default=0, comment="强度得分 (0-1)")

    # 模式参数（JSON格式存储具体参数）
    pattern_parameters = Column(Text, comment="模式参数，JSON格式")
    
    # 检测信息
    detection_date = Column(Date, nullable=False, comment="检测日期")
    confidence_score = Column(DECIMAL(3, 2), default=0, comment="置信度 (0-1)")
    sample_size = Column(Integer, default=0, comment="样本大小")
    
    # 状态管理
    is_active = Column(Boolean, default=True, comment="是否激活")
    last_confirmed_date = Column(Date, comment="最后确认日期")
    
    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")
    updated_at = Column(TIMESTAMP, server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 索引定义
    __table_args__ = (
        Index("idx_user_behavior_patterns_user", "user_id"),
        Index("idx_user_behavior_patterns_type", "pattern_type"),
        Index("idx_user_behavior_patterns_confidence", "confidence_score"),
        Index("idx_user_behavior_patterns_active", "is_active"),
        Index("idx_user_behavior_patterns_detection", "detection_date"),
    )

    def __repr__(self):
        return f"<UserBehaviorPattern(user_id={self.user_id}, type='{self.pattern_type}', name='{self.pattern_name}')>"


class UserInterestCategory(Base):
    """用户兴趣分类表"""
    
    __tablename__ = "user_interest_categories"

    id = Column(BigInteger, primary_key=True, comment="记录ID")
    user_id = Column(BigInteger, nullable=False, comment="用户ID")
    category_id = Column(BigInteger, nullable=False, comment="分类ID")  # 可以关联TagClassification
    
    # 兴趣评分
    interest_score = Column(DECIMAL(3, 2), default=0, comment="兴趣评分 (0-1)")
    explicit_score = Column(DECIMAL(3, 2), default=0, comment="显式兴趣评分")
    implicit_score = Column(DECIMAL(3, 2), default=0, comment="隐式兴趣评分")
    
    # 趋势分析
    trend = Column(String(20), default="stable", comment="趋势：increasing/decreasing/stable")
    trend_strength = Column(DECIMAL(3, 2), default=0, comment="趋势强度 (0-1)")
    
    # 行为统计
    interaction_count = Column(Integer, default=0, comment="交互次数")
    last_interaction_date = Column(Date, comment="最后交互日期")
    first_interaction_date = Column(Date, comment="首次交互日期")
    
    # 时间分析
    peak_activity_hour = Column(Integer, comment="活跃高峰小时")
    activity_duration_days = Column(Integer, default=0, comment="活跃持续天数")
    
    # 质量评估
    quality_score = Column(DECIMAL(3, 2), default=0, comment="兴趣质量评分")
    stability_score = Column(DECIMAL(3, 2), default=0, comment="稳定性评分")
    
    last_updated = Column(TIMESTAMP, server_default=func.now(), onupdate=func.now(), comment="最后更新时间")
    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")

    # 索引定义
    __table_args__ = (
        Index("idx_user_interest_categories_user", "user_id"),
        Index("idx_user_interest_categories_category", "category_id"),
        Index("idx_user_interest_categories_score", "interest_score"),
        Index("idx_user_interest_categories_trend", "trend"),
        Index("idx_user_interest_categories_updated", "last_updated"),
        # 复合索引
        Index("idx_user_interest_categories_user_category", "user_id", "category_id"),
        Index("idx_user_interest_categories_user_score", "user_id", "interest_score"),
    )

    def __repr__(self):
        return f"<UserInterestCategory(user_id={self.user_id}, category_id={self.category_id}, score={self.interest_score})>"


class UserInterestTrend(Base):
    """用户兴趣趋势表"""
    
    __tablename__ = "user_interest_trends"

    id = Column(BigInteger, primary_key=True, comment="趋势ID")
    user_id = Column(BigInteger, nullable=False, comment="用户ID")
    tag_id = Column(BigInteger, nullable=False, comment="标签ID")
    
    # 时间维度
    trend_date = Column(Date, nullable=False, comment="趋势日期")
    week_of_year = Column(Integer, comment="年中第几周")
    month_of_year = Column(Integer, comment="年中第几月")
    
    # 兴趣指标
    daily_interest_score = Column(DECIMAL(3, 2), default=0, comment="日兴趣评分")
    weekly_avg_score = Column(DECIMAL(3, 2), default=0, comment="周平均评分")
    monthly_avg_score = Column(DECIMAL(3, 2), default=0, comment="月平均评分")
    
    # 行为指标
    daily_interactions = Column(Integer, default=0, comment="日交互次数")
    daily_view_time = Column(Integer, default=0, comment="日浏览时间（秒）")
    
    # 变化指标
    score_change_from_prev_day = Column(DECIMAL(4, 3), default=0, comment="较前日评分变化")
    score_change_from_prev_week = Column(DECIMAL(4, 3), default=0, comment="较前周评分变化")
    
    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")

    # 索引定义
    __table_args__ = (
        Index("idx_user_interest_trends_user_date", "user_id", "trend_date"),
        Index("idx_user_interest_trends_tag_date", "tag_id", "trend_date"),
        Index("idx_user_interest_trends_date", "trend_date"),
        Index("idx_user_interest_trends_week", "week_of_year"),
        Index("idx_user_interest_trends_month", "month_of_year"),
        # 复合索引
        Index("idx_user_interest_trends_user_tag_date", "user_id", "tag_id", "trend_date"),
    )

    def __repr__(self):
        return f"<UserInterestTrend(user_id={self.user_id}, tag_id={self.tag_id}, date={self.trend_date})>"


class UserSessionSummary(Base):
    """用户会话摘要表"""
    
    __tablename__ = "user_session_summaries"

    id = Column(BigInteger, primary_key=True, comment="会话ID")
    user_id = Column(BigInteger, nullable=False, comment="用户ID")
    session_date = Column(Date, nullable=False, comment="会话日期")
    session_start_hour = Column(Integer, comment="会话开始小时")
    
    # 会话统计
    total_duration_seconds = Column(Integer, default=0, comment="总时长（秒）")
    page_views = Column(Integer, default=0, comment="页面浏览数")
    unique_tags_viewed = Column(Integer, default=0, comment="浏览的唯一标签数")
    interactions_count = Column(Integer, default=0, comment="交互次数")
    
    # 行为分析
    primary_interest_category = Column(String(100), comment="主要兴趣分类")
    secondary_interest_category = Column(String(100), comment="次要兴趣分类")
    session_quality_score = Column(DECIMAL(3, 2), default=0, comment="会话质量评分")
    
    # 设备和环境
    device_type = Column(String(50), comment="设备类型")
    access_channel = Column(String(50), comment="访问渠道")
    
    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")

    # 索引定义
    __table_args__ = (
        Index("idx_user_session_summaries_user_date", "user_id", "session_date"),
        Index("idx_user_session_summaries_date", "session_date"),
        Index("idx_user_session_summaries_quality", "session_quality_score"),
        Index("idx_user_session_summaries_duration", "total_duration_seconds"),
    )

    def __repr__(self):
        return f"<UserSessionSummary(user_id={self.user_id}, date={self.session_date})>"
