"""
标签分类服务B端管理API路由
定义标签分类相关的管理HTTP接口，包含创建、修改、删除操作
"""

from math import ceil
from typing import Annotated, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
from sqlalchemy.orm import Session

from ...core.database import get_db
from ..permission_service.dependencies import require_admin, require_permission
from ..user_service.dependencies import get_current_active_user
from ..user_service.models import User
from .dependencies import (
    get_classification_service,
    get_tag_category_service,
    get_tag_service,
    get_tag_type_service,
)
from .schemas import (
    # 标签类型相关
    TagTypeCreate,
    TagTypeResponse,
    TagTypeUpdate,
    # 标签分类相关
    TagCategoryCreate,
    TagCategoryResponse,
    TagCategoryUpdate,
    # 标签相关
    TagCreate,
    TagResponse,
    TagUpdate,
    # 分类相关
    ClassificationDimensionCreate,
    ClassificationDimensionResponse,
    ClassificationDimensionUpdate,
    ClassificationValueCreate,
    ClassificationValueResponse,
    ClassificationValueUpdate,
    # 通用响应
    PageResponse,
    UserClassificationPreferenceCreateInternal,
    UserClassificationPreferenceResponse,
    UserClassificationPreferenceUpdate,
)
from .service import (
    ClassificationService,
    TagCategoryService,
    TagService,
    TagTypeService,
)

# 创建路由器
router = APIRouter()


def get_client_info(request: Request) -> tuple[str, str]:
    """
    获取客户端信息

    Args:
        request: FastAPI请求对象

    Returns:
        IP地址和用户代理的元组
    """
    ip_address = request.client.host if request.client else None
    user_agent = request.headers.get("User-Agent", "")
    return ip_address, user_agent


# ==================== 标签类型管理接口 ====================


@router.get(
    "/tags/types",
    response_model=PageResponse,
    summary="获取标签类型列表",
    description="获取标签类型分页列表 (需要管理员权限)",
)
async def get_tag_types_admin(
    current_user: Annotated[User, Depends(require_permission("tag.type.read"))],
    tag_type_service: Annotated[TagTypeService, Depends(get_tag_type_service)],
    page: int = Query(default=1, ge=1, description="页码"),
    size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    is_active: Optional[bool] = Query(default=None, description="是否只获取活跃类型"),
):
    """获取标签类型列表"""
    try:
        skip = (page - 1) * size
        tag_types, total = tag_type_service.get_tag_types(skip, size, is_active)

        return PageResponse(
            items=[TagTypeResponse.model_validate(tt) for tt in tag_types],
            total=total,
            page=page,
            size=size,
            pages=ceil(total / size),
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get tag types: {str(e)}",
        )


@router.get(
    "/tags/types/{tag_type_id}",
    response_model=TagTypeResponse,
    summary="获取标签类型详情",
    description="根据ID获取标签类型详情 (需要管理员权限)",
)
async def get_tag_type_admin(
    tag_type_id: int,
    current_user: Annotated[User, Depends(require_permission("tag.type.read"))],
    tag_type_service: Annotated[TagTypeService, Depends(get_tag_type_service)],
):
    """获取标签类型详情"""
    tag_type = tag_type_service.get_tag_type_by_id(tag_type_id)

    if not tag_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Tag type not found"
        )

    return TagTypeResponse.model_validate(tag_type)


@router.post(
    "/tags/types",
    response_model=TagTypeResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建标签类型",
    description="创建新的标签类型 (需要管理员权限)",
)
async def create_tag_type(
    tag_type_data: TagTypeCreate,
    current_user: Annotated[User, Depends(require_permission("tag.type.create"))],
    tag_type_service: Annotated[TagTypeService, Depends(get_tag_type_service)],
    db: Session = Depends(get_db),
):
    """创建标签类型"""
    try:
        tag_type = tag_type_service.create_tag_type(tag_type_data)
        return TagTypeResponse.model_validate(tag_type)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create tag type: {str(e)}",
        )


@router.put(
    "/tags/types/{tag_type_id}",
    response_model=TagTypeResponse,
    summary="更新标签类型",
    description="更新标签类型信息 (需要管理员权限)",
)
async def update_tag_type(
    tag_type_id: int,
    update_data: TagTypeUpdate,
    current_user: Annotated[User, Depends(require_permission("tag.type.update"))],
    tag_type_service: Annotated[TagTypeService, Depends(get_tag_type_service)],
    db: Session = Depends(get_db),
):
    """更新标签类型"""
    try:
        tag_type = tag_type_service.update_tag_type(tag_type_id, update_data)

        if not tag_type:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Tag type not found"
            )

        return TagTypeResponse.model_validate(tag_type)
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update tag type: {str(e)}",
        )


@router.delete(
    "/tags/types/{tag_type_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除标签类型",
    description="删除标签类型 (需要管理员权限)",
)
async def delete_tag_type(
    tag_type_id: int,
    current_user: Annotated[User, Depends(require_permission("tag.type.delete"))],
    tag_type_service: Annotated[TagTypeService, Depends(get_tag_type_service)],
    db: Session = Depends(get_db),
):
    """删除标签类型"""
    try:
        success = tag_type_service.delete_tag_type(tag_type_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Tag type not found"
            )
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete tag type: {str(e)}",
        )


# ==================== 标签分类管理接口 ====================


@router.get(
    "/tags/categories",
    response_model=PageResponse,
    summary="获取标签分类列表",
    description="获取标签分类分页列表 (需要管理员权限)",
)
async def get_tag_categories_admin(
    current_user: Annotated[User, Depends(require_permission("tag.category.read"))],
    category_service: Annotated[TagCategoryService, Depends(get_tag_category_service)],
    page: int = Query(default=1, ge=1, description="页码"),
    size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    parent_id: Optional[int] = Query(default=None, description="父分类ID"),
    is_active: Optional[bool] = Query(default=None, description="是否只获取活跃分类"),
):
    """获取标签分类列表"""
    try:
        skip = (page - 1) * size
        categories, total = category_service.get_tag_categories(
            skip, size, parent_id, is_active
        )

        return PageResponse(
            items=[TagCategoryResponse.model_validate(cat) for cat in categories],
            total=total,
            page=page,
            size=size,
            pages=ceil(total / size),
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get tag categories: {str(e)}",
        )


@router.get(
    "/tags/categories/{category_id}",
    response_model=TagCategoryResponse,
    summary="获取标签分类详情",
    description="根据ID获取标签分类详情 (需要管理员权限)",
)
async def get_tag_category_admin(
    category_id: int,
    current_user: Annotated[User, Depends(require_permission("tag.category.read"))],
    category_service: Annotated[TagCategoryService, Depends(get_tag_category_service)],
):
    """获取标签分类详情"""
    category = category_service.get_tag_category_by_id(category_id)

    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Tag category not found"
        )

    return TagCategoryResponse.model_validate(category)


@router.get(
    "/tags/categories/tree",
    response_model=List[TagCategoryResponse],
    summary="获取标签分类树",
    description="获取标签分类的树形结构 (需要管理员权限)",
)
async def get_tag_categories_tree_admin(
    current_user: Annotated[User, Depends(require_permission("tag.category.read"))],
    category_service: Annotated[TagCategoryService, Depends(get_tag_category_service)],
    is_active: Optional[bool] = Query(default=None, description="是否只获取活跃分类"),
):
    """获取标签分类树形结构"""
    try:
        categories = category_service.get_tag_categories_tree(is_active)
        return [TagCategoryResponse.model_validate(cat) for cat in categories]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get tag categories tree: {str(e)}",
        )


@router.post(
    "/tags/categories",
    response_model=TagCategoryResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建标签分类",
    description="创建新的标签分类 (需要管理员权限)",
)
async def create_tag_category(
    category_data: TagCategoryCreate,
    current_user: Annotated[User, Depends(require_permission("tag.category.create"))],
    category_service: Annotated[TagCategoryService, Depends(get_tag_category_service)],
    db: Session = Depends(get_db),
):
    """创建标签分类"""
    try:
        category = category_service.create_tag_category(category_data)
        return TagCategoryResponse.model_validate(category)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create tag category: {str(e)}",
        )


@router.put(
    "/tags/categories/{category_id}",
    response_model=TagCategoryResponse,
    summary="更新标签分类",
    description="更新标签分类信息 (需要管理员权限)",
)
async def update_tag_category(
    category_id: int,
    update_data: TagCategoryUpdate,
    current_user: Annotated[User, Depends(require_permission("tag.category.update"))],
    category_service: Annotated[TagCategoryService, Depends(get_tag_category_service)],
    db: Session = Depends(get_db),
):
    """更新标签分类"""
    try:
        category = category_service.update_tag_category(category_id, update_data)

        if not category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Tag category not found"
            )

        return TagCategoryResponse.model_validate(category)
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update tag category: {str(e)}",
        )


@router.delete(
    "/tags/categories/{category_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除标签分类",
    description="删除标签分类 (需要管理员权限)",
)
async def delete_tag_category(
    category_id: int,
    current_user: Annotated[User, Depends(require_permission("tag.category.delete"))],
    category_service: Annotated[TagCategoryService, Depends(get_tag_category_service)],
    db: Session = Depends(get_db),
):
    """删除标签分类"""
    try:
        success = category_service.delete_tag_category(category_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Tag category not found"
            )
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete tag category: {str(e)}",
        )


# ==================== 标签管理接口 ====================


@router.get(
    "/tags",
    response_model=PageResponse,
    summary="获取标签列表",
    description="根据查询条件获取标签分页列表 (需要管理员权限)",
)
async def get_tags_admin(
    current_user: Annotated[User, Depends(require_permission("tag.tag.read"))],
    tag_service: Annotated[TagService, Depends(get_tag_service)],
    page: int = Query(default=1, ge=1, description="页码"),
    size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(default=None, description="搜索关键词"),
    tag_type_id: Optional[int] = Query(default=None, description="标签类型ID"),
    tag_category_id: Optional[int] = Query(default=None, description="标签分类ID"),
    parent_id: Optional[int] = Query(default=None, description="父标签ID"),
    lifecycle_stage: Optional[str] = Query(default=None, description="生命周期阶段"),
    is_active: Optional[bool] = Query(default=None, description="是否启用"),
    order_by: str = Query(default="created_at", description="排序字段"),
    order_direction: str = Query(default="desc", description="排序方向"),
):
    """获取标签列表"""
    try:
        skip = (page - 1) * size
        
        # 创建查询对象
        from .schemas import TagListQuery
        query = TagListQuery(
            search=search,
            tag_type_id=tag_type_id,
            tag_category_id=tag_category_id,
            parent_id=parent_id,
            lifecycle_stage=lifecycle_stage,
            is_active=is_active,
            order_by=order_by,
            order_direction=order_direction,
        )
        
        tags, total = tag_service.get_tags(query, skip=skip, limit=size)

        return PageResponse(
            items=[TagResponse.model_validate(tag) for tag in tags],
            total=total,
            page=page,
            size=size,
            pages=ceil(total / size),
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get tags: {str(e)}",
        )


@router.get(
    "/tags/{tag_id}",
    response_model=TagResponse,
    summary="获取标签详情",
    description="根据ID获取标签详情 (需要管理员权限)",
)
async def get_tag_admin(
    tag_id: int,
    current_user: Annotated[User, Depends(require_permission("tag.tag.read"))],
    tag_service: Annotated[TagService, Depends(get_tag_service)],
    include_relations: bool = Query(default=False, description="是否包含关联对象"),
):
    """获取标签详情"""
    tag = tag_service.get_tag_by_id(tag_id, include_relations=include_relations)

    if not tag:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Tag not found"
        )

    return TagResponse.model_validate(tag)


@router.get(
    "/tags/search",
    response_model=List[TagResponse],
    summary="搜索标签",
    description="根据关键词搜索标签 (需要管理员权限)",
)
async def search_tags_admin(
    current_user: Annotated[User, Depends(require_permission("tag.tag.read"))],
    tag_service: Annotated[TagService, Depends(get_tag_service)],
    q: str = Query(..., description="搜索关键词"),
    limit: int = Query(default=10, ge=1, le=50, description="返回数量限制"),
):
    """搜索标签"""
    try:
        tags = tag_service.search_tags(q, limit)
        return [TagResponse.model_validate(tag) for tag in tags]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search tags: {str(e)}",
        )


@router.post(
    "/tags",
    response_model=TagResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建标签",
    description="创建新的标签 (需要管理员权限)",
)
async def create_tag(
    tag_data: TagCreate,
    current_user: Annotated[User, Depends(require_permission("tag.tag.create"))],
    tag_service: Annotated[TagService, Depends(get_tag_service)],
    db: Session = Depends(get_db),
):
    """创建标签"""
    try:
        tag = tag_service.create_tag(tag_data)
        return TagResponse.model_validate(tag)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create tag: {str(e)}",
        )


@router.put(
    "/tags/{tag_id}",
    response_model=TagResponse,
    summary="更新标签",
    description="更新标签信息 (需要管理员权限)",
)
async def update_tag(
    tag_id: int,
    update_data: TagUpdate,
    current_user: Annotated[User, Depends(require_permission("tag.tag.update"))],
    tag_service: Annotated[TagService, Depends(get_tag_service)],
    db: Session = Depends(get_db),
):
    """更新标签"""
    try:
        tag = tag_service.update_tag(tag_id, update_data)

        if not tag:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Tag not found"
            )

        return TagResponse.model_validate(tag)
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update tag: {str(e)}",
        )


@router.delete(
    "/tags/{tag_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除标签",
    description="删除标签 (需要管理员权限)",
)
async def delete_tag(
    tag_id: int,
    current_user: Annotated[User, Depends(require_permission("tag.tag.delete"))],
    tag_service: Annotated[TagService, Depends(get_tag_service)],
    db: Session = Depends(get_db),
):
    """删除标签"""
    try:
        success = tag_service.delete_tag(tag_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Tag not found"
            )
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete tag: {str(e)}",
        )


# ==================== 分类维度管理接口 ====================


@router.get(
    "/classifications/dimensions",
    response_model=List[ClassificationDimensionResponse],
    summary="获取分类维度列表",
    description="获取所有分类维度 (需要管理员权限)",
)
async def get_classification_dimensions_admin(
    current_user: Annotated[
        User, Depends(require_permission("classification.dimension.read"))
    ],
    classification_service: Annotated[
        ClassificationService, Depends(get_classification_service)
    ],
    is_active: Optional[bool] = Query(default=None, description="是否只获取活跃维度"),
):
    """获取分类维度列表"""
    try:
        dimensions, _ = classification_service.get_classification_dimensions(
            skip=0, limit=1000, is_active=is_active
        )
        return [
            ClassificationDimensionResponse.model_validate(dim) for dim in dimensions
        ]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get classification dimensions: {str(e)}",
        )


@router.get(
    "/classifications/dimensions/{dimension_id}",
    response_model=ClassificationDimensionResponse,
    summary="获取分类维度详情",
    description="根据ID获取分类维度详情 (需要管理员权限)",
)
async def get_classification_dimension_admin(
    dimension_id: int,
    current_user: Annotated[
        User, Depends(require_permission("classification.dimension.read"))
    ],
    classification_service: Annotated[
        ClassificationService, Depends(get_classification_service)
    ],
):
    """获取分类维度详情"""
    dimension = classification_service.get_classification_dimension_by_id(dimension_id)

    if not dimension:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Classification dimension not found",
        )

    return ClassificationDimensionResponse.model_validate(dimension)


@router.post(
    "/classifications/dimensions",
    response_model=ClassificationDimensionResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建分类维度",
    description="创建新的分类维度 (需要管理员权限)",
)
async def create_classification_dimension(
    dimension_data: ClassificationDimensionCreate,
    current_user: Annotated[
        User, Depends(require_permission("classification.dimension.create"))
    ],
    classification_service: Annotated[
        ClassificationService, Depends(get_classification_service)
    ],
    db: Session = Depends(get_db),
):
    """创建分类维度"""
    try:
        dimension = classification_service.create_classification_dimension(dimension_data)
        return ClassificationDimensionResponse.model_validate(dimension)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create classification dimension: {str(e)}",
        )


@router.put(
    "/classifications/dimensions/{dimension_id}",
    response_model=ClassificationDimensionResponse,
    summary="更新分类维度",
    description="更新分类维度信息 (需要管理员权限)",
)
async def update_classification_dimension(
    dimension_id: int,
    update_data: ClassificationDimensionUpdate,
    current_user: Annotated[
        User, Depends(require_permission("classification.dimension.update"))
    ],
    classification_service: Annotated[
        ClassificationService, Depends(get_classification_service)
    ],
    db: Session = Depends(get_db),
):
    """更新分类维度"""
    try:
        dimension = classification_service.update_classification_dimension(dimension_id, update_data)

        if not dimension:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Classification dimension not found",
            )

        return ClassificationDimensionResponse.model_validate(dimension)
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update classification dimension: {str(e)}",
        )


@router.delete(
    "/classifications/dimensions/{dimension_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除分类维度",
    description="删除分类维度 (需要管理员权限)",
)
async def delete_classification_dimension(
    dimension_id: int,
    current_user: Annotated[
        User, Depends(require_permission("classification.dimension.delete"))
    ],
    classification_service: Annotated[
        ClassificationService, Depends(get_classification_service)
    ],
    db: Session = Depends(get_db),
):
    """删除分类维度"""
    try:
        success = classification_service.delete_classification_dimension(dimension_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Classification dimension not found",
            )
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete classification dimension: {str(e)}",
        )


# ==================== 分类值管理接口 ====================


@router.get(
    "/classifications/values",
    response_model=PageResponse,
    summary="获取分类值列表",
    description="获取分类值列表，支持按维度筛选、模糊搜索和分页 (需要管理员权限)",
)
async def get_classification_values_admin(
    current_user: Annotated[
        User, Depends(require_permission("classification.value.read"))
    ],
    classification_service: Annotated[
        ClassificationService, Depends(get_classification_service)
    ],
    page: int = Query(default=1, ge=1, description="页码"),
    size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    dimension_id: Optional[int] = Query(default=None, description="分类维度ID，为空则获取所有维度的分类值"),
    parent_id: Optional[int] = Query(default=None, description="父分类值ID"),
    is_active: Optional[bool] = Query(default=None, description="是否只获取活跃值"),
    search: Optional[str] = Query(default=None, description="搜索关键词，支持按display_name模糊搜索"),
):
    """获取分类值列表"""
    try:
        skip = (page - 1) * size
        values, total = classification_service.get_classification_values(
            dimension_id=dimension_id,
            skip=skip,
            limit=size,
            parent_id=parent_id,
            is_active=is_active,
            search=search
        )
        
        return PageResponse(
            items=[ClassificationValueResponse.model_validate(val) for val in values],
            total=total,
            page=page,
            size=size,
            pages=ceil(total / size),
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get classification values: {str(e)}",
        )


@router.get(
    "/classifications/values/{value_id}",
    response_model=ClassificationValueResponse,
    summary="获取分类值详情",
    description="根据ID获取分类值详情 (需要管理员权限)",
)
async def get_classification_value_admin(
    value_id: int,
    current_user: Annotated[
        User, Depends(require_permission("classification.value.read"))
    ],
    classification_service: Annotated[
        ClassificationService, Depends(get_classification_service)
    ],
):
    """获取分类值详情"""
    value = classification_service.get_classification_value_by_id(value_id)

    if not value:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Classification value not found",
        )

    return ClassificationValueResponse.model_validate(value)


@router.post(
    "/classifications/values",
    response_model=ClassificationValueResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建分类值",
    description="创建新的分类值 (需要管理员权限)",
)
async def create_classification_value(
    value_data: ClassificationValueCreate,
    current_user: Annotated[
        User, Depends(require_permission("classification.value.create"))
    ],
    classification_service: Annotated[
        ClassificationService, Depends(get_classification_service)
    ],
    db: Session = Depends(get_db),
):
    """创建分类值"""
    try:
        value = classification_service.create_classification_value(value_data)
        return ClassificationValueResponse.model_validate(value)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create classification value: {str(e)}",
        )


@router.put(
    "/classifications/values/{value_id}",
    response_model=ClassificationValueResponse,
    summary="更新分类值",
    description="更新分类值信息 (需要管理员权限)",
)
async def update_classification_value(
    value_id: int,
    update_data: ClassificationValueUpdate,
    current_user: Annotated[
        User, Depends(require_permission("classification.value.update"))
    ],
    classification_service: Annotated[
        ClassificationService, Depends(get_classification_service)
    ],
    db: Session = Depends(get_db),
):
    """更新分类值"""
    try:
        value = classification_service.update_classification_value(value_id, update_data)

        if not value:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Classification value not found",
            )

        return ClassificationValueResponse.model_validate(value)
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update classification value: {str(e)}",
        )


@router.delete(
    "/classifications/values/{value_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除分类值",
    description="删除分类值 (需要管理员权限)",
)
async def delete_classification_value(
    value_id: int,
    current_user: Annotated[
        User, Depends(require_permission("classification.value.delete"))
    ],
    classification_service: Annotated[
        ClassificationService, Depends(get_classification_service)
    ],
    db: Session = Depends(get_db),
):
    """删除分类值"""
    try:
        success = classification_service.delete_classification_value(value_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Classification value not found",
            )
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete classification value: {str(e)}",
        )


# ==================== 用户分类偏好管理接口 ====================


@router.get(
    "/users/{user_id}/classification-preferences",
    response_model=List[UserClassificationPreferenceResponse],
    summary="获取用户分类偏好列表",
    description="获取指定用户的分类偏好列表 (需要管理员权限)",
)
async def get_user_classification_preferences_admin(
    user_id: int,
    current_user: Annotated[
        User, Depends(require_permission("user.preference.read"))
    ],
    classification_service: Annotated[
        ClassificationService, Depends(get_classification_service)
    ],
):
    """获取用户分类偏好列表"""
    try:
        preferences = classification_service.get_user_classification_preferences(user_id)
        return [
            UserClassificationPreferenceResponse.model_validate(pref)
            for pref in preferences
        ]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get user classification preferences: {str(e)}",
        )


@router.get(
    "/users/{user_id}/classification-preferences/{preference_id}",
    response_model=UserClassificationPreferenceResponse,
    summary="获取用户分类偏好详情",
    description="根据ID获取用户分类偏好详情 (需要管理员权限)",
)
async def get_user_classification_preference_admin(
    user_id: int,
    preference_id: int,
    current_user: Annotated[
        User, Depends(require_permission("user.preference.read"))
    ],
    classification_service: Annotated[
        ClassificationService, Depends(get_classification_service)
    ],
):
    """获取用户分类偏好详情"""
    preference = classification_service.get_user_classification_preference_by_id(
        preference_id
    )

    if not preference:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User classification preference not found",
        )

    return UserClassificationPreferenceResponse.model_validate(preference)


@router.post(
    "/users/{user_id}/classification-preferences",
    response_model=UserClassificationPreferenceResponse,
    status_code=status.HTTP_201_CREATED,
    summary="为用户创建分类偏好",
    description="为指定用户创建或更新分类偏好 (需要管理员权限)",
)
async def create_user_classification_preference_admin(
    user_id: int,
    preference_data: UserClassificationPreferenceCreateInternal,
    current_user: Annotated[
        User, Depends(require_permission("user.preference.manage"))
    ],
    classification_service: Annotated[
        ClassificationService, Depends(get_classification_service)
    ],
    db: Session = Depends(get_db),
):
    """管理员为用户创建分类偏好"""
    try:
        # 设置用户ID
        preference_data.user_id = user_id

        preference = classification_service.create_user_classification_preference(preference_data)
        return UserClassificationPreferenceResponse.model_validate(preference)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create user classification preference: {str(e)}",
        )


@router.put(
    "/users/{user_id}/classification-preferences/{preference_id}",
    response_model=UserClassificationPreferenceResponse,
    summary="更新用户分类偏好",
    description="更新用户分类偏好 (需要管理员权限)",
)
async def update_user_classification_preference_admin(
    user_id: int,
    preference_id: int,
    update_data: UserClassificationPreferenceUpdate,
    current_user: Annotated[
        User, Depends(require_permission("user.preference.manage"))
    ],
    classification_service: Annotated[
        ClassificationService, Depends(get_classification_service)
    ],
    db: Session = Depends(get_db),
):
    """管理员更新用户分类偏好"""
    try:
        preference = classification_service.update_user_classification_preference(
            preference_id, update_data
        )

        if not preference:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User classification preference not found",
            )

        # 验证偏好属于指定用户
        if preference.user_id != user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Preference does not belong to specified user",
            )

        return UserClassificationPreferenceResponse.model_validate(preference)
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update user classification preference: {str(e)}",
        )


@router.delete(
    "/users/{user_id}/classification-preferences/{preference_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除用户分类偏好",
    description="删除用户分类偏好 (需要管理员权限)",
)
async def delete_user_classification_preference_admin(
    user_id: int,
    preference_id: int,
    current_user: Annotated[
        User, Depends(require_permission("user.preference.manage"))
    ],
    classification_service: Annotated[
        ClassificationService, Depends(get_classification_service)
    ],
    db: Session = Depends(get_db),
):
    """管理员删除用户分类偏好"""
    try:
        # 先获取偏好以验证归属
        preference = classification_service.get_user_classification_preference_by_id(preference_id)

        if not preference:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User classification preference not found",
            )

        if preference.user_id != user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Preference does not belong to specified user",
            )

        success = classification_service.delete_user_classification_preference(preference_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User classification preference not found",
            )
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete user classification preference: {str(e)}",
        )


# ==================== 数据统计分析接口 ====================


@router.get(
    "/tags/analytics",
    summary="标签统计分析",
    description="获取标签使用统计和分析数据 (需要管理员权限)",
)
async def get_tag_analytics(
    current_user: Annotated[User, Depends(require_permission("tag.analytics.read"))],
    tag_service: Annotated[TagService, Depends(get_tag_service)],
    db: Session = Depends(get_db),
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
):
    """获取标签统计分析"""
    try:
        analytics = tag_service.get_tag_analytics(start_date, end_date)
        return analytics
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get tag analytics: {str(e)}",
        )


@router.get(
    "/tags/analytics/users/{user_id}",
    summary="用户标签分析",
    description="获取指定用户的标签使用分析 (需要管理员权限)",
)
async def get_user_tag_analytics(
    user_id: int,
    current_user: Annotated[
        User, Depends(require_permission("user.tag.analytics.read"))
    ],
    tag_service: Annotated[TagService, Depends(get_tag_service)],
    classification_service: Annotated[
        ClassificationService, Depends(get_classification_service)
    ],
    db: Session = Depends(get_db),
):
    """获取用户标签分析"""
    try:
        # 获取用户的标签偏好和使用情况
        user_preferences = classification_service.get_user_classification_preferences(user_id)
        user_tag_analytics = tag_service.get_user_tag_analytics(user_id)

        return {
            "user_id": user_id,
            "preferences": [
                UserClassificationPreferenceResponse.model_validate(pref)
                for pref in user_preferences
            ],
            "analytics": user_tag_analytics,
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get user tag analytics: {str(e)}",
        )


@router.get(
    "/classifications/analytics",
    summary="分类统计分析",
    description="获取分类维度和值的使用统计 (需要管理员权限)",
)
async def get_classification_analytics(
    current_user: Annotated[
        User, Depends(require_permission("classification.analytics.read"))
    ],
    classification_service: Annotated[
        ClassificationService, Depends(get_classification_service)
    ],
    db: Session = Depends(get_db),
):
    """获取分类统计分析"""
    try:
        analytics = classification_service.get_classification_analytics()
        return analytics
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get classification analytics: {str(e)}",
        )
