"""
AI内容分析服务
提供内容分析、标签提取、分类识别等AI功能
"""

import asyncio
import json
import logging
import re
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy.orm import Session
from sqlalchemy.dialects.postgresql import ARRAY
from sqlalchemy import text

from ...core.ai_config import get_default_model_config, ModelType
from ...core.database import SessionLocal
from ..tag_classification_service.models import Tag, ClassificationDimension, ClassificationValue
from ..ai_service.service import AIModelService
from ..ai_service.schemas import ModelType as AIModelType
from .models import AITagMatches, UnifiedContentTags, UnifiedContentClassifications
from .real_ai_client import AIClientFactory, get_ai_client

logger = logging.getLogger(__name__)


def clean_json_response(text: str) -> str:
    """清理AI响应中的JSON文本，移除代码块标记"""
    clean_text = text.strip()
    if clean_text.startswith('```json'):
        clean_text = clean_text[7:]
    elif clean_text.startswith('```'):
        clean_text = clean_text[3:]
    if clean_text.endswith('```'):
        clean_text = clean_text[:-3]
    return clean_text.strip()


class AIContentAnalyzer:
    """
    AI内容分析器
    负责对内容进行AI分析，包括标签提取、分类识别、摘要生成等
    """

    def __init__(self):
        # 优先使用数据库配置
        db = SessionLocal()
        try:
            ai_model_service = AIModelService(db)
            self.ai_client = AIClientFactory.create_client(ai_model_service=ai_model_service)
            logger.info("AI内容分析器使用数据库模型配置初始化成功")
        except Exception as e:
            logger.warning(f"使用数据库配置初始化失败: {e}，回退到硬编码配置")
            self.model_config = get_default_model_config(ModelType.CHAT)
            self.ai_client = AIClientFactory.create_client(self.model_config)
        finally:
            db.close()

    async def analyze_content(
        self,
        data: Dict[str, Any],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        对内容进行完整的AI分析

        Args:
            data: 待分析的数据
            config: 分析配置

        Returns:
            Dict: 增强后的数据
        """

        enhanced_data = data.copy()

        # 使用一个共享的AI客户端会话进行所有API调用
        async with self.ai_client as client:
            try:
                # 1. 生成摘要
                if config.get("generate_summary", True):
                    summary = await self._generate_summary_with_client(client, data)
                    if summary:
                        enhanced_data["summary"] = summary

                # 2. 提取标签
                if config.get("extract_tags", True):
                    tags = await self._extract_tags_with_client(client, data)
                    enhanced_data["ai_extracted_tags"] = tags

                    # 进行标签匹配和保存（无论是否有content_id，都进行匹配以便后续使用）
                    if tags:
                        try:
                            # 获取内容ID和类型，如果没有则使用临时值
                            content_id = data.get("content_id")
                            content_type = data.get("content_type", "flash_news")  # 默认类型

                            if content_id:
                                # 有具体内容ID时，进行完整的匹配和关联
                                matched_tags = await self.match_tags_to_standard(
                                    tags, content_type, content_id,
                                    confidence_threshold=config.get("tag_confidence_threshold", 0.6)
                                )
                                enhanced_data["matched_tags"] = matched_tags
                                enhanced_data["matched_tag_count"] = len(matched_tags)
                                logger.info(f"成功匹配 {len(matched_tags)} 个标签到内容 {content_type}:{content_id}")
                            else:
                                # 没有内容ID时，只进行标签匹配不保存关联
                                matched_tags = await self.match_tags_only(
                                    tags, confidence_threshold=config.get("tag_confidence_threshold", 0.6)
                                )
                                enhanced_data["matched_tags"] = matched_tags
                                enhanced_data["matched_tag_count"] = len(matched_tags)
                                logger.info(f"成功匹配 {len(matched_tags)} 个标签（未保存关联）")
                        except Exception as e:
                            logger.error(f"标签匹配失败: {e}")
                            enhanced_data["matched_tags"] = []
                            enhanced_data["matched_tag_count"] = 0

                # 3. 内容分类
                if config.get("classify_content", True):
                    classifications = await self._classify_content_with_client(client, data)
                    enhanced_data["ai_classifications"] = classifications

                    # 进行分类匹配和保存
                    if classifications:
                        try:
                            content_id = data.get("content_id")
                            content_type = data.get("content_type", "flash_news")

                            if content_id:
                                # 有具体内容ID时，进行完整的匹配和关联
                                matched_classifications = await self.match_classifications_to_standard(
                                    classifications, content_type, content_id,
                                    confidence_threshold=config.get("classification_confidence_threshold", 0.7)
                                )
                                enhanced_data["matched_classifications"] = matched_classifications
                                enhanced_data["matched_classification_count"] = len(matched_classifications)
                                logger.info(f"成功匹配 {len(matched_classifications)} 个分类到内容 {content_type}:{content_id}")
                            else:
                                # 没有内容ID时，只进行分类匹配不保存关联
                                matched_classifications = await self.match_classifications_only(
                                    classifications, confidence_threshold=config.get("classification_confidence_threshold", 0.7)
                                )
                                enhanced_data["matched_classifications"] = matched_classifications
                                enhanced_data["matched_classification_count"] = len(matched_classifications)
                                logger.info(f"成功匹配 {len(matched_classifications)} 个分类（未保存关联）")
                        except Exception as e:
                            logger.error(f"分类匹配失败: {e}")
                            enhanced_data["matched_classifications"] = []
                            enhanced_data["matched_classification_count"] = 0

                # 4. 实体识别
                if config.get("extract_entities", True):
                    entities = await self._extract_entities_with_client(client, data)
                    enhanced_data.update(entities)

                # 5. 情感分析
                if config.get("sentiment_analysis", True):
                    sentiment = await self._analyze_sentiment_with_client(client, data)
                    enhanced_data["sentiment_analysis"] = sentiment

                # 6. 重要性评分
                if config.get("importance_scoring", True):
                    importance = await self._calculate_importance(data)
                    enhanced_data["importance_score"] = importance

                logger.debug("AI内容分析完成")
                return enhanced_data

            except Exception as e:
                logger.error(f"AI内容分析失败: {e}")
                return enhanced_data

    async def _generate_summary(self, data: Dict[str, Any]) -> Optional[str]:
        """生成内容摘要"""
        
        content = data.get("content", "")
        title = data.get("title", "")
        
        if not content:
            return None
            
        try:
            prompt = f"""
请为以下财经内容生成一个简洁的摘要（不超过100字）：

标题：{title}
内容：{content[:2000]}

要求：
1. 突出关键信息和核心观点
2. 保持客观中性的语调
3. 适合财经新闻读者快速了解
"""
            
            async with self.ai_client as client:
                response = await client.chat_completion(
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=200,
                    temperature=0.3
                )
            
            summary = response.get("content", "").strip()
            logger.debug(f"生成摘要: {summary[:50]}...")
            return summary
            
        except Exception as e:
            logger.error(f"生成摘要失败: {e}")
            return None

    async def _generate_summary_with_client(self, client, data: Dict[str, Any]) -> Optional[str]:
        """使用指定客户端生成内容摘要"""

        content = data.get("content", "")
        title = data.get("title", "")

        if not content:
            return None

        try:
            prompt = f"""
请为以下财经内容生成一个简洁的摘要（不超过100字）：

标题：{title}
内容：{content[:2000]}

要求：
1. 突出关键信息和核心观点
2. 保持客观中性的语调
3. 适合财经新闻读者快速了解
"""

            response = await client.chat_completion(
                messages=[{"role": "user", "content": prompt}],
                max_tokens=200,
                temperature=0.3
            )

            summary = response.get("content", "").strip()
            logger.debug(f"生成摘要: {summary[:50]}...")
            return summary

        except Exception as e:
            logger.error(f"生成摘要失败: {e}")
            return None

    async def _extract_tags(self, data: Dict[str, Any]) -> List[str]:
        """提取内容标签"""
        
        content = data.get("content", "")
        title = data.get("title", "")
        
        if not content and not title:
            return []
            
        try:
            prompt = f"""
请从以下财经内容中提取关键标签，每个标签用逗号分隔：

标题：{title}
内容：{content[:1500]}

要求：
1. 提取5-10个最相关的标签
2. 标签应该是财经领域的专业术语
3. 包括但不限于：公司名、行业、政策、市场、指标等
4. 只返回标签，用逗号分隔，不要其他解释
"""
            
            async with self.ai_client as client:
                response = await client.chat_completion(
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=150,
                    temperature=0.2
                )
            
            tags_text = response.get("content", "").strip()
            tags = [tag.strip() for tag in tags_text.split(",") if tag.strip()]
            
            logger.debug(f"提取标签: {tags}")
            return tags
            
        except Exception as e:
            logger.error(f"提取标签失败: {e}")
            return []

    async def _extract_tags_with_client(self, client, data: Dict[str, Any]) -> List[str]:
        """使用指定客户端提取内容标签"""

        content = data.get("content", "")
        title = data.get("title", "")

        if not content and not title:
            return []

        try:
            prompt = f"""
请从以下财经内容中提取关键标签，每个标签用逗号分隔：

标题：{title}
内容：{content[:1500]}

要求：
1. 提取5-10个最相关的标签
2. 标签应该是财经领域的专业术语
3. 包括但不限于：公司名、行业、政策、市场、指标等
4. 只返回标签，用逗号分隔，不要其他解释
"""

            response = await client.chat_completion(
                messages=[{"role": "user", "content": prompt}],
                max_tokens=150,
                temperature=0.2
            )

            tags_text = response.get("content", "").strip()
            tags = [tag.strip() for tag in tags_text.split(",") if tag.strip()]

            logger.debug(f"提取标签: {tags}")
            return tags

        except Exception as e:
            logger.error(f"提取标签失败: {e}")
            return []

    async def _classify_content(self, data: Dict[str, Any]) -> Dict[str, str]:
        """内容分类"""
        
        content = data.get("content", "")
        title = data.get("title", "")
        
        if not content and not title:
            return {}
            
        try:
            prompt = f"""
请对以下财经内容进行分类，返回JSON格式：

标题：{title}
内容：{content[:1500]}

请从以下维度进行分类：
1. 主要分类：monetary_policy(货币政策)/market_analysis(市场分析)/company_news(公司新闻)/economic_data(经济数据)/regulatory_news(监管新闻)
2. 影响范围：domestic(国内)/international(国际)/global(全球)
3. 紧急程度：low(低)/medium(中)/high(高)
4. 行业分类：banking(银行)/technology(科技)/real_estate(房地产)/manufacturing(制造业)/other(其他)

返回格式：
{{"primary_category": "分类", "impact_scope": "范围", "urgency_level": "程度", "industry": "行业"}}
"""
            
            async with self.ai_client as client:
                response = await client.chat_completion(
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=200,
                    temperature=0.1
                )
            
            classifications_text = response.get("content", "").strip()
            
            # 尝试解析JSON
            try:
                classifications = json.loads(classifications_text)
                logger.debug(f"内容分类: {classifications}")
                return classifications
            except json.JSONDecodeError:
                logger.warning(f"分类结果不是有效JSON: {classifications_text}")
                return {}
                
        except Exception as e:
            logger.error(f"内容分类失败: {e}")
            return {}

    async def _classify_content_with_client(self, client, data: Dict[str, Any]) -> Dict[str, str]:
        """使用指定客户端进行内容分类"""

        content = data.get("content", "")
        title = data.get("title", "")

        if not content and not title:
            return {}

        try:
            prompt = f"""
请对以下财经内容进行分类，返回JSON格式：

标题：{title}
内容：{content[:1500]}

请分析：
1. 主要分类：monetary_policy(货币政策)/fiscal_policy(财政政策)/market_news(市场新闻)/company_news(公司新闻)/economic_data(经济数据)
2. 影响范围：global(全球)/domestic(国内)/regional(区域)/industry(行业)
3. 紧急程度：high(高)/medium(中)/low(低)
4. 相关行业：如银行、科技、制造业等

返回格式：
{{"primary_category": "分类", "impact_scope": "范围", "urgency_level": "程度", "industry": "行业"}}
"""

            response = await client.chat_completion(
                messages=[{"role": "user", "content": prompt}],
                max_tokens=200,
                temperature=0.1
            )

            classifications_text = response.get("content", "").strip()

            # 尝试解析JSON
            try:
                clean_text = clean_json_response(classifications_text)
                classifications = json.loads(clean_text)
                logger.debug(f"内容分类: {classifications}")
                return classifications
            except json.JSONDecodeError:
                logger.warning(f"分类结果不是有效JSON: {classifications_text}")
                return {}

        except Exception as e:
            logger.error(f"内容分类失败: {e}")
            return {}

    async def _extract_entities(self, data: Dict[str, Any]) -> Dict[str, List[str]]:
        """实体识别"""
        
        content = data.get("content", "")
        title = data.get("title", "")
        
        if not content and not title:
            return {}
            
        try:
            prompt = f"""
请从以下财经内容中识别实体，返回JSON格式：

标题：{title}
内容：{content[:1500]}

请识别以下类型的实体：
1. 公司名称 (companies)
2. 人物姓名 (people)
3. 地点名称 (locations)
4. 股票代码 (stocks)
5. 货币名称 (currencies)

返回格式：
{{"companies": ["公司1", "公司2"], "people": ["人物1"], "locations": ["地点1"], "stocks": ["股票代码1"], "currencies": ["货币1"]}}
"""
            
            async with self.ai_client as client:
                response = await client.chat_completion(
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=300,
                    temperature=0.1
                )
            
            entities_text = response.get("content", "").strip()
            
            # 尝试解析JSON
            try:
                entities = json.loads(entities_text)
                
                # 转换为标准字段名
                result = {}
                if entities.get("companies"):
                    result["mentioned_companies"] = entities["companies"]
                if entities.get("people"):
                    result["mentioned_people"] = entities["people"]
                if entities.get("locations"):
                    result["mentioned_locations"] = entities["locations"]
                if entities.get("stocks"):
                    # 股票信息现在通过统一标签系统处理
                    result["mentioned_stocks"] = entities["stocks"]
                if entities.get("currencies"):
                    result["mentioned_currencies"] = entities["currencies"]
                
                logger.debug(f"实体识别: {result}")
                return result
                
            except json.JSONDecodeError:
                logger.warning(f"实体识别结果不是有效JSON: {entities_text}")
                return {}
                
        except Exception as e:
            logger.error(f"实体识别失败: {e}")
            return {}

    async def _extract_entities_with_client(self, client, data: Dict[str, Any]) -> Dict[str, List[str]]:
        """使用指定客户端进行实体识别"""

        content = data.get("content", "")
        title = data.get("title", "")

        if not content and not title:
            return {}

        try:
            prompt = f"""
请从以下财经内容中识别实体，返回JSON格式：

标题：{title}
内容：{content[:1500]}

请识别：
1. 公司名称（companies）
2. 人物姓名（people）
3. 地点名称（locations）
4. 股票代码（stocks）
5. 货币名称（currencies）

返回格式：
{{"companies": ["公司1", "公司2"], "people": ["人物1"], "locations": ["地点1"], "stocks": ["股票1"], "currencies": ["货币1"]}}
"""

            response = await client.chat_completion(
                messages=[{"role": "user", "content": prompt}],
                max_tokens=300,
                temperature=0.1
            )

            entities_text = response.get("content", "").strip()

            # 尝试解析JSON
            try:
                clean_text = clean_json_response(entities_text)
                entities = json.loads(clean_text)

                # 转换为标准字段名
                result = {}
                if entities.get("companies"):
                    result["mentioned_companies"] = entities["companies"]
                if entities.get("people"):
                    result["mentioned_people"] = entities["people"]
                if entities.get("locations"):
                    result["mentioned_locations"] = entities["locations"]
                if entities.get("stocks"):
                    # 股票信息现在通过统一标签系统处理
                    result["mentioned_stocks"] = entities["stocks"]
                if entities.get("currencies"):
                    result["mentioned_currencies"] = entities["currencies"]

                logger.debug(f"实体识别: {result}")
                return result

            except json.JSONDecodeError:
                logger.warning(f"实体识别结果不是有效JSON: {entities_text}")
                return {}

        except Exception as e:
            logger.error(f"实体识别失败: {e}")
            return {}

    async def _analyze_sentiment(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """情感分析"""
        
        content = data.get("content", "")
        title = data.get("title", "")
        
        if not content and not title:
            return None
            
        try:
            prompt = f"""
请对以下财经内容进行情感分析，返回JSON格式：

标题：{title}
内容：{content[:1000]}

请分析：
1. 整体情感倾向：positive(积极)/neutral(中性)/negative(消极)
2. 市场情感：bullish(看涨)/neutral(中性)/bearish(看跌)
3. 情感强度：0.0-1.0之间的数值

返回格式：
{{"sentiment": "情感倾向", "market_sentiment": "市场情感", "confidence": 0.8}}
"""
            
            async with self.ai_client as client:
                response = await client.chat_completion(
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=150,
                    temperature=0.1
                )
            
            sentiment_text = response.get("content", "").strip()
            
            # 尝试解析JSON
            try:
                sentiment = json.loads(sentiment_text)
                logger.debug(f"情感分析: {sentiment}")
                return sentiment
                
            except json.JSONDecodeError:
                logger.warning(f"情感分析结果不是有效JSON: {sentiment_text}")
                return None
                
        except Exception as e:
            logger.error(f"情感分析失败: {e}")
            return None

    async def _analyze_sentiment_with_client(self, client, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """使用指定客户端进行情感分析"""

        content = data.get("content", "")
        title = data.get("title", "")

        if not content and not title:
            return None

        try:
            prompt = f"""
请对以下财经内容进行情感分析，返回JSON格式：

标题：{title}
内容：{content[:1000]}

请分析：
1. 整体情感倾向：positive(积极)/neutral(中性)/negative(消极)
2. 市场情感：bullish(看涨)/neutral(中性)/bearish(看跌)
3. 情感强度：0.0-1.0之间的数值

返回格式：
{{"sentiment": "情感倾向", "market_sentiment": "市场情感", "confidence": 0.8}}
"""

            response = await client.chat_completion(
                messages=[{"role": "user", "content": prompt}],
                max_tokens=150,
                temperature=0.1
            )

            sentiment_text = response.get("content", "").strip()

            # 尝试解析JSON
            try:
                clean_text = clean_json_response(sentiment_text)
                sentiment = json.loads(clean_text)
                logger.debug(f"情感分析: {sentiment}")
                return sentiment

            except json.JSONDecodeError:
                logger.warning(f"情感分析结果不是有效JSON: {sentiment_text}")
                return None

        except Exception as e:
            logger.error(f"情感分析失败: {e}")
            return None

    async def _calculate_importance(self, data: Dict[str, Any]) -> float:
        """计算重要性评分"""
        
        content = data.get("content", "")
        title = data.get("title", "")
        
        if not content and not title:
            return 0.5
            
        try:
            # 基于关键词的重要性评分
            importance_keywords = {
                "央行": 0.9, "降准": 0.8, "加息": 0.8, "货币政策": 0.7,
                "IPO": 0.7, "重组": 0.6, "并购": 0.6, "业绩": 0.5,
                "监管": 0.7, "政策": 0.6, "法规": 0.5,
                "突发": 0.9, "紧急": 0.8, "重大": 0.7
            }
            
            text = f"{title} {content}".lower()
            score = 0.5  # 基础分数
            
            for keyword, weight in importance_keywords.items():
                if keyword in text:
                    score = min(1.0, score + weight * 0.1)
            
            # 基于内容长度调整
            if len(content) > 1000:
                score = min(1.0, score + 0.1)
            elif len(content) < 100:
                score = max(0.1, score - 0.2)
            
            logger.debug(f"重要性评分: {score}")
            return round(score, 2)
            
        except Exception as e:
            logger.error(f"计算重要性评分失败: {e}")
            return 0.5

    async def match_tags_to_standard(
        self,
        ai_tags: List[str],
        content_type: str,
        content_id: int,
        confidence_threshold: float = 0.6
    ) -> List[Dict[str, Any]]:
        """
        将AI提取的标签匹配到标准标签库并保存结果

        Args:
            ai_tags: AI提取的标签列表
            content_type: 内容类型
            content_id: 内容ID
            confidence_threshold: 置信度阈值，低于此值的匹配将被忽略

        Returns:
            List: 匹配结果列表
        """

        db = SessionLocal()
        matched_tags = []

        try:
            for ai_tag in ai_tags:
                # 查找匹配的标准标签（如果置信度足够高会自动创建新标签）
                match_result = await self._find_matching_standard_tag(db, ai_tag, create_new_threshold=0.8)

                if match_result and match_result["confidence"] >= confidence_threshold:
                    # 保存AI标签匹配记录
                    ai_tag_match = await self._save_ai_tag_match(db, ai_tag, match_result)

                    # 如果找到了标准标签，创建内容标签关联
                    if match_result["tag_id"]:
                        await self._save_unified_content_tag(
                            db, content_type, content_id,
                            match_result["tag_id"], match_result["confidence"],
                            ai_tag_match.id if ai_tag_match else None
                        )

                    matched_tags.append({
                        "ai_tag": ai_tag,
                        "standard_tag_id": match_result["tag_id"],
                        "confidence": match_result["confidence"],
                        "match_method": match_result["method"],
                        "ai_tag_match_id": ai_tag_match.id if ai_tag_match else None
                    })
                else:
                    logger.debug(f"标签 '{ai_tag}' 置信度 {match_result['confidence'] if match_result else 0} 低于阈值 {confidence_threshold}，跳过")

            db.commit()

        except Exception as e:
            logger.error(f"标签匹配和保存失败: {e}")
            db.rollback()
            raise
        finally:
            db.close()

        return matched_tags

    async def match_tags_only(
        self,
        ai_tags: List[str],
        confidence_threshold: float = 0.6
    ) -> List[Dict[str, Any]]:
        """
        只匹配AI提取的标签到标准标签库，不保存关联关系
        用于没有具体内容ID时的标签匹配
        """
        db = SessionLocal()
        matched_tags = []

        try:
            for ai_tag in ai_tags:
                # 查找匹配的标准标签
                match_result = await self._find_matching_standard_tag(db, ai_tag, create_new_threshold=0.8)

                if match_result and match_result["confidence"] >= confidence_threshold:
                    matched_tags.append({
                        "ai_tag": ai_tag,
                        "standard_tag_id": match_result["tag_id"],
                        "confidence": match_result["confidence"],
                        "match_method": match_result["method"]
                    })
                else:
                    logger.debug(f"标签 '{ai_tag}' 置信度 {match_result['confidence'] if match_result else 0} 低于阈值 {confidence_threshold}，跳过")

            return matched_tags

        except Exception as e:
            logger.error(f"标签匹配失败: {e}")
            return []
        finally:
            db.close()

    async def _find_matching_standard_tag(
        self,
        db: Session,
        ai_tag: str,
        create_new_threshold: float = 0.8
    ) -> Optional[Dict[str, Any]]:
        """查找匹配的标准标签，如果置信度足够高则创建新标签"""

        # 1. 精确匹配
        exact_match = db.query(Tag).filter(Tag.tag_name == ai_tag).first()
        if exact_match:
            return {
                "tag_id": exact_match.id,
                "confidence": 1.0,
                "method": "exact"
            }

        # 2. 同义词匹配 (暂时跳过，因为SQL语法问题)
        # TODO: 修复同义词匹配的SQL语法
        synonym_match = None
        # synonym_match = db.query(Tag).filter(
        #     text("synonyms @> ARRAY[:ai_tag]").params(ai_tag=ai_tag)
        # ).first()
        if synonym_match:
            return {
                "tag_id": synonym_match.id,
                "confidence": 0.9,
                "method": "synonym"
            }

        # 3. 模糊匹配（简单的包含关系）
        fuzzy_matches = db.query(Tag).filter(
            Tag.tag_name.ilike(f"%{ai_tag}%")
        ).all()

        if fuzzy_matches:
            # 选择最相似的
            best_match = fuzzy_matches[0]
            return {
                "tag_id": best_match.id,
                "confidence": 0.7,
                "method": "fuzzy"
            }

        # 4. 未找到匹配，评估是否创建新标签
        ai_confidence = await self._evaluate_ai_tag_quality(ai_tag)

        if ai_confidence >= create_new_threshold:
            # 创建新标签
            new_tag = await self._create_new_tag(db, ai_tag, ai_confidence)
            if new_tag:
                return {
                    "tag_id": new_tag.id,
                    "confidence": ai_confidence,
                    "method": "created"
                }

        # 5. 置信度不足，标记为新标签但不创建
        return {
            "tag_id": None,
            "confidence": ai_confidence,
            "method": "new"
        }

    async def _save_ai_tag_match(
        self,
        db: Session,
        ai_tag: str,
        match_result: Dict[str, Any]
    ) -> Optional[AITagMatches]:
        """保存AI标签匹配记录"""

        try:
            # 检查是否已存在相同的匹配记录
            existing_match = db.query(AITagMatches).filter(
                AITagMatches.ai_tag_text == ai_tag,
                AITagMatches.standard_tag_id == match_result["tag_id"],
                AITagMatches.match_method == match_result["method"]
            ).first()

            if existing_match:
                # 更新使用次数和成功率
                existing_match.usage_count += 1
                existing_match.success_rate = min(1.0, existing_match.success_rate + 0.1)
                return existing_match

            # 创建新的匹配记录
            ai_tag_match = AITagMatches(
                ai_tag_text=ai_tag,
                standard_tag_id=match_result["tag_id"],
                confidence_score=match_result["confidence"],
                match_method=match_result["method"],
                similarity_score=match_result.get("similarity", match_result["confidence"]),
                usage_count=1,
                success_rate=0.8  # 初始成功率
            )

            db.add(ai_tag_match)
            db.flush()  # 获取ID但不提交

            logger.debug(f"保存AI标签匹配: {ai_tag} -> {match_result['tag_id']} ({match_result['method']})")
            return ai_tag_match

        except Exception as e:
            logger.error(f"保存AI标签匹配失败: {e}")
            return None

    async def _save_unified_content_tag(
        self,
        db: Session,
        content_type: str,
        content_id: int,
        tag_id: int,
        confidence: float,
        ai_tag_match_id: Optional[int] = None
    ) -> Optional[UnifiedContentTags]:
        """保存统一内容标签关联"""

        try:
            # 检查是否已存在相同的关联
            existing_tag = db.query(UnifiedContentTags).filter(
                UnifiedContentTags.content_type == content_type,
                UnifiedContentTags.content_id == content_id,
                UnifiedContentTags.tag_id == tag_id
            ).first()

            if existing_tag:
                # 更新置信度和提及次数
                existing_tag.confidence_score = max(existing_tag.confidence_score, confidence)
                existing_tag.mention_count += 1
                return existing_tag

            # 创建新的内容标签关联
            content_tag = UnifiedContentTags(
                content_type=content_type,
                content_id=content_id,
                tag_id=tag_id,
                relevance_score=confidence,
                confidence_score=confidence,
                importance_score=min(1.0, confidence + 0.2),  # 重要性稍高于置信度
                source="ai",
                ai_tag_match_id=ai_tag_match_id,
                mention_count=1
            )

            db.add(content_tag)
            db.flush()  # 获取ID但不提交

            logger.debug(f"保存内容标签关联: {content_type}:{content_id} -> tag:{tag_id}")
            return content_tag

        except Exception as e:
            logger.error(f"保存内容标签关联失败: {e}")
            return None

    async def _evaluate_ai_tag_quality(self, ai_tag: str) -> float:
        """评估AI标签的质量，返回置信度分数"""

        confidence = 0.5  # 基础置信度

        # 1. 长度检查（2-20个字符比较合理）
        if 2 <= len(ai_tag) <= 20:
            confidence += 0.1
        elif len(ai_tag) > 20:
            confidence -= 0.2

        # 2. 字符类型检查（中文、英文、数字组合）
        import re
        if re.match(r'^[\u4e00-\u9fa5a-zA-Z0-9\-_]+$', ai_tag):
            confidence += 0.1

        # 3. 财经领域关键词检查
        financial_keywords = [
            '银行', '股票', '基金', '债券', '期货', '外汇', '保险', '证券',
            '央行', '货币', '政策', '利率', '通胀', 'GDP', 'CPI', 'PMI',
            '上市', '并购', '重组', '分红', '配股', '增发', '回购',
            '涨停', '跌停', '熔断', '停牌', '复牌', '退市',
            '美联储', '欧央行', '日央行', '英央行',
            'A股', 'H股', '港股', '美股', '创业板', '科创板',
            '沪深', '上证', '深证', '中证', '恒生', '纳斯达克', '道琼斯',
            '人民币', '美元', '欧元', '英镑', '日元', '港币'
        ]

        for keyword in financial_keywords:
            if keyword in ai_tag:
                confidence += 0.2
                break

        # 4. 避免过于通用的词汇
        generic_words = ['新闻', '消息', '报道', '信息', '数据', '情况', '问题', '方面']
        if ai_tag in generic_words:
            confidence -= 0.3

        # 5. 避免特殊字符和数字开头
        if ai_tag[0].isdigit() or not ai_tag[0].isalnum():
            confidence -= 0.2

        return max(0.0, min(1.0, confidence))

    async def _create_new_tag(
        self,
        db: Session,
        ai_tag: str,
        confidence: float
    ) -> Optional[Tag]:
        """创建新标签"""

        try:
            # 生成标签代码（去除特殊字符，转换为小写）
            import re
            tag_code = re.sub(r'[^\w\-]', '_', ai_tag.lower())
            tag_slug = tag_code.replace('_', '-')

            # 确保代码唯一性
            counter = 1
            original_code = tag_code
            original_slug = tag_slug

            while db.query(Tag).filter(Tag.tag_code == tag_code).first():
                tag_code = f"{original_code}_{counter}"
                tag_slug = f"{original_slug}-{counter}"
                counter += 1

            # 获取默认标签类型（如果存在）
            from ..tag_classification_service.models import TagType
            default_tag_type = db.query(TagType).filter(
                TagType.type_code == 'general'
            ).first()

            if not default_tag_type:
                # 如果没有默认类型，创建一个
                default_tag_type = TagType(
                    type_code='general',
                    type_name='通用标签',
                    description='AI自动生成的通用标签类型'
                )
                db.add(default_tag_type)
                db.flush()

            # 创建新标签
            new_tag = Tag(
                tag_name=ai_tag,
                tag_code=tag_code,
                tag_slug=tag_slug,
                tag_type_id=default_tag_type.id,
                description=f"AI自动生成的标签，置信度: {confidence:.2f}",
                base_weight=confidence,
                quality_weight=confidence,
                is_active=True,
                is_system=False,
                usage_count=1
            )

            db.add(new_tag)
            db.flush()  # 获取ID但不提交

            logger.info(f"创建新标签: {ai_tag} (ID: {new_tag.id}, 置信度: {confidence:.2f})")
            return new_tag

        except Exception as e:
            logger.error(f"创建新标签失败: {e}")
            return None

    # ============================================================================
    # 分类匹配和关联方法
    # ============================================================================

    async def match_classifications_to_standard(
        self,
        ai_classifications: Dict[str, str],
        content_type: str,
        content_id: int,
        confidence_threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """
        将AI提取的分类匹配到标准分类库并保存结果

        Args:
            ai_classifications: AI提取的分类字典，如 {"primary_category": "market_news", "impact_scope": "global"}
            content_type: 内容类型
            content_id: 内容ID
            confidence_threshold: 置信度阈值

        Returns:
            List: 匹配结果列表
        """
        db = SessionLocal()
        matched_classifications = []

        try:
            for dimension_key, classification_value in ai_classifications.items():
                if not classification_value:
                    continue

                # 处理列表类型的分类值
                values_to_process = []
                if isinstance(classification_value, list):
                    values_to_process = classification_value
                else:
                    values_to_process = [classification_value]

                # 对每个分类值进行匹配
                for single_value in values_to_process:
                    if not single_value:
                        continue

                    # 查找匹配的标准分类
                    match_result = await self._find_matching_standard_classification(
                        db, dimension_key, single_value, create_new_threshold=0.8
                    )

                    if match_result and match_result["confidence"] >= confidence_threshold:
                        # 保存内容分类关联
                        await self._save_unified_content_classification(
                            db, content_type, content_id,
                            match_result["dimension_id"], match_result["value_id"],
                            match_result["confidence"]
                        )

                        matched_classifications.append({
                            "dimension_key": dimension_key,
                            "ai_classification_value": single_value,
                            "dimension_id": match_result["dimension_id"],
                            "value_id": match_result["value_id"],
                            "confidence": match_result["confidence"],
                            "match_method": match_result["method"]
                        })
                    else:
                        logger.debug(f"分类 '{dimension_key}:{single_value}' 置信度 {match_result['confidence'] if match_result else 0} 低于阈值 {confidence_threshold}，跳过")

            db.commit()

        except Exception as e:
            logger.error(f"分类匹配和保存失败: {e}")
            db.rollback()
            raise
        finally:
            db.close()

        return matched_classifications

    async def match_classifications_only(
        self,
        ai_classifications: Dict[str, str],
        confidence_threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """
        只匹配AI提取的分类到标准分类库，不保存关联关系
        用于没有具体内容ID时的分类匹配
        """
        db = SessionLocal()
        matched_classifications = []

        try:
            for dimension_key, classification_value in ai_classifications.items():
                if not classification_value:
                    continue

                # 处理列表类型的分类值
                values_to_process = []
                if isinstance(classification_value, list):
                    values_to_process = classification_value
                else:
                    values_to_process = [classification_value]

                # 对每个分类值进行匹配
                for single_value in values_to_process:
                    if not single_value:
                        continue

                    # 查找匹配的标准分类
                    match_result = await self._find_matching_standard_classification(
                        db, dimension_key, single_value, create_new_threshold=0.8
                    )

                    if match_result and match_result["confidence"] >= confidence_threshold:
                        matched_classifications.append({
                            "dimension_key": dimension_key,
                            "ai_classification_value": single_value,
                            "dimension_id": match_result["dimension_id"],
                            "value_id": match_result["value_id"],
                            "confidence": match_result["confidence"],
                            "match_method": match_result["method"]
                        })
                    else:
                        logger.debug(f"分类 '{dimension_key}:{single_value}' 置信度 {match_result['confidence'] if match_result else 0} 低于阈值 {confidence_threshold}，跳过")

            return matched_classifications

        except Exception as e:
            logger.error(f"分类匹配失败: {e}")
            return []
        finally:
            db.close()

    async def _find_matching_standard_classification(
        self,
        db: Session,
        dimension_key: str,
        classification_value: str,
        create_new_threshold: float = 0.8
    ) -> Optional[Dict[str, Any]]:
        """查找匹配的标准分类，如果置信度足够高则创建新分类"""

        # 定义维度映射关系
        dimension_mapping = {
            "primary_category": "content_category",
            "impact_scope": "impact_scope",
            "urgency_level": "urgency",  # 修正：数据库中是urgency，不是urgency_level
            "industry": "industry",
            "market_sentiment": "sentiment"  # 修正：数据库中是sentiment，不是market_sentiment
        }

        # 获取对应的维度名称
        dimension_name = dimension_mapping.get(dimension_key, dimension_key)

        # 1. 查找分类维度
        dimension = db.query(ClassificationDimension).filter(
            ClassificationDimension.dimension_name == dimension_name
        ).first()

        if not dimension:
            # 如果维度不存在，尝试创建新维度
            if create_new_threshold <= 0.8:  # 只有在阈值足够低时才创建新维度
                dimension = await self._create_new_classification_dimension(db, dimension_name, dimension_key)
                if not dimension:
                    return None
            else:
                logger.debug(f"未找到分类维度: {dimension_name}")
                return None

        # 2. 在该维度下查找匹配的分类值
        # 精确匹配 (使用value_code字段)
        exact_match = db.query(ClassificationValue).filter(
            ClassificationValue.dimension_id == dimension.id,
            ClassificationValue.value_code == classification_value
        ).first()

        if exact_match:
            return {
                "dimension_id": dimension.id,
                "value_id": exact_match.id,
                "confidence": 1.0,
                "method": "exact"
            }

        # 3. 模糊匹配（包含关系，使用value_code和display_name）
        fuzzy_matches = db.query(ClassificationValue).filter(
            ClassificationValue.dimension_id == dimension.id,
            (ClassificationValue.value_code.ilike(f"%{classification_value}%") |
             ClassificationValue.display_name.ilike(f"%{classification_value}%"))
        ).all()

        if fuzzy_matches:
            # 选择最相似的匹配
            best_match = fuzzy_matches[0]  # 简单选择第一个，后续可以优化为相似度计算
            return {
                "dimension_id": dimension.id,
                "value_id": best_match.id,
                "confidence": 0.8,
                "method": "fuzzy"
            }

        # 4. 如果没有匹配且置信度足够高，创建新的分类值
        if create_new_threshold <= 0.8:
            new_value = await self._create_new_classification_value(db, dimension.id, classification_value)
            if new_value:
                return {
                    "dimension_id": dimension.id,
                    "value_id": new_value.id,
                    "confidence": 0.9,
                    "method": "new"
                }

        return None

    async def _save_unified_content_classification(
        self,
        db: Session,
        content_type: str,
        content_id: int,
        dimension_id: int,
        value_id: int,
        confidence: float
    ) -> Optional[UnifiedContentClassifications]:
        """保存统一内容分类关联"""

        try:
            # 检查是否已存在相同的关联
            existing_classification = db.query(UnifiedContentClassifications).filter(
                UnifiedContentClassifications.content_type == content_type,
                UnifiedContentClassifications.content_id == content_id,
                UnifiedContentClassifications.dimension_id == dimension_id,
                UnifiedContentClassifications.value_id == value_id
            ).first()

            if existing_classification:
                # 更新置信度
                existing_classification.confidence_score = max(existing_classification.confidence_score, confidence)
                return existing_classification

            # 创建新的内容分类关联
            content_classification = UnifiedContentClassifications(
                content_type=content_type,
                content_id=content_id,
                dimension_id=dimension_id,
                value_id=value_id,
                confidence_score=confidence,
                source="ai"
            )

            db.add(content_classification)
            db.flush()  # 获取ID但不提交

            logger.debug(f"保存内容分类关联: {content_type}:{content_id} -> {dimension_id}:{value_id}")
            return content_classification

        except Exception as e:
            logger.error(f"保存内容分类关联失败: {e}")
            return None

    async def _create_new_classification_dimension(
        self,
        db: Session,
        dimension_name: str,
        dimension_key: str
    ) -> Optional[ClassificationDimension]:
        """创建新的分类维度"""

        try:
            # 确保维度名称唯一性
            counter = 1
            original_name = dimension_name
            while db.query(ClassificationDimension).filter(ClassificationDimension.dimension_name == dimension_name).first():
                dimension_name = f"{original_name}_{counter}"
                counter += 1

            new_dimension = ClassificationDimension(
                dimension_name=dimension_name,
                display_name=dimension_name.replace('_', ' ').title(),
                description=f"AI自动生成的分类维度: {dimension_key}",
                is_active=True
            )

            db.add(new_dimension)
            db.flush()

            logger.info(f"创建新分类维度: {dimension_name}")
            return new_dimension

        except Exception as e:
            logger.error(f"创建新分类维度失败: {e}")
            return None

    async def _create_new_classification_value(
        self,
        db: Session,
        dimension_id: int,
        value_name: str
    ) -> Optional[ClassificationValue]:
        """创建新的分类值"""

        try:
            # 生成值代码
            value_code = re.sub(r'[^\w\-]', '_', value_name.lower())

            # 确保在该维度下代码唯一性
            counter = 1
            original_code = value_code
            while db.query(ClassificationValue).filter(
                ClassificationValue.dimension_id == dimension_id,
                ClassificationValue.value_code == value_code
            ).first():
                value_code = f"{original_code}_{counter}"
                counter += 1

            new_value = ClassificationValue(
                dimension_id=dimension_id,
                value_code=value_code,
                display_name=value_name.replace('_', ' ').title(),
                description=f"AI自动生成的分类值",
                is_active=True
            )

            db.add(new_value)
            db.flush()

            logger.info(f"创建新分类值: {value_name} (代码: {value_code})")
            return new_value

        except Exception as e:
            logger.error(f"创建新分类值失败: {e}")
            return None
