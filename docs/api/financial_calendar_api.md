# 财经日历API文档

## 概述

财经日历API提供全球主要国家和地区的经济数据发布时间表，包括重要经济指标、央行会议、政府报告等财经事件信息。

## 基础信息

- **基础路径**: `/api/financial-calendar`
- **认证方式**: Bear<PERSON>
- **数据格式**: JSON

## API接口列表

### 1. 财经事件管理

#### 1.1 创建财经事件
- **接口**: `POST /events`
- **描述**: 创建新的财经事件
- **权限**: 管理员

**请求参数**:
```json
{
  "event_title": "美国非农就业人数",
  "event_title_en": "US Non-Farm Payrolls",
  "event_type_id": 1,
  "country_id": 1,
  "indicator_id": 1,
  "scheduled_time": "2024-01-05T13:30:00Z",
  "importance_level": 3,
  "market_impact": "high",
  "previous_value": 250.5,
  "forecast_value": 260.0,
  "value_unit": "千人",
  "event_description": "美国劳工部发布的非农就业人数数据"
}
```

**响应示例**:
```json
{
  "id": 1,
  "event_title": "美国非农就业人数",
  "event_title_en": "US Non-Farm Payrolls",
  "event_type_id": 1,
  "country_id": 1,
  "indicator_id": 1,
  "scheduled_time": "2024-01-05T13:30:00Z",
  "importance_level": 3,
  "market_impact": "high",
  "previous_value": 250.5,
  "forecast_value": 260.0,
  "actual_value": null,
  "value_unit": "千人",
  "event_status": "scheduled",
  "created_at": "2024-01-01T10:00:00Z"
}
```

#### 1.2 获取单个财经事件
- **接口**: `GET /events/{event_id}`
- **描述**: 获取指定ID的财经事件详情

**响应示例**:
```json
{
  "id": 1,
  "event_title": "美国非农就业人数",
  "country": {
    "id": 1,
    "country_name": "美国",
    "country_code": "US"
  },
  "event_type": {
    "id": 1,
    "type_name": "就业数据",
    "category": "economic_indicator"
  },
  "scheduled_time": "2024-01-05T13:30:00Z",
  "importance_level": 3,
  "market_impact": "high"
}
```

#### 1.3 更新财经事件
- **接口**: `PUT /events/{event_id}`
- **描述**: 更新财经事件信息
- **权限**: 管理员

#### 1.4 删除财经事件
- **接口**: `DELETE /events/{event_id}`
- **描述**: 删除财经事件
- **权限**: 管理员

### 2. 财经事件查询

#### 2.1 获取事件列表
- **接口**: `GET /events`
- **描述**: 获取财经事件列表，支持分页和过滤

**查询参数**:
- `page`: 页码（默认: 1）
- `page_size`: 每页数量（默认: 20）
- `country_id`: 国家ID
- `event_type_id`: 事件类型ID
- `importance_level`: 重要性级别
- `start_date`: 开始日期
- `end_date`: 结束日期
- `market_impact`: 市场影响程度

#### 2.2 获取即将到来的事件
- **接口**: `GET /events/upcoming`
- **描述**: 获取未来24小时内的财经事件

**查询参数**:
- `hours`: 时间范围（小时，默认: 24）
- `importance_levels`: 重要性级别列表

#### 2.3 获取高影响力事件
- **接口**: `GET /events/high-impact`
- **描述**: 获取高影响力的财经事件

**查询参数**:
- `days`: 天数范围（默认: 7）

#### 2.4 按国家获取事件
- **接口**: `GET /events/by-country/{country_id}`
- **描述**: 获取指定国家的财经事件

#### 2.5 搜索事件
- **接口**: `GET /events/search`
- **描述**: 通过关键词搜索财经事件

**查询参数**:
- `q`: 搜索关键词
- `limit`: 结果数量限制（默认: 50）

### 3. 数据更新

#### 3.1 更新事件实际值
- **接口**: `PATCH /events/{event_id}/actual-value`
- **描述**: 更新财经事件的实际公布值
- **权限**: 管理员

**请求参数**:
```json
{
  "actual_value": 265.3,
  "actual_time": "2024-01-05T13:30:00Z"
}
```

### 4. 基础数据查询

#### 4.1 获取国家列表
- **接口**: `GET /countries`
- **描述**: 获取支持的国家和地区列表

**响应示例**:
```json
[
  {
    "id": 1,
    "country_code": "US",
    "country_name": "美国",
    "country_name_en": "United States",
    "region": "North America",
    "primary_currency": "USD",
    "time_zone": "America/New_York"
  }
]
```

#### 4.2 获取事件类型列表
- **接口**: `GET /event-types`
- **描述**: 获取财经事件类型列表

**响应示例**:
```json
[
  {
    "id": 1,
    "type_code": "employment_data",
    "type_name": "就业数据",
    "type_name_en": "Employment Data",
    "category": "economic_indicator",
    "default_importance": 3
  }
]
```

### 5. 统计分析

#### 5.1 获取日历摘要
- **接口**: `GET /calendar/summary`
- **描述**: 获取指定时间段的财经日历统计摘要

**查询参数**:
- `start_date`: 开始日期
- `end_date`: 结束日期

**响应示例**:
```json
{
  "total_events": 125,
  "high_impact_events": 15,
  "countries_count": 8,
  "event_types_distribution": {
    "employment_data": 5,
    "inflation_data": 8,
    "central_bank_meeting": 2
  },
  "daily_distribution": {
    "2024-01-05": 8,
    "2024-01-06": 5,
    "2024-01-07": 12
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未认证 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 422 | 参数验证失败 |
| 500 | 服务器内部错误 |

## 数据模型

### 财经事件模型

```json
{
  "id": "事件ID",
  "event_title": "事件标题",
  "event_title_en": "英文事件标题",
  "event_type_id": "事件类型ID",
  "country_id": "国家ID",
  "indicator_id": "经济指标ID（可选）",
  "scheduled_time": "计划时间",
  "actual_time": "实际时间（可选）",
  "importance_level": "重要性级别（1-3）",
  "market_impact": "市场影响程度",
  "previous_value": "前值",
  "forecast_value": "预期值",
  "actual_value": "实际值（可选）",
  "value_unit": "数值单位",
  "event_description": "事件描述",
  "event_status": "事件状态",
  "created_at": "创建时间",
  "updated_at": "更新时间"
}
```

## 使用示例

### 获取今日重要事件

```bash
curl -X GET "https://api.finsight.com/financial-calendar/events?start_date=2024-01-05&end_date=2024-01-05&importance_level=3" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 订阅美国就业数据

```bash
curl -X GET "https://api.finsight.com/financial-calendar/events?country_id=1&event_type_id=1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 注意事项

1. 所有时间均为UTC时间
2. 重要性级别：1=低，2=中，3=高
3. 市场影响程度：low, medium, high
4. 事件状态：scheduled, published, cancelled, delayed
5. API限流：每分钟最多100次请求 