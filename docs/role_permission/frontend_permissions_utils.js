/**
 * FinSight 权限管理工具类
 * 
 * 提供前端权限检查、角色判断、权限验证等功能
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

// 权限常量定义
export const PERMISSIONS = {
  // 用户管理
  USER: {
    PROFILE_READ: 'user.profile.read',
    PROFILE_UPDATE: 'user.profile.update',
    LIST_READ: 'user.list.read',
    DETAIL_READ: 'user.detail.read',
    CREATE: 'user.create',
    UPDATE: 'user.update',
    DELETE: 'user.delete',
    STATUS_UPDATE: 'user.status.update',
    ROLE_ASSIGN: 'user.role.assign',
    ROLE_REVOKE: 'user.role.revoke',
    ROLE_READ: 'user.role.read',
    PERMISSION_READ: 'user.permission.read',
    ROLE_BATCH_ASSIGN: 'user.role.batch.assign',
    ROLE_BATCH_REVOKE: 'user.role.batch.revoke',
    PREFERENCES_READ: 'user.preferences.read',
    PREFERENCES_UPDATE: 'user.preferences.update'
  },
  
  // 权限管理
  PERMISSION: {
    CREATE: 'permission.create',
    READ: 'permission.read',
    UPDATE: 'permission.update',
    DELETE: 'permission.delete',
    LIST_READ: 'permission.list.read',
    ANALYTICS_READ: 'permission.analytics.read'
  },
  
  // 角色管理
  ROLE: {
    CREATE: 'role.create',
    READ: 'role.read',
    UPDATE: 'role.update',
    DELETE: 'role.delete',
    LIST_READ: 'role.list.read',
    PERMISSION_ASSIGN: 'role.permission.assign',
    PERMISSION_REVOKE: 'role.permission.revoke',
    PERMISSION_READ: 'role.permission.read',
    ANALYTICS_READ: 'role.analytics.read'
  },
  
  // 数据管理
  DATA: {
    SOURCE_READ: 'data.source.read',
    SOURCE_CREATE: 'data.source.create',
    SOURCE_UPDATE: 'data.source.update',
    SOURCE_DELETE: 'data.source.delete',
    TASK_READ: 'data.task.read',
    TASK_CREATE: 'data.task.create',
    TASK_UPDATE: 'data.task.update',
    TASK_EXECUTE: 'data.task.execute',
    TASK_DELETE: 'data.task.delete',
    QUERY_EXECUTE: 'data.query.execute',
    REPORT_READ: 'data.report.read',
    REPORT_CREATE: 'data.report.create',
    REPORT_UPDATE: 'data.report.update',
    REPORT_PUBLISH: 'data.report.publish',
    REPORT_DELETE: 'data.report.delete',
    EXPORT: 'data.export'
  },

  // 数据源管理
  DATA_SOURCE: {
    DATA_SOURCE_CREATE: 'data_source.data_source.create',
    DATA_SOURCE_READ: 'data_source.data_source.read',
    DATA_SOURCE_UPDATE: 'data_source.data_source.update',
    DATA_SOURCE_DELETE: 'data_source.data_source.delete',
    LIST_READ: 'data_source.list.read',
    STATS_READ: 'data_source.stats.read',
    CONFIG_CREATE: 'data_source.config.create',
    CONFIG_READ: 'data_source.config.read',
    CONFIG_UPDATE: 'data_source.config.update',
    CONFIG_DELETE: 'data_source.config.delete',
    CONFIG_MANAGE: 'data_source.config.manage'
  },

  // 原始数据记录
  RAW_DATA_RECORD: {
    RAW_DATA_RECORD_CREATE: 'raw_data_record.raw_data_record.create',
    RAW_DATA_RECORD_READ: 'raw_data_record.raw_data_record.read',
    RAW_DATA_RECORD_UPDATE: 'raw_data_record.raw_data_record.update',
    RAW_DATA_RECORD_DELETE: 'raw_data_record.raw_data_record.delete',
    RAW_DATA_RECORD_MANAGE: 'raw_data_record.raw_data_record.manage',
    RAW_DATA_RECORD_ANALYZE: 'raw_data_record.raw_data_record.analyze',
    LIST_READ: 'raw_data_record.list.read',
    STATS_READ: 'raw_data_record.stats.read'
  },
  
  // 内容管理
  CONTENT: {
    ARTICLE_READ: 'content.article.read',
    ARTICLE_CREATE: 'content.article.create',
    ARTICLE_UPDATE: 'content.article.update',
    ARTICLE_PUBLISH: 'content.article.publish',
    ARTICLE_DELETE: 'content.article.delete',
    COMMENT_MODERATE: 'content.comment.moderate'
  },
  
  // 财经日历
  FINANCIAL: {
    CALENDAR_READ: 'financial.calendar.read',
    CALENDAR_CREATE: 'financial.calendar.create',
    CALENDAR_UPDATE: 'financial.calendar.update',
    CALENDAR_DELETE: 'financial.calendar.delete'
  },
  
  // 标签分类
  TAG: {
    READ: 'tag.read',
    CREATE: 'tag.create',
    UPDATE: 'tag.update',
    DELETE: 'tag.delete',
    TYPE_CREATE: 'tag.type.create',
    TYPE_UPDATE: 'tag.type.update',
    TYPE_DELETE: 'tag.type.delete',
    CATEGORY_CREATE: 'tag.category.create',
    CATEGORY_UPDATE: 'tag.category.update',
    CATEGORY_DELETE: 'tag.category.delete',
    ANALYTICS_READ: 'tag.analytics.read'
  },
  
  // 系统管理
  // AI模型管理权限
  AI_MODEL: {
    MODEL_CREATE: 'ai_model.model.create',
    MODEL_READ: 'ai_model.model.read',
    MODEL_UPDATE: 'ai_model.model.update',
    MODEL_DELETE: 'ai_model.model.delete',
    LIST_READ: 'ai_model.list.read',
    STATUS_UPDATE: 'ai_model.status.update',
    DEFAULT_UPDATE: 'ai_model.default.update',
    METRICS_CREATE: 'ai_model.metrics.create',
    METRICS_READ: 'ai_model.metrics.read',
    ANALYTICS_READ: 'ai_model.analytics.read'
  },

  SYSTEM: {
    CONFIG_READ: 'system.config.read',
    CONFIG_UPDATE: 'system.config.update',
    LOG_READ: 'system.log.read',
    BACKUP: 'system.backup',
    PERMISSION_MANAGE: 'system.permission.manage'
  }
};

// 角色常量定义
export const ROLES = {
  ADMIN: 'Admin',
  DATA_ADMIN: 'DataAdmin',
  ANALYST: 'Analyst',
  EDITOR: 'Editor',
  ACCOUNT_MANAGER: 'AccountManager',
  RISK_OFFICER: 'RiskOfficer',
  USER: 'User',
  GUEST: 'Guest',
  AI_ENGINEER: 'AIEngineer'
};

// 模块常量定义
export const MODULES = {
  USER: 'user',
  PERMISSION: 'permission',
  DATA: 'data',
  CONTENT: 'content',
  FINANCIAL: 'financial',
  TAG: 'tag',
  AI_MODEL: 'ai_model',
  SYSTEM: 'system'
};

/**
 * 权限管理工具类
 */
export class PermissionManager {
  constructor() {
    this.userPermissions = null;
    this.userRoles = null;
    this.permissionCodes = [];
    this.initialized = false;
  }

  /**
   * 初始化用户权限
   * @param {string} token - 用户token
   * @returns {Promise<boolean>} 是否初始化成功
   */
  async init(token) {
    try {
      const response = await fetch('/api/v1/permissions/me', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      this.userPermissions = data.permissions || [];
      this.userRoles = data.roles || [];
      this.permissionCodes = data.permission_codes || [];
      this.initialized = true;

      console.log('权限初始化成功:', {
        roles: this.userRoles.length,
        permissions: this.permissionCodes.length
      });

      return true;
    } catch (error) {
      console.error('权限初始化失败:', error);
      this.initialized = false;
      return false;
    }
  }

  /**
   * 检查是否已初始化
   * @returns {boolean}
   */
  isInitialized() {
    return this.initialized;
  }

  /**
   * 检查用户是否有指定权限
   * @param {string} permissionCode - 权限编码
   * @returns {boolean}
   */
  hasPermission(permissionCode) {
    if (!this.initialized) {
      console.warn('权限管理器未初始化');
      return false;
    }
    return this.permissionCodes.includes(permissionCode);
  }

  /**
   * 检查用户是否有任一权限
   * @param {string[]} permissionCodes - 权限编码数组
   * @returns {boolean}
   */
  hasAnyPermission(permissionCodes) {
    if (!this.initialized) {
      console.warn('权限管理器未初始化');
      return false;
    }
    return permissionCodes.some(code => this.permissionCodes.includes(code));
  }

  /**
   * 检查用户是否有所有权限
   * @param {string[]} permissionCodes - 权限编码数组
   * @returns {boolean}
   */
  hasAllPermissions(permissionCodes) {
    if (!this.initialized) {
      console.warn('权限管理器未初始化');
      return false;
    }
    return permissionCodes.every(code => this.permissionCodes.includes(code));
  }

  /**
   * 检查用户是否有指定角色
   * @param {string} roleName - 角色名称
   * @returns {boolean}
   */
  hasRole(roleName) {
    if (!this.initialized) {
      console.warn('权限管理器未初始化');
      return false;
    }
    return this.userRoles.some(role => role.name === roleName);
  }

  /**
   * 检查用户是否有任一角色
   * @param {string[]} roleNames - 角色名称数组
   * @returns {boolean}
   */
  hasAnyRole(roleNames) {
    if (!this.initialized) {
      console.warn('权限管理器未初始化');
      return false;
    }
    return roleNames.some(roleName => this.hasRole(roleName));
  }

  /**
   * 检查用户是否为管理员
   * @returns {boolean}
   */
  isAdmin() {
    return this.hasRole(ROLES.ADMIN);
  }

  /**
   * 检查用户是否可以访问指定模块
   * @param {string} module - 模块名称
   * @returns {boolean}
   */
  canAccessModule(module) {
    const modulePermissions = this.permissionCodes.filter(code => 
      code.startsWith(module + '.')
    );
    return modulePermissions.length > 0;
  }

  /**
   * 获取用户的权限编码列表
   * @returns {string[]}
   */
  getPermissionCodes() {
    return [...this.permissionCodes];
  }

  /**
   * 获取用户的角色列表
   * @returns {Object[]}
   */
  getRoles() {
    return [...this.userRoles];
  }

  /**
   * 获取用户的权限详情列表
   * @returns {Object[]}
   */
  getPermissions() {
    return [...this.userPermissions];
  }

  /**
   * 过滤用户有权限访问的路由
   * @param {Object[]} routes - 路由配置数组
   * @returns {Object[]}
   */
  filterAuthorizedRoutes(routes) {
    return routes.filter(route => {
      // 如果路由不需要权限，则允许访问
      if (!route.meta?.requiredPermission) {
        return true;
      }

      // 检查用户是否有路由所需权限
      if (Array.isArray(route.meta.requiredPermission)) {
        return this.hasAnyPermission(route.meta.requiredPermission);
      } else {
        return this.hasPermission(route.meta.requiredPermission);
      }
    });
  }

  /**
   * 检查用户是否可以执行指定操作
   * @param {string} module - 模块名称
   * @param {string} resource - 资源名称
   * @param {string} action - 操作名称
   * @returns {boolean}
   */
  canPerformAction(module, resource, action) {
    const permissionCode = `${module}.${resource}.${action}`;
    return this.hasPermission(permissionCode);
  }

  /**
   * 远程检查权限（实时验证）
   * @param {string} permissionCode - 权限编码
   * @param {string} token - 用户token
   * @returns {Promise<boolean>}
   */
  async checkPermissionRemote(permissionCode, token) {
    try {
      const response = await fetch('/api/v1/permissions/check', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          permission_code: permissionCode
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.has_permission || false;
    } catch (error) {
      console.error('远程权限检查失败:', error);
      return false;
    }
  }

  /**
   * 清除权限缓存
   */
  clear() {
    this.userPermissions = null;
    this.userRoles = null;
    this.permissionCodes = [];
    this.initialized = false;
  }
}

/**
 * Vue.js 权限指令
 */
export const permissionDirective = {
  mounted(el, binding) {
    const { value: permissionCode, modifiers } = binding;
    const permissionManager = window.permissionManager || new PermissionManager();

    if (!permissionManager.isInitialized()) {
      console.warn('权限管理器未初始化，无法进行权限检查');
      return;
    }

    let hasPermission = false;
    
    if (Array.isArray(permissionCode)) {
      // 数组权限，根据修饰符决定是 AND 还是 OR
      hasPermission = modifiers.all 
        ? permissionManager.hasAllPermissions(permissionCode)
        : permissionManager.hasAnyPermission(permissionCode);
    } else if (typeof permissionCode === 'string') {
      hasPermission = permissionManager.hasPermission(permissionCode);
    }

    if (!hasPermission) {
      if (modifiers.hide) {
        el.style.display = 'none';
      } else {
        el.parentNode?.removeChild(el);
      }
    }
  },

  updated(el, binding) {
    // 权限状态可能在运行时改变，重新检查
    this.mounted(el, binding);
  }
};

/**
 * Vue.js 角色指令
 */
export const roleDirective = {
  mounted(el, binding) {
    const { value: roleNames, modifiers } = binding;
    const permissionManager = window.permissionManager || new PermissionManager();

    if (!permissionManager.isInitialized()) {
      console.warn('权限管理器未初始化，无法进行角色检查');
      return;
    }

    let hasRole = false;

    if (Array.isArray(roleNames)) {
      hasRole = modifiers.all 
        ? roleNames.every(role => permissionManager.hasRole(role))
        : permissionManager.hasAnyRole(roleNames);
    } else if (typeof roleNames === 'string') {
      hasRole = permissionManager.hasRole(roleNames);
    }

    if (!hasRole) {
      if (modifiers.hide) {
        el.style.display = 'none';
      } else {
        el.parentNode?.removeChild(el);
      }
    }
  },

  updated(el, binding) {
    this.mounted(el, binding);
  }
};

/**
 * React 权限高阶组件
 */
export function withPermission(WrappedComponent, requiredPermission) {
  return function PermissionWrapper(props) {
    const permissionManager = window.permissionManager || new PermissionManager();

    if (!permissionManager.isInitialized()) {
      console.warn('权限管理器未初始化');
      return null;
    }

    let hasPermission = false;

    if (Array.isArray(requiredPermission)) {
      hasPermission = permissionManager.hasAnyPermission(requiredPermission);
    } else if (typeof requiredPermission === 'string') {
      hasPermission = permissionManager.hasPermission(requiredPermission);
    }

    if (!hasPermission) {
      return null;
    }

    return React.createElement(WrappedComponent, props);
  };
}

/**
 * React 权限组件
 */
export function PermissionGate({ 
  permission, 
  role, 
  fallback = null, 
  children,
  requireAll = false 
}) {
  const permissionManager = window.permissionManager || new PermissionManager();

  if (!permissionManager.isInitialized()) {
    console.warn('权限管理器未初始化');
    return fallback;
  }

  // 权限检查
  if (permission) {
    let hasPermission = false;
    
    if (Array.isArray(permission)) {
      hasPermission = requireAll
        ? permissionManager.hasAllPermissions(permission)
        : permissionManager.hasAnyPermission(permission);
    } else {
      hasPermission = permissionManager.hasPermission(permission);
    }

    if (!hasPermission) {
      return fallback;
    }
  }

  // 角色检查
  if (role) {
    let hasRole = false;
    
    if (Array.isArray(role)) {
      hasRole = requireAll
        ? role.every(r => permissionManager.hasRole(r))
        : permissionManager.hasAnyRole(role);
    } else {
      hasRole = permissionManager.hasRole(role);
    }

    if (!hasRole) {
      return fallback;
    }
  }

  return children;
}

/**
 * 创建权限管理器实例
 */
export function createPermissionManager() {
  return new PermissionManager();
}

/**
 * 权限工具函数
 */
export const PermissionUtils = {
  /**
   * 解析权限编码
   * @param {string} permissionCode 
   * @returns {Object}
   */
  parsePermissionCode(permissionCode) {
    const parts = permissionCode.split('.');
    return {
      module: parts[0] || '',
      resource: parts[1] || '',
      action: parts[2] || '',
      fullCode: permissionCode
    };
  },

  /**
   * 构建权限编码
   * @param {string} module 
   * @param {string} resource 
   * @param {string} action 
   * @returns {string}
   */
  buildPermissionCode(module, resource, action) {
    return `${module}.${resource}.${action}`;
  },

  /**
   * 按模块分组权限
   * @param {string[]} permissionCodes 
   * @returns {Object}
   */
  groupPermissionsByModule(permissionCodes) {
    const groups = {};
    
    permissionCodes.forEach(code => {
      const { module } = this.parsePermissionCode(code);
      if (!groups[module]) {
        groups[module] = [];
      }
      groups[module].push(code);
    });

    return groups;
  },

  /**
   * 获取模块的所有权限
   * @param {string} module 
   * @param {string[]} permissionCodes 
   * @returns {string[]}
   */
  getModulePermissions(module, permissionCodes) {
    return permissionCodes.filter(code => code.startsWith(module + '.'));
  }
};

// 默认导出
export default {
  PermissionManager,
  permissionDirective,
  roleDirective,
  withPermission,
  PermissionGate,
  createPermissionManager,
  PermissionUtils,
  PERMISSIONS,
  ROLES,
  MODULES
};