{"roles": {"Admin": {"id": 1, "name": "Admin", "displayName": "系统管理员", "description": "拥有系统最高权限，可管理所有资源和用户", "permissionCount": 46, "isSystem": true}, "DataAdmin": {"id": 2, "name": "DataAdmin", "displayName": "数据管理员", "description": "负责数据源和采集任务管理，包含数据相关权限", "permissionCount": 16, "isSystem": true, "parentRole": "Admin"}, "Analyst": {"id": 3, "name": "Analyst", "displayName": "数据分析师", "description": "负责数据分析和报告生成，可访问分析功能", "permissionCount": 11, "isSystem": true, "parentRole": "DataAdmin"}, "Editor": {"id": 4, "name": "Editor", "displayName": "内容编辑", "description": "负责内容创建和编辑，管理文章和资讯", "permissionCount": 12, "isSystem": true, "parentRole": "Admin"}, "AccountManager": {"id": 5, "name": "Acco<PERSON><PERSON><PERSON><PERSON>", "displayName": "客户经理", "description": "负责用户服务和账户管理", "permissionCount": 7, "isSystem": true, "parentRole": "Admin"}, "RiskOfficer": {"id": 6, "name": "RiskOfficer", "displayName": "风控专员", "description": "负责风险监控和合规检查", "permissionCount": 4, "isSystem": true, "parentRole": "Admin"}, "User": {"id": 7, "name": "User", "displayName": "普通用户", "description": "基础用户，可查看公开内容和个人数据", "permissionCount": 5, "isSystem": true, "parentRole": "Acco<PERSON><PERSON><PERSON><PERSON>"}, "Guest": {"id": 8, "name": "Guest", "displayName": "访客", "description": "未注册或未登录用户，只能访问公开内容", "permissionCount": 2, "isSystem": true}, "AIEngineer": {"id": 9, "name": "AIEngineer", "displayName": "AI工程师", "description": "负责AI模型训练和调优，拥有模型管理权限", "permissionCount": 8, "isSystem": true, "parentRole": "Admin"}}, "permissions": {"user": {"module": "user", "displayName": "用户管理", "description": "用户管理相关权限", "permissions": [{"code": "user.profile.read", "name": "查看用户资料", "description": "查看用户基本信息", "module": "user", "resource": "profile", "action": "read", "applicableRoles": ["所有角色"]}, {"code": "user.profile.update", "name": "更新用户资料", "description": "修改用户基本信息", "module": "user", "resource": "profile", "action": "update", "applicableRoles": ["User", "Acco<PERSON><PERSON><PERSON><PERSON>", "Admin"]}, {"code": "user.list.read", "name": "查看用户列表", "description": "查看系统用户列表", "module": "user", "resource": "list", "action": "read", "applicableRoles": ["Acco<PERSON><PERSON><PERSON><PERSON>", "RiskOfficer", "Admin"]}, {"code": "user.detail.read", "name": "查看用户详情", "description": "查看用户详细信息", "module": "user", "resource": "detail", "action": "read", "applicableRoles": ["Acco<PERSON><PERSON><PERSON><PERSON>", "Admin"]}, {"code": "user.create", "name": "创建用户", "description": "创建新用户账户", "module": "user", "resource": "user", "action": "create", "applicableRoles": ["Acco<PERSON><PERSON><PERSON><PERSON>", "Admin"]}, {"code": "user.update", "name": "更新用户", "description": "修改用户信息", "module": "user", "resource": "user", "action": "update", "applicableRoles": ["Acco<PERSON><PERSON><PERSON><PERSON>", "Admin"]}, {"code": "user.delete", "name": "删除用户", "description": "删除用户账户", "module": "user", "resource": "user", "action": "delete", "applicableRoles": ["Admin"]}, {"code": "user.status.update", "name": "更新用户状态", "description": "修改用户状态（启用/禁用）", "module": "user", "resource": "status", "action": "update", "applicableRoles": ["Admin"]}, {"code": "user.role.assign", "name": "分配用户角色", "description": "为用户分配或撤销角色", "module": "user", "resource": "role", "action": "assign", "applicableRoles": ["Admin"]}, {"code": "user.role.revoke", "name": "撤销用户角色", "description": "撤销用户角色", "module": "user", "resource": "role", "action": "revoke", "applicableRoles": ["Admin"]}, {"code": "user.role.read", "name": "查看用户角色", "description": "查看用户的角色信息", "module": "user", "resource": "role", "action": "read", "applicableRoles": ["Admin"]}, {"code": "user.permission.read", "name": "查看用户权限", "description": "查看用户的权限信息", "module": "user", "resource": "permission", "action": "read", "applicableRoles": ["Admin"]}, {"code": "user.role.batch.assign", "name": "批量分配角色", "description": "批量为用户分配角色", "module": "user", "resource": "role", "action": "batch.assign", "applicableRoles": ["Admin"]}, {"code": "user.role.batch.revoke", "name": "批量撤销角色", "description": "批量撤销用户角色", "module": "user", "resource": "role", "action": "batch.revoke", "applicableRoles": ["Admin"]}, {"code": "user.preferences.read", "name": "读取用户偏好", "description": "查看用户偏好设置", "module": "user", "resource": "preferences", "action": "read", "applicableRoles": ["User", "Admin"]}, {"code": "user.preferences.update", "name": "更新用户偏好", "description": "修改用户偏好设置", "module": "user", "resource": "preferences", "action": "update", "applicableRoles": ["User", "Admin"]}]}, "permission": {"module": "permission", "displayName": "权限管理", "description": "权限和角色管理相关权限", "permissions": [{"code": "permission.create", "name": "创建权限", "description": "创建新的系统权限", "module": "permission", "resource": "permission", "action": "create", "applicableRoles": ["Admin"]}, {"code": "permission.read", "name": "查看权限", "description": "查看权限详情", "module": "permission", "resource": "permission", "action": "read", "applicableRoles": ["Admin"]}, {"code": "permission.update", "name": "更新权限", "description": "修改权限信息", "module": "permission", "resource": "permission", "action": "update", "applicableRoles": ["Admin"]}, {"code": "permission.delete", "name": "删除权限", "description": "删除权限", "module": "permission", "resource": "permission", "action": "delete", "applicableRoles": ["Admin"]}, {"code": "permission.list.read", "name": "查看权限列表", "description": "查看系统权限列表", "module": "permission", "resource": "list", "action": "read", "applicableRoles": ["Admin"]}, {"code": "permission.analytics.read", "name": "查看权限统计", "description": "查看权限使用统计", "module": "permission", "resource": "analytics", "action": "read", "applicableRoles": ["Admin"]}, {"code": "role.create", "name": "创建角色", "description": "创建新角色", "module": "permission", "resource": "role", "action": "create", "applicableRoles": ["Admin"]}, {"code": "role.read", "name": "查看角色", "description": "查看角色详情", "module": "permission", "resource": "role", "action": "read", "applicableRoles": ["Admin"]}, {"code": "role.update", "name": "更新角色", "description": "修改角色信息", "module": "permission", "resource": "role", "action": "update", "applicableRoles": ["Admin"]}, {"code": "role.delete", "name": "删除角色", "description": "删除角色", "module": "permission", "resource": "role", "action": "delete", "applicableRoles": ["Admin"]}, {"code": "role.list.read", "name": "查看角色列表", "description": "查看系统角色列表", "module": "permission", "resource": "role", "action": "list.read", "applicableRoles": ["Admin"]}, {"code": "role.permission.assign", "name": "分配角色权限", "description": "为角色分配权限", "module": "permission", "resource": "role", "action": "permission.assign", "applicableRoles": ["Admin"]}, {"code": "role.permission.revoke", "name": "撤销角色权限", "description": "从角色撤销权限", "module": "permission", "resource": "role", "action": "permission.revoke", "applicableRoles": ["Admin"]}, {"code": "role.permission.read", "name": "查看角色权限", "description": "查看角色的权限列表", "module": "permission", "resource": "role", "action": "permission.read", "applicableRoles": ["Admin"]}, {"code": "role.analytics.read", "name": "查看角色统计", "description": "查看角色使用统计", "module": "permission", "resource": "role", "action": "analytics.read", "applicableRoles": ["Admin"]}]}, "data": {"module": "data", "displayName": "数据管理", "description": "数据采集和分析相关权限", "permissions": [{"code": "data.source.read", "name": "查看数据源", "description": "查看数据源配置", "module": "data", "resource": "source", "action": "read", "applicableRoles": ["Analyst", "DataAdmin", "Admin"]}, {"code": "data.source.create", "name": "创建数据源", "description": "创建新的数据源", "module": "data", "resource": "source", "action": "create", "applicableRoles": ["DataAdmin", "Admin"]}, {"code": "data.source.update", "name": "更新数据源", "description": "修改数据源配置", "module": "data", "resource": "source", "action": "update", "applicableRoles": ["DataAdmin", "Admin"]}, {"code": "data.source.delete", "name": "删除数据源", "description": "删除数据源", "module": "data", "resource": "source", "action": "delete", "applicableRoles": ["Admin"]}, {"code": "data.task.read", "name": "查看采集任务", "description": "查看数据采集任务", "module": "data", "resource": "task", "action": "read", "applicableRoles": ["Analyst", "DataAdmin", "Admin"]}, {"code": "data.task.create", "name": "创建采集任务", "description": "创建新的采集任务", "module": "data", "resource": "task", "action": "create", "applicableRoles": ["DataAdmin", "Admin"]}, {"code": "data.task.update", "name": "更新采集任务", "description": "修改采集任务配置", "module": "data", "resource": "task", "action": "update", "applicableRoles": ["DataAdmin", "Admin"]}, {"code": "data.task.execute", "name": "执行采集任务", "description": "执行数据采集任务", "module": "data", "resource": "task", "action": "execute", "applicableRoles": ["DataAdmin", "Admin"]}, {"code": "data.task.delete", "name": "删除采集任务", "description": "删除采集任务", "module": "data", "resource": "task", "action": "delete", "applicableRoles": ["Admin"]}, {"code": "data.query.execute", "name": "执行数据查询", "description": "执行数据查询操作", "module": "data", "resource": "query", "action": "execute", "applicableRoles": ["Analyst", "DataAdmin", "Admin"]}, {"code": "data.report.read", "name": "查看分析报告", "description": "查看数据分析报告", "module": "data", "resource": "report", "action": "read", "applicableRoles": ["User", "Analyst", "DataAdmin", "Admin"]}, {"code": "data.report.create", "name": "创建分析报告", "description": "创建新的分析报告", "module": "data", "resource": "report", "action": "create", "applicableRoles": ["Analyst", "DataAdmin", "Admin"]}, {"code": "data.report.update", "name": "更新分析报告", "description": "修改分析报告", "module": "data", "resource": "report", "action": "update", "applicableRoles": ["Analyst", "DataAdmin", "Admin"]}, {"code": "data.report.publish", "name": "发布分析报告", "description": "发布分析报告", "module": "data", "resource": "report", "action": "publish", "applicableRoles": ["Analyst", "DataAdmin", "Admin"]}, {"code": "data.report.delete", "name": "删除分析报告", "description": "删除分析报告", "module": "data", "resource": "report", "action": "delete", "applicableRoles": ["Admin"]}, {"code": "data.export", "name": "导出数据", "description": "导出数据到文件", "module": "data", "resource": "data", "action": "export", "applicableRoles": ["Analyst", "DataAdmin", "Admin"]}]}, "data_source": {"module": "data_source", "displayName": "数据源管理", "description": "数据源相关权限", "permissions": [{"code": "data_source.data_source.create", "name": "创建数据源", "description": "创建新的数据源", "module": "data_source", "resource": "data_source", "action": "create", "applicableRoles": ["DataAdmin", "Admin"]}, {"code": "data_source.data_source.read", "name": "查看数据源", "description": "查看数据源详情", "module": "data_source", "resource": "data_source", "action": "read", "applicableRoles": ["Analyst", "DataAdmin", "Admin"]}, {"code": "data_source.data_source.update", "name": "更新数据源", "description": "修改数据源配置", "module": "data_source", "resource": "data_source", "action": "update", "applicableRoles": ["DataAdmin", "Admin"]}, {"code": "data_source.data_source.delete", "name": "删除数据源", "description": "删除数据源", "module": "data_source", "resource": "data_source", "action": "delete", "applicableRoles": ["Admin"]}, {"code": "data_source.list.read", "name": "查看数据源列表", "description": "查看数据源列表", "module": "data_source", "resource": "list", "action": "read", "applicableRoles": ["Analyst", "DataAdmin", "Admin"]}, {"code": "data_source.stats.read", "name": "查看数据源统计", "description": "查看数据源统计信息", "module": "data_source", "resource": "stats", "action": "read", "applicableRoles": ["Analyst", "DataAdmin", "Admin"]}, {"code": "data_source.config.create", "name": "创建数据源配置", "description": "创建新的数据源配置", "module": "data_source", "resource": "config", "action": "create", "applicableRoles": ["DataAdmin", "Admin"]}, {"code": "data_source.config.read", "name": "查看数据源配置", "description": "查看数据源配置详情", "module": "data_source", "resource": "config", "action": "read", "applicableRoles": ["Analyst", "DataAdmin", "Admin"]}, {"code": "data_source.config.update", "name": "更新数据源配置", "description": "修改数据源配置", "module": "data_source", "resource": "config", "action": "update", "applicableRoles": ["DataAdmin", "Admin"]}, {"code": "data_source.config.delete", "name": "删除数据源配置", "description": "删除数据源配置", "module": "data_source", "resource": "config", "action": "delete", "applicableRoles": ["Admin"]}, {"code": "data_source.config.manage", "name": "管理数据源配置", "description": "激活/停用数据源配置", "module": "data_source", "resource": "config", "action": "manage", "applicableRoles": ["DataAdmin", "Admin"]}]}, "raw_data_record": {"module": "raw_data_record", "displayName": "原始数据记录", "description": "原始数据记录相关权限", "permissions": [{"code": "raw_data_record.raw_data_record.create", "name": "创建原始数据记录", "description": "创建新的原始数据记录", "module": "raw_data_record", "resource": "raw_data_record", "action": "create", "applicableRoles": ["DataAdmin", "Admin"]}, {"code": "raw_data_record.raw_data_record.read", "name": "查看原始数据记录", "description": "查看原始数据记录详情", "module": "raw_data_record", "resource": "raw_data_record", "action": "read", "applicableRoles": ["Analyst", "DataAdmin", "Admin"]}, {"code": "raw_data_record.raw_data_record.update", "name": "更新原始数据记录", "description": "修改原始数据记录", "module": "raw_data_record", "resource": "raw_data_record", "action": "update", "applicableRoles": ["DataAdmin", "Admin"]}, {"code": "raw_data_record.raw_data_record.delete", "name": "删除原始数据记录", "description": "删除原始数据记录", "module": "raw_data_record", "resource": "raw_data_record", "action": "delete", "applicableRoles": ["Admin"]}, {"code": "raw_data_record.raw_data_record.manage", "name": "管理原始数据记录", "description": "归档原始数据记录", "module": "raw_data_record", "resource": "raw_data_record", "action": "manage", "applicableRoles": ["DataAdmin", "Admin"]}, {"code": "raw_data_record.raw_data_record.analyze", "name": "分析原始数据记录", "description": "获取重复记录分析", "module": "raw_data_record", "resource": "raw_data_record", "action": "analyze", "applicableRoles": ["Analyst", "DataAdmin", "Admin"]}, {"code": "raw_data_record.list.read", "name": "查看原始数据记录列表", "description": "查看原始数据记录列表", "module": "raw_data_record", "resource": "list", "action": "read", "applicableRoles": ["Analyst", "DataAdmin", "Admin"]}, {"code": "raw_data_record.stats.read", "name": "查看原始数据记录统计", "description": "查看原始数据记录统计信息", "module": "raw_data_record", "resource": "stats", "action": "read", "applicableRoles": ["Analyst", "DataAdmin", "Admin"]}]}, "content": {"module": "content", "displayName": "内容管理", "description": "内容管理相关权限", "permissions": [{"code": "content.article.read", "name": "查看文章", "description": "查看文章内容", "module": "content", "resource": "article", "action": "read", "applicableRoles": ["所有角色"]}, {"code": "content.article.create", "name": "创建文章", "description": "创建新文章", "module": "content", "resource": "article", "action": "create", "applicableRoles": ["Editor", "Admin"]}, {"code": "content.article.update", "name": "更新文章", "description": "修改文章内容", "module": "content", "resource": "article", "action": "update", "applicableRoles": ["Editor", "Admin"]}, {"code": "content.article.publish", "name": "发布文章", "description": "发布文章到公开平台", "module": "content", "resource": "article", "action": "publish", "applicableRoles": ["Editor", "Admin"]}, {"code": "content.article.delete", "name": "删除文章", "description": "删除文章", "module": "content", "resource": "article", "action": "delete", "applicableRoles": ["Editor", "Admin"]}, {"code": "content.comment.moderate", "name": "管理评论", "description": "审核和管理文章评论", "module": "content", "resource": "comment", "action": "moderate", "applicableRoles": ["Editor", "Admin"]}]}, "financial": {"module": "financial", "displayName": "财经日历", "description": "财经日历相关权限", "permissions": [{"code": "financial.calendar.read", "name": "查看财经日历", "description": "查看财经日历事件", "module": "financial", "resource": "calendar", "action": "read", "applicableRoles": ["所有角色"]}, {"code": "financial.calendar.create", "name": "创建财经事件", "description": "创建新的财经事件", "module": "financial", "resource": "calendar", "action": "create", "applicableRoles": ["Editor", "Admin"]}, {"code": "financial.calendar.update", "name": "更新财经事件", "description": "修改财经事件信息", "module": "financial", "resource": "calendar", "action": "update", "applicableRoles": ["Editor", "Admin"]}, {"code": "financial.calendar.delete", "name": "删除财经事件", "description": "删除财经事件", "module": "financial", "resource": "calendar", "action": "delete", "applicableRoles": ["Editor", "Admin"]}]}, "tag": {"module": "tag", "displayName": "标签分类", "description": "标签分类相关权限", "permissions": [{"code": "tag.read", "name": "查看标签", "description": "查看标签信息", "module": "tag", "resource": "tag", "action": "read", "applicableRoles": ["User", "Editor", "Admin"]}, {"code": "tag.create", "name": "创建标签", "description": "创建新标签", "module": "tag", "resource": "tag", "action": "create", "applicableRoles": ["Editor", "Admin"]}, {"code": "tag.update", "name": "更新标签", "description": "修改标签信息", "module": "tag", "resource": "tag", "action": "update", "applicableRoles": ["Editor", "Admin"]}, {"code": "tag.delete", "name": "删除标签", "description": "删除标签", "module": "tag", "resource": "tag", "action": "delete", "applicableRoles": ["Editor", "Admin"]}, {"code": "tag.type.create", "name": "创建标签类型", "description": "创建新的标签类型", "module": "tag", "resource": "type", "action": "create", "applicableRoles": ["Editor", "Admin"]}, {"code": "tag.type.update", "name": "更新标签类型", "description": "修改标签类型", "module": "tag", "resource": "type", "action": "update", "applicableRoles": ["Editor", "Admin"]}, {"code": "tag.type.delete", "name": "删除标签类型", "description": "删除标签类型", "module": "tag", "resource": "type", "action": "delete", "applicableRoles": ["Editor", "Admin"]}, {"code": "tag.category.create", "name": "创建标签分类", "description": "创建标签分类", "module": "tag", "resource": "category", "action": "create", "applicableRoles": ["Editor", "Admin"]}, {"code": "tag.category.update", "name": "更新标签分类", "description": "修改标签分类", "module": "tag", "resource": "category", "action": "update", "applicableRoles": ["Editor", "Admin"]}, {"code": "tag.category.delete", "name": "删除标签分类", "description": "删除标签分类", "module": "tag", "resource": "category", "action": "delete", "applicableRoles": ["Editor", "Admin"]}, {"code": "tag.analytics.read", "name": "查看标签统计", "description": "查看标签使用统计", "module": "tag", "resource": "analytics", "action": "read", "applicableRoles": ["Editor", "Admin"]}]}, "ai_model": {"module": "ai_model", "displayName": "AI模型管理", "description": "AI模型管理相关权限", "permissions": [{"code": "ai_model.model.create", "name": "创建AI模型", "description": "创建新的AI模型配置", "module": "ai_model", "resource": "model", "action": "create", "applicableRoles": ["DataAdmin", "AIEngineer", "Admin"]}, {"code": "ai_model.model.read", "name": "查看AI模型", "description": "查看AI模型详细信息", "module": "ai_model", "resource": "model", "action": "read", "applicableRoles": ["Analyst", "DataAdmin", "AIEngineer", "Admin"]}, {"code": "ai_model.model.update", "name": "更新AI模型", "description": "修改AI模型配置和使用统计", "module": "ai_model", "resource": "model", "action": "update", "applicableRoles": ["DataAdmin", "AIEngineer", "Admin"]}, {"code": "ai_model.model.delete", "name": "删除AI模型", "description": "删除指定的AI模型", "module": "ai_model", "resource": "model", "action": "delete", "applicableRoles": ["DataAdmin", "Admin"]}, {"code": "ai_model.list.read", "name": "查看模型列表", "description": "查看AI模型列表和可用模型", "module": "ai_model", "resource": "list", "action": "read", "applicableRoles": ["User", "Analyst", "DataAdmin", "AIEngineer", "Admin"]}, {"code": "ai_model.status.update", "name": "更新模型状态", "description": "修改模型状态（激活/停用/测试/废弃）", "module": "ai_model", "resource": "status", "action": "update", "applicableRoles": ["DataAdmin", "AIEngineer", "Admin"]}, {"code": "ai_model.default.update", "name": "设置默认模型", "description": "设置或取消模型为默认模型", "module": "ai_model", "resource": "default", "action": "update", "applicableRoles": ["DataAdmin", "Admin"]}, {"code": "ai_model.metrics.create", "name": "创建模型指标", "description": "为指定模型创建指标记录", "module": "ai_model", "resource": "metrics", "action": "create", "applicableRoles": ["DataAdmin", "AIEngineer", "Admin"]}, {"code": "ai_model.metrics.read", "name": "查看模型指标", "description": "获取指定模型的指标数据", "module": "ai_model", "resource": "metrics", "action": "read", "applicableRoles": ["Analyst", "DataAdmin", "AIEngineer", "Admin"]}, {"code": "ai_model.analytics.read", "name": "查看分析数据", "description": "获取系统信息、模型性能和对比分析", "module": "ai_model", "resource": "analytics", "action": "read", "applicableRoles": ["User", "Analyst", "DataAdmin", "AIEngineer", "Admin"]}]}, "system": {"module": "system", "displayName": "系统管理", "description": "系统管理相关权限", "permissions": [{"code": "system.config.read", "name": "查看系统配置", "description": "查看系统配置参数", "module": "system", "resource": "config", "action": "read", "applicableRoles": ["Admin"]}, {"code": "system.config.update", "name": "更新系统配置", "description": "修改系统配置参数", "module": "system", "resource": "config", "action": "update", "applicableRoles": ["Admin"]}, {"code": "system.log.read", "name": "查看系统日志", "description": "查看系统操作日志", "module": "system", "resource": "log", "action": "read", "applicableRoles": ["RiskOfficer", "Admin"]}, {"code": "system.backup", "name": "系统备份", "description": "执行系统备份操作", "module": "system", "resource": "system", "action": "backup", "applicableRoles": ["Admin"]}, {"code": "system.permission.manage", "name": "管理权限设置", "description": "管理系统权限配置", "module": "system", "resource": "permission", "action": "manage", "applicableRoles": ["Admin"]}]}}, "apiRoutes": {"frontend": [{"path": "/api/v1/permissions/me", "method": "GET", "description": "获取当前用户权限", "permission": "登录用户", "responseFormat": {"user_id": "number", "roles": "Role[]", "permissions": "Permission[]", "permission_codes": "string[]"}}, {"path": "/api/v1/permissions/check", "method": "POST", "description": "检查当前用户权限", "permission": "登录用户", "requestFormat": {"permission_code": "string"}, "responseFormat": {"has_permission": "boolean"}}], "admin": [{"path": "/api/v1/admin/permissions/permissions", "method": "GET", "description": "获取权限列表", "permission": "permission.list.read"}, {"path": "/api/v1/admin/permissions/permissions", "method": "POST", "description": "创建权限", "permission": "permission.create"}, {"path": "/api/v1/admin/permissions/roles", "method": "GET", "description": "获取角色列表", "permission": "role.list.read"}, {"path": "/api/v1/admin/permissions/roles", "method": "POST", "description": "创建角色", "permission": "role.create"}, {"path": "/api/v1/admin/permissions/users/{id}/roles", "method": "GET", "description": "获取用户角色", "permission": "user.role.read"}, {"path": "/api/v1/admin/permissions/users/{id}/roles", "method": "POST", "description": "分配用户角色", "permission": "user.role.assign"}]}, "constants": {"ROLES": {"ADMIN": "Admin", "DATA_ADMIN": "DataAdmin", "ANALYST": "Analyst", "EDITOR": "Editor", "ACCOUNT_MANAGER": "Acco<PERSON><PERSON><PERSON><PERSON>", "RISK_OFFICER": "RiskOfficer", "USER": "User", "GUEST": "Guest", "AI_ENGINEER": "AIEngineer"}, "PERMISSION_MODULES": {"USER": "user", "PERMISSION": "permission", "DATA": "data", "CONTENT": "content", "FINANCIAL": "financial", "TAG": "tag", "AI_MODEL": "ai_model", "SYSTEM": "system"}}}