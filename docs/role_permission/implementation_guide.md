# 角色权限系统实现指南

## 1. 概述

本文档提供了在FinSight系统中实现角色权限体系的详细指南，包括数据模型、服务层、API层和前端集成的具体实现。

## 2. 数据模型实现

### 2.1 创建权限服务目录结构

```bash
src/services/permission_service/
├── __init__.py
├── models.py          # 数据模型
├── schemas.py         # 数据传输对象
├── service.py         # 业务逻辑服务
├── router.py          # API路由
└── dependencies.py    # 依赖注入
```

### 2.2 数据模型定义 (models.py)

```python
"""
权限服务数据模型
定义角色、权限相关的数据库模型
"""

from datetime import datetime
from sqlalchemy import (
    Boolean, Column, DateTime, ForeignKey, Index, Integer, String, Text,
    UniqueConstraint
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from ...core.database import Base


class Role(Base):
    """角色表"""
    
    __tablename__ = "roles"
    
    id = Column(Integer, primary_key=True, index=True, comment="角色ID")
    name = Column(String(50), unique=True, nullable=False, comment="角色名称")
    description = Column(Text, comment="角色描述")
    is_system = Column(Boolean, default=False, comment="是否系统内置角色")
    parent_id = Column(Integer, ForeignKey("roles.id"), nullable=True, comment="父角色ID")
    created_at = Column(DateTime, default=func.current_timestamp(), comment="创建时间")
    updated_at = Column(
        DateTime,
        default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间"
    )
    
    # 关系定义
    parent = relationship("Role", remote_side=[id], backref="children")
    permissions = relationship("Permission", secondary="role_permissions", back_populates="roles")
    users = relationship("User", secondary="user_roles", back_populates="roles")
    
    # 索引定义
    __table_args__ = (
        Index("idx_role_parent_id", "parent_id"),
        Index("idx_role_name", "name"),
    )


class Permission(Base):
    """权限表"""
    
    __tablename__ = "permissions"
    
    id = Column(Integer, primary_key=True, index=True, comment="权限ID")
    code = Column(String(100), unique=True, nullable=False, comment="权限编码")
    name = Column(String(100), nullable=False, comment="权限名称")
    description = Column(Text, comment="权限描述")
    module = Column(String(50), nullable=False, comment="所属模块")
    resource = Column(String(50), nullable=False, comment="资源类型")
    action = Column(String(50), nullable=False, comment="操作类型")
    created_at = Column(DateTime, default=func.current_timestamp(), comment="创建时间")
    updated_at = Column(
        DateTime,
        default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间"
    )
    
    # 关系定义
    roles = relationship("Role", secondary="role_permissions", back_populates="permissions")
    
    # 索引定义
    __table_args__ = (
        Index("idx_permission_module", "module"),
        Index("idx_permission_resource", "resource"),
        Index("idx_permission_action", "action"),
        Index("idx_permission_code", "code"),
    )


class RolePermission(Base):
    """角色权限关联表"""
    
    __tablename__ = "role_permissions"
    
    id = Column(Integer, primary_key=True, index=True, comment="关联ID")
    role_id = Column(Integer, ForeignKey("roles.id", ondelete="CASCADE"), nullable=False, comment="角色ID")
    permission_id = Column(Integer, ForeignKey("permissions.id", ondelete="CASCADE"), nullable=False, comment="权限ID")
    created_at = Column(DateTime, default=func.current_timestamp(), comment="创建时间")
    
    # 唯一约束
    __table_args__ = (
        UniqueConstraint("role_id", "permission_id", name="uk_role_permission"),
        Index("idx_rp_role_id", "role_id"),
        Index("idx_rp_permission_id", "permission_id"),
    )


class UserRole(Base):
    """用户角色关联表"""
    
    __tablename__ = "user_roles"
    
    id = Column(Integer, primary_key=True, index=True, comment="关联ID")
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, comment="用户ID")
    role_id = Column(Integer, ForeignKey("roles.id", ondelete="CASCADE"), nullable=False, comment="角色ID")
    created_at = Column(DateTime, default=func.current_timestamp(), comment="创建时间")
    created_by = Column(Integer, comment="创建人ID")
    
    # 唯一约束
    __table_args__ = (
        UniqueConstraint("user_id", "role_id", name="uk_user_role"),
        Index("idx_ur_user_id", "user_id"),
        Index("idx_ur_role_id", "role_id"),
    )


class PermissionOperationLog(Base):
    """权限操作日志表"""
    
    __tablename__ = "permission_operation_logs"
    
    id = Column(Integer, primary_key=True, index=True, comment="日志ID")
    operator_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="操作人ID")
    operation_type = Column(String(20), nullable=False, comment="操作类型：GRANT/REVOKE")
    target_type = Column(String(20), nullable=False, comment="目标类型：USER/ROLE")
    target_id = Column(Integer, nullable=False, comment="目标ID")
    role_id = Column(Integer, comment="角色ID")
    permission_id = Column(Integer, comment="权限ID")
    operation_time = Column(DateTime, default=func.current_timestamp(), comment="操作时间")
    ip_address = Column(String(45), comment="操作IP")
    
    # 索引定义
    __table_args__ = (
        Index("idx_pol_operator_id", "operator_id"),
        Index("idx_pol_target_id", "target_id"),
        Index("idx_pol_operation_time", "operation_time"),
    )
```

### 2.3 更新用户模型

在现有的用户模型中添加角色关系：

```python
# 在 src/services/user_service/models.py 中添加
from sqlalchemy.orm import relationship

class User(Base):
    # ... 现有字段 ...
    
    # 添加角色关系
    roles = relationship("Role", secondary="user_roles", back_populates="users")
    
    def has_role(self, role_name: str) -> bool:
        """检查用户是否拥有指定角色"""
        return any(role.name == role_name for role in self.roles)
    
    def has_permission(self, permission_code: str) -> bool:
        """检查用户是否拥有指定权限"""
        for role in self.roles:
            for permission in role.permissions:
                if permission.code == permission_code:
                    return True
        return False
    
    @property
    def is_admin(self) -> bool:
        """检查用户是否是管理员"""
        return self.has_role("Admin")
```

## 3. 数据传输对象 (schemas.py)

```python
"""
权限服务数据传输对象
定义API请求和响应的数据格式
"""

from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field


# ==================== 权限相关 ====================

class PermissionBase(BaseModel):
    """权限基础模型"""
    code: str = Field(..., description="权限编码")
    name: str = Field(..., description="权限名称")
    description: Optional[str] = Field(None, description="权限描述")
    module: str = Field(..., description="所属模块")
    resource: str = Field(..., description="资源类型")
    action: str = Field(..., description="操作类型")


class PermissionCreate(PermissionBase):
    """权限创建请求"""
    pass


class PermissionUpdate(BaseModel):
    """权限更新请求"""
    name: Optional[str] = None
    description: Optional[str] = None
    module: Optional[str] = None
    resource: Optional[str] = None
    action: Optional[str] = None


class PermissionResponse(PermissionBase):
    """权限响应"""
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# ==================== 角色相关 ====================

class RoleBase(BaseModel):
    """角色基础模型"""
    name: str = Field(..., description="角色名称")
    description: Optional[str] = Field(None, description="角色描述")
    parent_id: Optional[int] = Field(None, description="父角色ID")


class RoleCreate(RoleBase):
    """角色创建请求"""
    is_system: bool = Field(default=False, description="是否系统内置角色")


class RoleUpdate(BaseModel):
    """角色更新请求"""
    name: Optional[str] = None
    description: Optional[str] = None
    parent_id: Optional[int] = None


class RoleResponse(RoleBase):
    """角色响应"""
    id: int
    is_system: bool
    created_at: datetime
    updated_at: datetime
    permissions: List[PermissionResponse] = []
    
    class Config:
        from_attributes = True


# ==================== 用户角色相关 ====================

class UserRoleAssign(BaseModel):
    """用户角色分配请求"""
    user_id: int = Field(..., description="用户ID")
    role_id: int = Field(..., description="角色ID")


class UserRoleResponse(BaseModel):
    """用户角色响应"""
    id: int
    user_id: int
    role_id: int
    created_at: datetime
    created_by: Optional[int]
    
    class Config:
        from_attributes = True


# ==================== 角色权限相关 ====================

class RolePermissionAssign(BaseModel):
    """角色权限分配请求"""
    role_id: int = Field(..., description="角色ID")
    permission_id: int = Field(..., description="权限ID")


class RolePermissionResponse(BaseModel):
    """角色权限响应"""
    id: int
    role_id: int
    permission_id: int
    created_at: datetime
    
    class Config:
        from_attributes = True


# ==================== 权限检查相关 ====================

class PermissionCheckRequest(BaseModel):
    """权限检查请求"""
    user_id: int = Field(..., description="用户ID")
    permission_code: str = Field(..., description="权限编码")


class PermissionCheckResponse(BaseModel):
    """权限检查响应"""
    has_permission: bool = Field(..., description="是否拥有权限")
    user_id: int = Field(..., description="用户ID")
    permission_code: str = Field(..., description="权限编码")


class UserPermissionsResponse(BaseModel):
    """用户权限列表响应"""
    user_id: int
    permissions: List[str] = Field(..., description="权限编码列表")
    roles: List[str] = Field(..., description="角色名称列表")


# ==================== 操作日志相关 ====================

class PermissionOperationLogResponse(BaseModel):
    """权限操作日志响应"""
    id: int
    operator_id: int
    operation_type: str
    target_type: str
    target_id: int
    role_id: Optional[int]
    permission_id: Optional[int]
    operation_time: datetime
    ip_address: Optional[str]
    
    class Config:
        from_attributes = True
```

## 4. 服务层实现

### 4.1 权限服务 (service.py 第一部分)

```python
"""
权限服务业务逻辑
包含角色、权限管理的核心服务
"""

import logging
from typing import Dict, List, Optional, Set
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from ...core.cache import CacheService
from .models import Permission, Role, RolePermission, UserRole, PermissionOperationLog
from .schemas import (
    PermissionCreate, PermissionUpdate, RoleCreate, RoleUpdate,
    UserRoleAssign, RolePermissionAssign
)

logger = logging.getLogger(__name__)


class PermissionService:
    """权限管理服务"""
    
    def __init__(self, db_session: Session, cache_service: Optional[CacheService] = None):
        self.db = db_session
        self.cache = cache_service
    
    def create_permission(self, permission_data: PermissionCreate) -> Permission:
        """创建新权限"""
        # 检查权限编码是否已存在
        existing = self.db.query(Permission).filter(
            Permission.code == permission_data.code
        ).first()
        
        if existing:
            raise ValueError(f"Permission with code '{permission_data.code}' already exists")
        
        permission = Permission(**permission_data.model_dump())
        self.db.add(permission)
        self.db.commit()
        self.db.refresh(permission)
        
        logger.info(f"Created permission: {permission.code}")
        return permission
    
    def get_permission(self, permission_id: int) -> Optional[Permission]:
        """获取权限详情"""
        return self.db.query(Permission).filter(Permission.id == permission_id).first()
    
    def get_permission_by_code(self, code: str) -> Optional[Permission]:
        """根据编码获取权限"""
        return self.db.query(Permission).filter(Permission.code == code).first()
    
    def get_permissions_by_codes(self, codes: List[str]) -> List[Permission]:
        """根据权限编码列表获取权限"""
        return self.db.query(Permission).filter(Permission.code.in_(codes)).all()
    
    def update_permission(self, permission_id: int, permission_data: PermissionUpdate) -> Optional[Permission]:
        """更新权限信息"""
        permission = self.get_permission(permission_id)
        if not permission:
            return None
        
        update_data = permission_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(permission, field, value)
        
        self.db.commit()
        self.db.refresh(permission)
        
        logger.info(f"Updated permission: {permission.code}")
        return permission
    
    def delete_permission(self, permission_id: int) -> bool:
        """删除权限"""
        permission = self.get_permission(permission_id)
        if not permission:
            return False
        
        # 删除相关的角色权限关联
        self.db.query(RolePermission).filter(
            RolePermission.permission_id == permission_id
        ).delete()
        
        self.db.delete(permission)
        self.db.commit()
        
        logger.info(f"Deleted permission: {permission.code}")
        return True
    
    def get_all_permissions(self, module: Optional[str] = None) -> List[Permission]:
        """获取所有权限，可按模块过滤"""
        query = self.db.query(Permission)
        
        if module:
            query = query.filter(Permission.module == module)
        
        return query.all()
```

### 4.2 角色服务和权限验证服务

```python
class RoleService:
    """角色管理服务"""
    
    def __init__(self, db_session: Session, cache_service: Optional[CacheService] = None):
        self.db = db_session
        self.cache = cache_service
    
    def create_role(self, role_data: RoleCreate) -> Role:
        """创建新角色"""
        # 检查角色名称是否已存在
        existing = self.db.query(Role).filter(Role.name == role_data.name).first()
        if existing:
            raise ValueError(f"Role with name '{role_data.name}' already exists")
        
        # 如果指定了父角色，检查父角色是否存在
        if role_data.parent_id:
            parent_role = self.db.query(Role).filter(Role.id == role_data.parent_id).first()
            if not parent_role:
                raise ValueError(f"Parent role with id {role_data.parent_id} not found")
        
        role = Role(**role_data.model_dump())
        self.db.add(role)
        self.db.commit()
        self.db.refresh(role)
        
        logger.info(f"Created role: {role.name}")
        return role
    
    def get_role(self, role_id: int) -> Optional[Role]:
        """获取角色详情"""
        return self.db.query(Role).filter(Role.id == role_id).first()
    
    def get_role_by_name(self, name: str) -> Optional[Role]:
        """根据名称获取角色"""
        return self.db.query(Role).filter(Role.name == name).first()
    
    def update_role(self, role_id: int, role_data: RoleUpdate) -> Optional[Role]:
        """更新角色信息"""
        role = self.get_role(role_id)
        if not role:
            return None
        
        # 防止修改系统内置角色
        if role.is_system:
            raise ValueError("Cannot modify system role")
        
        update_data = role_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(role, field, value)
        
        self.db.commit()
        self.db.refresh(role)
        
        logger.info(f"Updated role: {role.name}")
        return role
    
    def delete_role(self, role_id: int) -> bool:
        """删除角色"""
        role = self.get_role(role_id)
        if not role:
            return False
        
        # 防止删除系统内置角色
        if role.is_system:
            raise ValueError("Cannot delete system role")
        
        # 检查是否有用户使用此角色
        user_count = self.db.query(UserRole).filter(UserRole.role_id == role_id).count()
        if user_count > 0:
            raise ValueError(f"Cannot delete role '{role.name}': {user_count} users are assigned to this role")
        
        # 删除角色权限关联
        self.db.query(RolePermission).filter(RolePermission.role_id == role_id).delete()
        
        self.db.delete(role)
        self.db.commit()
        
        logger.info(f"Deleted role: {role.name}")
        return True
    
    def get_all_roles(self) -> List[Role]:
        """获取所有角色"""
        return self.db.query(Role).all()
    
    def assign_permission_to_role(self, role_id: int, permission_id: int) -> bool:
        """为角色分配权限"""
        # 检查角色和权限是否存在
        role = self.get_role(role_id)
        if not role:
            raise ValueError(f"Role with id {role_id} not found")
        
        permission = self.db.query(Permission).filter(Permission.id == permission_id).first()
        if not permission:
            raise ValueError(f"Permission with id {permission_id} not found")
        
        # 检查是否已经分配了该权限
        existing = self.db.query(RolePermission).filter(
            and_(RolePermission.role_id == role_id, RolePermission.permission_id == permission_id)
        ).first()
        
        if existing:
            return False  # 已经分配了该权限
        
        role_permission = RolePermission(role_id=role_id, permission_id=permission_id)
        self.db.add(role_permission)
        self.db.commit()
        
        # 清除相关缓存
        if self.cache:
            self.cache.invalidate_role_permissions(role_id)
        
        logger.info(f"Assigned permission '{permission.code}' to role '{role.name}'")
        return True
    
    def revoke_permission_from_role(self, role_id: int, permission_id: int) -> bool:
        """从角色撤销权限"""
        role_permission = self.db.query(RolePermission).filter(
            and_(RolePermission.role_id == role_id, RolePermission.permission_id == permission_id)
        ).first()
        
        if not role_permission:
            return False
        
        self.db.delete(role_permission)
        self.db.commit()
        
        # 清除相关缓存
        if self.cache:
            self.cache.invalidate_role_permissions(role_id)
        
        logger.info(f"Revoked permission {permission_id} from role {role_id}")
        return True
    
    def get_role_permissions(self, role_id: int) -> List[Permission]:
        """获取角色的所有权限"""
        return self.db.query(Permission).join(RolePermission).filter(
            RolePermission.role_id == role_id
        ).all()
    
    def get_role_hierarchy_permissions(self, role_id: int) -> Set[str]:
        """获取角色及其父角色的所有权限编码"""
        permissions = set()
        
        def collect_permissions(current_role_id: int):
            # 获取当前角色的权限
            role_permissions = self.get_role_permissions(current_role_id)
            for perm in role_permissions:
                permissions.add(perm.code)
            
            # 递归获取父角色的权限
            role = self.get_role(current_role_id)
            if role and role.parent_id:
                collect_permissions(role.parent_id)
        
        collect_permissions(role_id)
        return permissions


class UserRoleService:
    """用户角色管理服务"""
    
    def __init__(self, db_session: Session, cache_service: Optional[CacheService] = None):
        self.db = db_session
        self.cache = cache_service
    
    def assign_role_to_user(self, user_id: int, role_id: int, operator_id: int) -> bool:
        """为用户分配角色"""
        # 检查用户和角色是否存在
        from ..user_service.models import User
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            raise ValueError(f"User with id {user_id} not found")
        
        role = self.db.query(Role).filter(Role.id == role_id).first()
        if not role:
            raise ValueError(f"Role with id {role_id} not found")
        
        # 检查是否已经分配了该角色
        existing = self.db.query(UserRole).filter(
            and_(UserRole.user_id == user_id, UserRole.role_id == role_id)
        ).first()
        
        if existing:
            return False  # 已经分配了该角色
        
        user_role = UserRole(user_id=user_id, role_id=role_id, created_by=operator_id)
        self.db.add(user_role)
        self.db.commit()
        
        # 记录操作日志
        self._log_permission_operation(
            operator_id, "GRANT", "USER", user_id, role_id=role_id
        )
        
        # 清除用户权限缓存
        if self.cache:
            self.cache.invalidate_user_permissions(user_id)
        
        logger.info(f"Assigned role '{role.name}' to user {user_id}")
        return True
    
    def revoke_role_from_user(self, user_id: int, role_id: int, operator_id: int) -> bool:
        """从用户撤销角色"""
        user_role = self.db.query(UserRole).filter(
            and_(UserRole.user_id == user_id, UserRole.role_id == role_id)
        ).first()
        
        if not user_role:
            return False
        
        self.db.delete(user_role)
        self.db.commit()
        
        # 记录操作日志
        self._log_permission_operation(
            operator_id, "REVOKE", "USER", user_id, role_id=role_id
        )
        
        # 清除用户权限缓存
        if self.cache:
            self.cache.invalidate_user_permissions(user_id)
        
        logger.info(f"Revoked role {role_id} from user {user_id}")
        return True
    
    def get_user_roles(self, user_id: int) -> List[Role]:
        """获取用户的所有角色"""
        return self.db.query(Role).join(UserRole).filter(
            UserRole.user_id == user_id
        ).all()
    
    def get_users_by_role(self, role_id: int) -> List:
        """获取拥有指定角色的所有用户"""
        from ..user_service.models import User
        return self.db.query(User).join(UserRole).filter(
            UserRole.role_id == role_id
        ).all()
    
    def has_role(self, user_id: int, role_id: int) -> bool:
        """检查用户是否拥有指定角色"""
        return self.db.query(UserRole).filter(
            and_(UserRole.user_id == user_id, UserRole.role_id == role_id)
        ).first() is not None
    
    def has_role_by_name(self, user_id: int, role_name: str) -> bool:
        """检查用户是否拥有指定名称的角色"""
        return self.db.query(UserRole).join(Role).filter(
            and_(UserRole.user_id == user_id, Role.name == role_name)
        ).first() is not None
    
    def _log_permission_operation(self, operator_id: int, operation_type: str, 
                                 target_type: str, target_id: int, 
                                 role_id: Optional[int] = None, 
                                 permission_id: Optional[int] = None):
        """记录权限操作日志"""
        log = PermissionOperationLog(
            operator_id=operator_id,
            operation_type=operation_type,
            target_type=target_type,
            target_id=target_id,
            role_id=role_id,
            permission_id=permission_id
        )
        self.db.add(log)
        self.db.commit()


class AuthorizationService:
    """权限验证服务"""
    
    def __init__(self, db_session: Session, cache_service: Optional[CacheService] = None):
        self.db = db_session
        self.cache = cache_service
        self.role_service = RoleService(db_session, cache_service)
        self.user_role_service = UserRoleService(db_session, cache_service)
    
    def has_permission(self, user_id: int, permission_code: str) -> bool:
        """检查用户是否拥有指定权限"""
        # 先检查缓存
        if self.cache:
            cached_permissions = self.cache.get_user_permissions(user_id)
            if cached_permissions:
                return permission_code in cached_permissions
        
        # 获取用户所有权限
        user_permissions = self.get_user_permissions(user_id)
        permission_codes = {perm.code for perm in user_permissions}
        
        # 缓存用户权限
        if self.cache:
            self.cache.cache_user_permissions(user_id, list(permission_codes))
        
        return permission_code in permission_codes
    
    def get_user_permissions(self, user_id: int) -> List[Permission]:
        """获取用户的所有权限（包括通过角色继承的权限）"""
        permissions = set()
        
        # 获取用户的所有角色
        user_roles = self.user_role_service.get_user_roles(user_id)
        
        for role in user_roles:
            # 获取角色层级的所有权限
            role_permission_codes = self.role_service.get_role_hierarchy_permissions(role.id)
            
            # 根据权限编码获取权限对象
            role_permissions = self.db.query(Permission).filter(
                Permission.code.in_(role_permission_codes)
            ).all()
            
            permissions.update(role_permissions)
        
        return list(permissions)
    
    def get_user_permission_codes(self, user_id: int) -> List[str]:
        """获取用户的所有权限编码"""
        permissions = self.get_user_permissions(user_id)
        return [perm.code for perm in permissions]
    
    def get_user_role_names(self, user_id: int) -> List[str]:
        """获取用户的所有角色名称"""
        roles = self.user_role_service.get_user_roles(user_id)
        return [role.name for role in roles]
    
    def check_resource_permission(self, user_id: int, resource_id: int, 
                                 resource_type: str, action: str) -> bool:
        """检查用户对特定资源的权限"""
        # 构建权限编码
        permission_code = f"{resource_type}.{action}"
        
        # 检查用户是否有该权限
        if not self.has_permission(user_id, permission_code):
            return False
        
        # TODO: 实现资源级权限控制（如数据权限隔离）
        # 这里可以添加更细粒度的资源访问控制逻辑
        
        return True
    
    def filter_accessible_resources(self, user_id: int, resources: List[Dict], 
                                   resource_type: str, action: str) -> List[Dict]:
        """过滤用户可访问的资源列表"""
        # 检查用户是否有基础权限
        permission_code = f"{resource_type}.{action}"
        if not self.has_permission(user_id, permission_code):
            return []
        
        # TODO: 实现资源级过滤逻辑
        # 根据用户权限和资源所有者等条件过滤资源
        
        return resources
```
