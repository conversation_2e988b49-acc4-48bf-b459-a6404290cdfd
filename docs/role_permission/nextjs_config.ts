/**
 * FinSight NextJS 前端权限配置
 * 基于后端 RBAC 系统的完整前端配置
 */

// ==================== 权限常量定义 ====================

export const PERMISSIONS = {
  // 用户管理模块
  USER: {
    PROFILE_READ: 'user.profile.read',
    PROFILE_UPDATE: 'user.profile.update', 
    LIST_READ: 'user.list.read',
    DETAIL_READ: 'user.detail.read',
    CREATE: 'user.create',
    UPDATE: 'user.update',
    DELETE: 'user.delete',
    STATUS_UPDATE: 'user.status.update',
    ROLE_ASSIGN: 'user.role.assign',
    ROLE_REVOKE: 'user.role.revoke',
    ROLE_READ: 'user.role.read',
    PERMISSION_READ: 'user.permission.read',
    ROLE_BATCH_ASSIGN: 'user.role.batch.assign',
    ROLE_BATCH_REVOKE: 'user.role.batch.revoke',
    PREFERENCES_READ: 'user.preferences.read',
    PREFERENCES_UPDATE: 'user.preferences.update'
  },

  // 数据管理模块
  DATA: {
    SOURCE_READ: 'data.source.read',
    SOURCE_CREATE: 'data.source.create',
    SOURCE_UPDATE: 'data.source.update',
    SOURCE_DELETE: 'data.source.delete',
    TASK_READ: 'data.task.read',
    TASK_CREATE: 'data.task.create',
    TASK_EXECUTE: 'data.task.execute',
    QUERY_EXECUTE: 'data.query.execute',
    REPORT_READ: 'data.report.read',
    REPORT_CREATE: 'data.report.create',
    EXPORT: 'data.export'
  },

  // 数据源管理模块
  DATA_SOURCE: {
    DATA_SOURCE_CREATE: 'data_source.data_source.create',
    DATA_SOURCE_READ: 'data_source.data_source.read',
    DATA_SOURCE_UPDATE: 'data_source.data_source.update',
    DATA_SOURCE_DELETE: 'data_source.data_source.delete',
    LIST_READ: 'data_source.list.read',
    STATS_READ: 'data_source.stats.read',
    CONFIG_CREATE: 'data_source.config.create',
    CONFIG_READ: 'data_source.config.read',
    CONFIG_UPDATE: 'data_source.config.update',
    CONFIG_DELETE: 'data_source.config.delete',
    CONFIG_MANAGE: 'data_source.config.manage'
  },

  // 原始数据记录模块
  RAW_DATA_RECORD: {
    RAW_DATA_RECORD_CREATE: 'raw_data_record.raw_data_record.create',
    RAW_DATA_RECORD_READ: 'raw_data_record.raw_data_record.read',
    RAW_DATA_RECORD_UPDATE: 'raw_data_record.raw_data_record.update',
    RAW_DATA_RECORD_DELETE: 'raw_data_record.raw_data_record.delete',
    RAW_DATA_RECORD_MANAGE: 'raw_data_record.raw_data_record.manage',
    RAW_DATA_RECORD_ANALYZE: 'raw_data_record.raw_data_record.analyze',
    LIST_READ: 'raw_data_record.list.read',
    STATS_READ: 'raw_data_record.stats.read'
  },

  // 系统管理模块
  SYSTEM: {
    CONFIG_READ: 'system.config.read',
    CONFIG_UPDATE: 'system.config.update',
    LOG_READ: 'system.log.read',
    BACKUP: 'system.backup',
    PERMISSION_MANAGE: 'system.permission.manage'
  }
} as const;

// ==================== 角色常量定义 ====================

export const ROLES = {
  ADMIN: 'Admin',
  DATA_ADMIN: 'DataAdmin',
  ANALYST: 'Analyst', 
  EDITOR: 'Editor',
  ACCOUNT_MANAGER: 'AccountManager',
  RISK_OFFICER: 'RiskOfficer',
  USER: 'User',
  GUEST: 'Guest'
} as const;

// ==================== 菜单配置 ====================

export interface MenuConfig {
  key: string;
  label: string;
  icon?: string;
  path?: string;
  requiredPermissions?: string[];
  requiredRoles?: string[];
  children?: MenuConfig[];
  module?: string;
}

export const MENU_CONFIG: MenuConfig[] = [
  {
    key: 'dashboard',
    label: '仪表板',
    icon: 'DashboardOutlined',
    path: '/dashboard',
    requiredPermissions: [PERMISSIONS.DATA.REPORT_READ]
  },
  {
    key: 'users',
    label: '用户管理',
    icon: 'UserOutlined',
    module: 'user',
    requiredPermissions: [PERMISSIONS.USER.LIST_READ],
    children: [
      {
        key: 'user-list',
        label: '用户列表',
        path: '/users',
        requiredPermissions: [PERMISSIONS.USER.LIST_READ]
      },
      {
        key: 'user-create',
        label: '创建用户',
        path: '/users/create',
        requiredPermissions: [PERMISSIONS.USER.CREATE]
      }
    ]
  },
  {
    key: 'data',
    label: '数据管理',
    icon: 'DatabaseOutlined',
    module: 'data',
    requiredRoles: [ROLES.DATA_ADMIN, ROLES.ANALYST, ROLES.ADMIN],
    children: [
      {
        key: 'data-sources',
        label: '数据源管理',
        path: '/data/sources',
        requiredPermissions: [PERMISSIONS.DATA.SOURCE_READ]
      }
    ]
  }
];

export default {
  PERMISSIONS,
  ROLES,
  MENU_CONFIG
}; 