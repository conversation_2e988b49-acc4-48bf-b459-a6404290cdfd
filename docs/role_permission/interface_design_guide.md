# 角色权限系统 API 接口设计指南（微服务架构）

## 1. 架构设计原则

### 1.1 微服务架构原则
- **接口独立**：C端和B端通过不同的微服务模块提供服务
- **数据统一**：共享同一套数据模型和数据库
- **部署统一**：在同一个应用中部署，通过服务模块区分不同端，C端用户路由在router.py，B端路由在admin_router.py

### 1.2 目录结构设计
```
src/
├── services/
│   ├── user_service/                    # 现有用户服务（扩展）
│   │   ├── __init__.py
│   │   ├── models.py                    # 用户模型（现有）
│   │   ├── schemas.py                   # 用户DTO（现有）
│   │   ├── service.py                   # 用户业务逻辑（现有）
│   │   ├── router.py                    # C端用户路由
│   │   ├── admin_router.py              # B端用户管理路由
│   │   └── dependencies.py             # 依赖注入（现有）
│   │
│   ├── permission_service/              # 权限服务（已实现）
│   │   ├── __init__.py
│   │   ├── models.py                    # 权限相关模型
│   │   ├── schemas.py                   # 权限DTO
│   │   ├── service.py                   # 权限业务逻辑
│   │   ├── router.py                    # C端权限查询路由
│   │   ├── admin_router.py              # B端权限管理路由
│   │   ├── dependencies.py             # 权限依赖
│   │   └── cache.py                     # 权限缓存服务
│   │
│   ├── tag_classification_service/      # 现有标签分类服务（扩展）
│   │   ├── __init__.py
│   │   ├── models.py                    # 标签模型（现有）
│   │   ├── schemas.py                   # 标签DTO（现有）
│   │   ├── service.py                   # 标签业务逻辑（现有）
│   │   ├── router.py                    # C端标签查询路由（现有，需扩展）
│   │   ├── admin_router.py              # B端标签管理路由（新增）
│   │   └── dependencies.py             # 标签依赖（现有）
│   │
│   ├── financial_calendar_service/      # 现有财经日历服务（扩展）
│   │   ├── __init__.py
│   │   ├── models.py                    # 财经日历模型（现有）
│   │   ├── schemas.py                   # 财经日历DTO（现有）
│   │   ├── service.py                   # 财经日历业务逻辑（现有）
│   │   ├── router.py                    # C端财经日历查询路由（现有，需扩展）
│   │   ├── admin_router.py              # B端财经日历管理路由（新增）
│   │   └── dependencies.py             # 财经日历依赖（新增）
│   │
│   ├── data_collection_service/         # 现有数据采集服务（扩展）
│   │   ├── __init__.py
│   │   ├── models.py                    # 数据模型（现有）
│   │   ├── schemas.py                   # 数据DTO（现有）
│   │   ├── service.py                   # 数据业务逻辑（现有）
│   │   ├── router.py                    # C端数据查询路由（现有，需扩展）
│   │   ├── admin_router.py              # B端数据管理路由（新增）
│   │   └── dependencies.py             # 数据依赖（现有）
│   │
│   ├── sms_service/                     # 现有短信服务（扩展）
│   │   ├── __init__.py
│   │   ├── models.py                    # 短信模型（现有）
│   │   ├── schemas.py                   # 短信DTO（现有）
│   │   ├── service.py                   # 短信业务逻辑（现有）
│   │   ├── router.py                    # C端短信服务路由（新增）
│   │   ├── admin_router.py              # B端短信管理路由（新增）
│   │   └── dependencies.py             # 短信依赖（新增）
│   │
│   ├── information_processing_service/  # 信息处理服务（待实现）
│   │   ├── __init__.py
│   │   ├── models.py                    # 信息处理模型
│   │   ├── schemas.py                   # 信息处理DTO
│   │   ├── service.py                   # 信息处理业务逻辑
│   │   ├── router.py                    # C端信息查询路由
│   │   ├── admin_router.py              # B端信息管理路由
│   │   └── dependencies.py             # 信息处理依赖
│   │
│   └── push_service/                    # 推送服务（待实现）
│       ├── __init__.py
│       ├── models.py                    # 推送模型
│       ├── schemas.py                   # 推送DTO
│       ├── service.py                   # 推送业务逻辑
│       ├── router.py                    # C端推送查询路由
│       ├── admin_router.py              # B端推送管理路由
│       └── dependencies.py             # 推送依赖
│
├── core/                                # 核心配置（现有）
└── main.py                              # 主应用（现有）
```

## 2. 微服务路由设计

### 2.1 路由前缀规范
```python
# 现有服务路由扩展设计
USER_SERVICE_PREFIX = "/api/v1/users"                    # 用户服务
PERMISSION_SERVICE_PREFIX = "/api/v1/permissions"        # 权限服务
TAG_CLASSIFICATION_PREFIX = "/api/v1/tags"              # 标签分类服务
FINANCIAL_CALENDAR_PREFIX = "/api/v1/calendar"          # 财经日历服务
DATA_COLLECTION_PREFIX = "/api/v1/data"                 # 数据采集服务
INFORMATION_PROCESSING_PREFIX = "/api/v1/information"   # 信息处理服务
SMS_SERVICE_PREFIX = "/api/v1/sms"                      # 短信服务
PUSH_SERVICE_PREFIX = "/api/v1/push"                    # 推送服务

# B端管理路由前缀
ADMIN_SERVICE_PREFIX = "/api/v1/admin"                  # 管理服务总前缀

# 路由示例：
# C端用户登录：POST /api/v1/users/auth/login
# B端管理员登录：POST /api/v1/admin/users/auth/login
# C端标签查询：GET /api/v1/tags/
# B端标签管理：GET /api/v1/admin/tags/
```

### 2.2 主应用路由注册
```python
# src/main.py (扩展现有路由注册)
from fastapi import FastAPI
from src.services.user_service.router import router as user_c_router
from src.services.user_service.admin_router import router as user_admin_router
from src.services.permission_service.router import router as permission_c_router
from src.services.permission_service.admin_router import router as permission_admin_router
from src.services.tag_classification_service.router import router as tag_c_router
from src.services.tag_classification_service.admin_router import router as tag_admin_router
from src.services.financial_calendar_service.router import router as calendar_c_router
from src.services.financial_calendar_service.admin_router import router as calendar_admin_router
from src.services.data_collection_service.router import router as data_c_router
from src.services.data_collection_service.admin_router import router as data_admin_router
from src.services.information_processing_service.router import router as info_c_router
from src.services.information_processing_service.admin_router import router as info_admin_router
from src.services.sms_service.router import router as sms_c_router
from src.services.sms_service.admin_router import router as sms_admin_router
from src.services.push_service.router import router as push_c_router
from src.services.push_service.admin_router import router as push_admin_router

app = FastAPI(title="FinSight API", version="1.0.0")

# 注册C端微服务路由
app.include_router(user_c_router, prefix="/api/v1/users", tags=["用户服务-C端"])
app.include_router(permission_c_router, prefix="/api/v1/permissions", tags=["权限服务-C端"])
app.include_router(tag_c_router, prefix="/api/v1/tags", tags=["标签分类-C端"])
app.include_router(calendar_c_router, prefix="/api/v1/calendar", tags=["财经日历-C端"])
app.include_router(data_c_router, prefix="/api/v1/data", tags=["数据采集-C端"])
app.include_router(info_c_router, prefix="/api/v1/information", tags=["信息处理-C端"])
app.include_router(sms_c_router, prefix="/api/v1/sms", tags=["短信服务-C端"])
app.include_router(push_c_router, prefix="/api/v1/push", tags=["推送服务-C端"])

# 注册B端微服务路由
app.include_router(user_admin_router, prefix="/api/v1/admin/users", tags=["用户服务-B端"])
app.include_router(permission_admin_router, prefix="/api/v1/admin/permissions", tags=["权限服务-B端"])
app.include_router(tag_admin_router, prefix="/api/v1/admin/tags", tags=["标签分类-B端"])
app.include_router(calendar_admin_router, prefix="/api/v1/admin/calendar", tags=["财经日历-B端"])
app.include_router(data_admin_router, prefix="/api/v1/admin/data", tags=["数据采集-B端"])
app.include_router(info_admin_router, prefix="/api/v1/admin/information", tags=["信息处理-B端"])
app.include_router(sms_admin_router, prefix="/api/v1/admin/sms", tags=["短信服务-B端"])
app.include_router(push_admin_router, prefix="/api/v1/admin/push", tags=["推送服务-B端"])
```

## 3. 现有服务扩展指南

### 3.1 服务实现状态概览
| 服务模块 | C端路由 | B端路由 | 权限集成 | 状态 |
|---------|---------|---------|----------|------|
| user_service | 存在 | 需新增 | 需集成 | 扩展中 |
| permission_service | 已实现 | 已实现 | 已集成 | 完成 |
| tag_classification_service | 存在 | 需新增 | 需集成 | 扩展中 |
| financial_calendar_service | 存在 | 需新增 | 需集成 | 扩展中 |
| data_collection_service | 存在 | 需新增 | 需集成 | 扩展中 |
| sms_service | 需新增 | 需新增 | 需集成 | 待实现 |
| information_processing_service | 需新增 | 需新增 | 需集成 | 待实现 |
| push_service | 需新增 | 需新增 | 需集成 | 待实现 |

### 3.2 公共组件实现

#### 3.2.1 权限装饰器扩展
```python
# src/core/permission_decorators.py
from functools import wraps
from fastapi import HTTPException, Depends
from fastapi.security import HTTPBearer
from sqlalchemy.orm import Session
from src.core.database import get_db
from src.services.permission_service.service import AuthorizationService
from src.services.user_service.service import JWTService

oauth2_scheme = HTTPBearer()
jwt_service = JWTService()

def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    """获取当前C端用户"""
    payload = jwt_service.verify_token(token.credentials)
    if not payload or payload.get("type") != "access":
        raise HTTPException(status_code=401, detail="Invalid token")
    
    user_id = payload.get("sub")
    # 从用户服务获取用户信息
    from src.services.user_service.service import UserService
    user_service = UserService(db)
    user = user_service.get_user_by_id(user_id)
    
    if not user:
        raise HTTPException(status_code=401, detail="User not found")
    
    return user

def get_current_admin(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    """获取当前B端管理员"""
    payload = jwt_service.verify_token(token.credentials)
    if not payload or payload.get("type") != "access":
        raise HTTPException(status_code=401, detail="Invalid token")
    
    user_id = payload.get("sub")
    
    # 验证是否为管理员
    if not AuthorizationService(db).is_admin_user(user_id):
        raise HTTPException(status_code=403, detail="Admin access required")
    
    from src.services.user_service.service import UserService
    user_service = UserService(db)
    user = user_service.get_user_by_id(user_id)
    
    if not user:
        raise HTTPException(status_code=401, detail="Admin not found")
    
    return user

def require_permission(permission: str):
    """C端权限验证装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            current_user = kwargs.get('current_user')
            db = kwargs.get('db')
            
            if not AuthorizationService(db).check_user_permission(current_user.id, permission):
                raise HTTPException(status_code=403, detail="Permission denied")
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator

def require_admin_permission(permission: str):
    """B端权限验证装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            current_admin = kwargs.get('current_admin')
            db = kwargs.get('db')
            
            if not AuthorizationService(db).check_user_permission(current_admin.id, permission):
                raise HTTPException(status_code=403, detail="Admin permission denied")
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator
```

## 4. 各服务模块双路由实现详解

### 4.1 用户服务扩展（双路由结构）

#### 4.1.1 C端用户路由（router.py）
```python
# src/services/user_service/router.py
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from src.core.database import get_db
from src.core.permission_decorators import get_current_user, require_permission
from .schemas import UserLoginRequest, UserProfileUpdate, UserPreferencesUpdate
from .service import UserService

router = APIRouter()

@router.post("/auth/login")
async def user_login(
    login_data: UserLoginRequest,
    db: Session = Depends(get_db)
):
    """C端用户登录"""
    user_service = UserService(db)
    result = await user_service.authenticate_by_phone(
        login_data.phone, login_data.verification_code
    )
    
    if not result:
        raise HTTPException(status_code=401, detail="登录失败")
    
    return result

@router.get("/profile")
@require_permission("user.profile.read")
async def get_user_profile(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户资料"""
    return {
        "id": current_user.id,
        "phone": current_user.phone,
        "user_type": current_user.user_type,
        "nickname": current_user.nickname,
        "risk_level": current_user.risk_level,
        "knowledge_level": current_user.knowledge_level
    }

@router.put("/profile")
@require_permission("user.profile.update")
async def update_user_profile(
    profile_data: UserProfileUpdate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新用户资料"""
    user_service = UserService(db)
    updated_user = await user_service.update_profile(
        current_user.id, profile_data.dict(exclude_unset=True)
    )
    return {"message": "资料更新成功", "user": updated_user}

@router.get("/preferences")
@require_permission("user.preferences.read")
async def get_user_preferences(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户偏好设置"""
    user_service = UserService(db)
    preferences = await user_service.get_user_preferences(current_user.id)
    return preferences

@router.put("/preferences")
@require_permission("user.preferences.update")
async def update_user_preferences(
    preferences_data: UserPreferencesUpdate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新用户偏好设置"""
    user_service = UserService(db)
    await user_service.update_user_preferences(
        current_user.id, preferences_data.dict(exclude_unset=True)
    )
    return {"message": "偏好设置更新成功"}
```

#### 4.1.2 B端用户管理（admin_router.py）
```python
# src/services/user_service/admin_router.py  
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional
from src.core.database import get_db
from src.core.permission_decorators import get_current_admin, require_admin_permission
from .schemas import AdminLoginRequest, UserStatusUpdate, UserRoleAssignment
from .service import UserService

router = APIRouter()

@router.post("/auth/login")
async def admin_login(
    login_data: AdminLoginRequest,
    db: Session = Depends(get_db)
):
    """B端管理员登录"""
    user_service = UserService(db)
    result = await user_service.authenticate_admin(
        login_data.username, login_data.password
    )
    
    if not result:
        raise HTTPException(status_code=401, detail="管理员登录失败")
    
    return result

@router.get("/")
@require_admin_permission("user.list.read")
async def list_users(
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    user_type: Optional[str] = None,
    status: Optional[str] = None,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin)
):
    """获取用户列表"""
    user_service = UserService(db)
    users, total = await user_service.get_users_paginated(
        page, size, user_type, status
    )
    return {
        "users": users,
        "total": total,
        "page": page,
        "size": size
    }

@router.get("/{user_id}")
@require_admin_permission("user.detail.read")
async def get_user_detail(
    user_id: int,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin)
):
    """获取用户详情"""
    user_service = UserService(db)
    user = await user_service.get_user_by_id(user_id)
    
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 获取用户角色和权限
    from src.services.permission_service.service import AuthorizationService
    auth_service = AuthorizationService(db)
    roles = await auth_service.get_user_roles(user_id)
    permissions = await auth_service.get_user_permissions(user_id)
    
    return {
        "user": user,
        "roles": roles,
        "permissions": permissions
    }

@router.put("/{user_id}/status")
@require_admin_permission("user.status.update")
async def update_user_status(
    user_id: int,
    status_data: UserStatusUpdate,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin)
):
    """更新用户状态"""
    user_service = UserService(db)
    updated_user = await user_service.update_user_status(
        user_id, status_data.status
    )
    
    # 记录操作日志
    await user_service.log_admin_operation(
        operator_id=current_admin.id,
        operation="update_user_status",
        target_id=user_id,
        details=status_data.dict()
    )
    
    return {"message": "用户状态更新成功", "user": updated_user}

@router.post("/{user_id}/roles")
@require_admin_permission("user.role.assign")
async def assign_user_role(
    user_id: int,
    role_assignment: UserRoleAssignment,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin)
):
    """为用户分配角色"""
    from src.services.permission_service.service import UserRoleService
    role_service = UserRoleService(db)
    
    await role_service.assign_role_to_user(user_id, role_assignment.role_id)
    
    # 记录操作日志
    user_service = UserService(db)
    await user_service.log_admin_operation(
        operator_id=current_admin.id,
        operation="assign_user_role",
        target_id=user_id,
        details=role_assignment.dict()
    )
    
    return {"message": "角色分配成功"}

@router.delete("/{user_id}/roles/{role_id}")
@require_admin_permission("user.role.revoke")
async def revoke_user_role(
    user_id: int,
    role_id: int,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin)
):
    """撤销用户角色"""
    from src.services.permission_service.service import UserRoleService
    role_service = UserRoleService(db)
    
    await role_service.revoke_role_from_user(user_id, role_id)
    
    # 记录操作日志
    user_service = UserService(db)
    await user_service.log_admin_operation(
        operator_id=current_admin.id,
        operation="revoke_user_role",
        target_id=user_id,
        details={"role_id": role_id}
    )
    
    return {"message": "角色撤销成功"}
```

### 4.2 短信服务扩展（双路由结构）

#### 4.2.1 C端短信服务（router.py）
```python
# src/services/sms_service/router.py
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from src.core.database import get_db
from src.core.permission_decorators import get_current_user, require_permission
from .schemas import VerificationCodeRequest, SmsHistoryResponse
from .service import SmsService

router = APIRouter()

@router.post("/verification-code")
async def send_verification_code(
    request: VerificationCodeRequest,
    db: Session = Depends(get_db)
):
    """发送验证码（公开接口，无需认证）"""
    sms_service = SmsService(db)
    result = await sms_service.send_verification_code(
        request.phone, request.purpose
    )
    
    if not result.success:
        raise HTTPException(status_code=400, detail=result.message)
    
    return {"message": "验证码发送成功"}

@router.get("/history")
@require_permission("sms.history.read")
async def get_sms_history(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取我的短信历史（只能查看自己的）"""
    sms_service = SmsService(db)
    history = await sms_service.get_user_sms_history(current_user.phone)
    return {"history": history}

@router.get("/status")
async def get_sms_service_status(
    db: Session = Depends(get_db)
):
    """获取短信服务状态（公开接口）"""
    sms_service = SmsService(db)
    status = sms_service.get_service_status()
    return status
```

#### 4.2.2 B端短信管理（admin_router.py）
```python
# src/services/sms_service/admin_router.py
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional
from src.core.database import get_db
from src.core.permission_decorators import get_current_admin, require_admin_permission
from .schemas import (
    SmsBusinessCreate, SmsBusinessUpdate, SmsTemplateCreate, 
    SmsSignatureCreate, SmsBatchSendRequest
)
from .service import SmsService

router = APIRouter()

@router.post("/businesses")
@require_admin_permission("sms.business.create")
async def create_sms_business(
    business_data: SmsBusinessCreate,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin)
):
    """创建短信业务配置"""
    sms_service = SmsService(db)
    business = await sms_service.create_business_config(
        business_data.dict(), current_admin.id
    )
    return business

@router.get("/businesses")
@require_admin_permission("sms.business.list.read")
async def list_sms_businesses(
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin)
):
    """获取所有短信业务配置"""
    sms_service = SmsService(db)
    businesses = await sms_service.get_all_business_configs()
    return businesses

@router.put("/businesses/{business_id}")
@require_admin_permission("sms.business.update")
async def update_sms_business(
    business_id: int,
    business_data: SmsBusinessUpdate,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin)
):
    """更新短信业务配置"""
    sms_service = SmsService(db)
    business = await sms_service.update_business_config(
        business_id, business_data.dict(exclude_unset=True)
    )
    return business

@router.post("/templates")
@require_admin_permission("sms.template.create")
async def create_sms_template(
    template_data: SmsTemplateCreate,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin)
):
    """创建短信模板"""
    sms_service = SmsService(db)
    template = await sms_service.create_template(
        template_data.dict(), current_admin.id
    )
    return template

@router.get("/templates")
@require_admin_permission("sms.template.list.read")
async def list_sms_templates(
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin)
):
    """获取所有短信模板"""
    sms_service = SmsService(db)
    templates = await sms_service.get_all_templates()
    return templates

@router.post("/signatures")
@require_admin_permission("sms.signature.create")
async def create_sms_signature(
    signature_data: SmsSignatureCreate,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin)
):
    """创建短信签名"""
    sms_service = SmsService(db)
    signature = await sms_service.create_signature(
        signature_data.dict(), current_admin.id
    )
    return signature

@router.post("/send-batch")
@require_admin_permission("sms.batch.send")
async def send_batch_sms(
    send_request: SmsBatchSendRequest,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin)
):
    """批量发送短信"""
    sms_service = SmsService(db)
    result = await sms_service.send_batch_sms(
        send_request.dict(), current_admin.id
    )
    return result

@router.get("/statistics")
@require_admin_permission("sms.statistics.read")
async def get_sms_statistics(
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin)
):
    """获取短信发送统计"""
    sms_service = SmsService(db)
    statistics = await sms_service.get_sms_statistics(start_date, end_date)
    return statistics

@router.get("/logs")
@require_admin_permission("sms.logs.read")
async def get_sms_logs(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    phone: Optional[str] = Query(None),
    business_name: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin)
):
    """获取短信发送日志"""
    sms_service = SmsService(db)
    logs, total = await sms_service.get_sms_logs_paginated(
        page, size, phone, business_name
    )
    return {
        "logs": logs,
        "total": total,
        "page": page,
        "size": size
    }
```

## 5. 微服务路由表总览

### 5.1 完整路由映射表
```
项目路由架构总览

C端路由 (用户侧)：
├── /api/v1/users/*                    # 用户服务 (C端)
│   ├── /auth/login                   # 用户登录
│   ├── /profile                      # 用户资料
│   ├── /preferences                  # 用户偏好
│   └── /auth/logout                  # 用户登出
│
├── /api/v1/permissions/*             # 权限服务 (C端)
│   ├── /me                          # 我的权限
│   ├── /me/roles                    # 我的角色
│   └── /check                       # 权限检查
│
├── /api/v1/tags/*                    # 标签分类 (C端)
│   ├── /categories                  # 分类列表
│   ├── /                           # 标签列表
│   ├── /my-preferences             # 我的标签偏好
│   └── /recommendations            # 标签推荐
│
├── /api/v1/calendar/*                # 财经日历 (C端)
│   ├── /events                      # 财经事件
│   ├── /events/{id}                 # 事件详情
│   ├── /indicators                  # 经济指标
│   ├── /my-subscriptions           # 我的订阅
│   └── /events/{id}/subscribe      # 订阅事件
│
├── /api/v1/data/*                    # 数据采集 (C端)
│   ├── /sources                     # 数据源查询
│   ├── /sources/{id}/data          # 查询数据
│   ├── /tasks/my                   # 我的任务
│   └── /tasks/{id}/trigger         # 触发任务
│
├── /api/v1/sms/*                     # 短信服务 (C端)
│   ├── /verification-code          # 发送验证码
│   ├── /history                    # 短信历史
│   └── /status                     # 服务状态
│
├── /api/v1/information/*             # 信息处理 (C端)
│   ├── /articles                   # 文章列表
│   ├── /articles/{id}              # 文章详情
│   ├── /summaries                  # 摘要列表
│   └── /my-feeds                   # 我的订阅
│
└── /api/v1/push/*                    # 推送服务 (C端)
    ├── /notifications              # 通知列表
    ├── /preferences                # 推送偏好
    └── /subscribe                  # 订阅推送

B端路由 (管理侧)：
├── /api/v1/admin/users/*             # 用户管理 (B端)
│   ├── /auth/login                 # 管理员登录
│   ├── /                          # 用户列表
│   ├── /{id}                       # 用户详情
│   ├── /{id}/status                # 更新状态
│   ├── /{id}/roles                 # 角色管理
│   └── /{id}/roles/{role_id}       # 角色操作
│
├── /api/v1/admin/permissions/*       # 权限管理 (B端)
│   ├── /permissions                # 权限CRUD
│   ├── /roles                      # 角色CRUD
│   ├── /roles/{id}/permissions     # 角色权限
│   ├── /users/{id}/permissions     # 用户权限
│   └── /analytics                  # 权限分析
│
├── /api/v1/admin/tags/*              # 标签管理 (B端)
│   ├── /tags                       # 标签CRUD
│   ├── /categories                 # 分类CRUD
│   ├── /analytics/tags             # 标签统计
│   └── /analytics/users/{id}/tags  # 用户标签分析
│
├── /api/v1/admin/calendar/*          # 财经日历管理 (B端)
│   ├── /events                     # 事件CRUD
│   ├── /event-types                # 事件类型CRUD
│   ├── /indicators                 # 指标CRUD
│   └── /analytics/events           # 事件分析
│
├── /api/v1/admin/data/*              # 数据管理 (B端)
│   ├── /sources                    # 数据源管理
│   ├── /tasks                      # 任务管理
│   ├── /tasks/{id}/start           # 启动任务
│   ├── /tasks/{id}/stop            # 停止任务
│   ├── /users/{id}/sources/{sid}/access  # 用户数据源权限
│   └── /monitoring/tasks           # 任务监控
│
├── /api/v1/admin/sms/*               # 短信管理 (B端)
│   ├── /businesses                 # 业务配置
│   ├── /templates                  # 模板管理
│   ├── /signatures                 # 签名管理
│   ├── /send-batch                 # 批量发送
│   ├── /statistics                 # 发送统计
│   └── /logs                       # 发送日志
│
├── /api/v1/admin/information/*       # 信息管理 (B端)
│   ├── /articles                   # 文章管理
│   ├── /sources                    # 信息源管理
│   ├── /processing/tasks           # 处理任务
│   ├── /quality/rules              # 质量规则
│   └── /analytics/processing       # 处理分析
│
└── /api/v1/admin/push/*              # 推送管理 (B端)
    ├── /channels                   # 推送渠道
    ├── /templates                  # 推送模板
    ├── /campaigns                  # 推送活动
    ├── /send-batch                 # 批量推送
    └── /analytics/push             # 推送分析
```

### 5.2 权限编码规范
```python
# 各服务的权限编码规范
PERMISSION_CODES = {
    # 用户服务权限
    "user.profile.read": "读取用户资料",
    "user.profile.update": "更新用户资料", 
    "user.preferences.read": "读取用户偏好",
    "user.preferences.update": "更新用户偏好",
    "user.list.read": "查看用户列表",
    "user.detail.read": "查看用户详情",
    "user.status.update": "更新用户状态",
    "user.role.assign": "分配用户角色",
    "user.role.revoke": "撤销用户角色",
    
    # 权限服务权限
    "permission.create": "创建权限",
    "permission.update": "更新权限",
    "permission.delete": "删除权限",
    "permission.list.read": "查看权限列表",
    "role.create": "创建角色",
    "role.update": "更新角色",
    "role.delete": "删除角色",
    "role.list.read": "查看角色列表",
    "role.permission.assign": "分配角色权限",
    "role.permission.revoke": "撤销角色权限",
    
    # 标签分类权限
    "tag.read": "查看标签",
    "tag.create": "创建标签",
    "tag.update": "更新标签",
    "tag.delete": "删除标签",
    "tag.list.read": "查看标签列表",
    "tag.preference.read": "查看标签偏好",
    "tag.preference.update": "更新标签偏好",
    "tag.recommendation.read": "查看标签推荐",
    "tag.analytics.read": "查看标签统计",
    "category.create": "创建分类",
    "category.update": "更新分类",
    "category.list.read": "查看分类列表",
    
    # 财经日历权限
    "calendar.event.read": "查看财经事件",
    "calendar.event.detail.read": "查看事件详情",
    "calendar.event.create": "创建财经事件",
    "calendar.event.update": "更新财经事件",
    "calendar.event.delete": "删除财经事件",
    "calendar.event.list.read": "查看事件列表",
    "calendar.subscription.read": "查看订阅",
    "calendar.subscription.create": "创建订阅",
    "calendar.indicator.read": "查看经济指标",
    "calendar.indicator.create": "创建经济指标",
    "calendar.indicator.list.read": "查看指标列表",
    "calendar.analytics.read": "查看日历分析",
    
    # 数据采集权限
    "data.source.read": "查看数据源",
    "data.source.query": "查询数据源",
    "data.source.create": "创建数据源",
    "data.source.update": "更新数据源",
    "data.source.delete": "删除数据源",
    "data.source.list.read": "查看数据源列表",
    "data.task.read": "查看采集任务",
    "data.task.execute": "执行采集任务",
    "data.task.create": "创建采集任务",
    "data.task.update": "更新采集任务",
    "data.task.control": "控制采集任务",
    "data.task.list.read": "查看任务列表",
    "data.access.grant": "授权数据访问",
    "data.access.revoke": "撤销数据访问",
    "data.monitoring.read": "查看数据监控",
    
    # 短信服务权限
    "sms.history.read": "查看短信历史",
    "sms.business.create": "创建短信业务",
    "sms.business.update": "更新短信业务",
    "sms.business.list.read": "查看业务列表",
    "sms.template.create": "创建短信模板",
    "sms.template.list.read": "查看模板列表",
    "sms.signature.create": "创建短信签名",
    "sms.batch.send": "批量发送短信",
    "sms.statistics.read": "查看短信统计",
    "sms.logs.read": "查看短信日志",
    
    # 信息处理权限
    "information.article.read": "查看文章",
    "information.article.create": "创建文章",
    "information.article.update": "更新文章",
    "information.article.delete": "删除文章",
    "information.source.create": "创建信息源",
    "information.source.update": "更新信息源",
    "information.processing.read": "查看处理任务",
    "information.processing.control": "控制处理任务",
    "information.quality.manage": "管理质量规则",
    "information.analytics.read": "查看处理分析",
    
    # 推送服务权限
    "push.notification.read": "查看通知",
    "push.preference.read": "查看推送偏好",
    "push.preference.update": "更新推送偏好",
    "push.subscribe": "订阅推送",
    "push.channel.create": "创建推送渠道",
    "push.channel.update": "更新推送渠道",
    "push.template.create": "创建推送模板",
    "push.campaign.create": "创建推送活动",
    "push.campaign.control": "控制推送活动",
    "push.batch.send": "批量推送",
    "push.analytics.read": "查看推送分析",
}
```

## 6. 总结

### 6.1 架构优势
1. **统一部署**：所有服务在同一个FastAPI应用中，简化部署和维护
2. **清晰分离**：C端和B端接口完全分离，职责明确
3. **权限统一**：统一的权限管理体系，支持细粒度控制
4. **扩展灵活**：支持新服务模块的快速集成

### 6.2 实施建议
1. **分阶段实施**：优先实现核心服务的双路由，再逐步扩展
2. **权限优先**：确保权限服务完全实现后再进行其他服务扩展
3. **测试先行**：每个服务模块都要有完整的测试覆盖
4. **文档同步**：及时更新API文档和使用示例

### 6.3 注意事项
1. **性能考虑**：权限验证要使用缓存，避免频繁数据库查询
2. **安全防护**：B端接口要有额外的安全验证措施
3. **错误处理**：统一的错误处理和响应格式
4. **日志审计**：重要操作要有完整的审计日志

这种微服务双路由架构既保持了代码的模块化，又实现了C端和B端的完全分离，是一个平衡性很好的解决方案。
