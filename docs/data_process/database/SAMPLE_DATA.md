# FinSight 数据库示例数据

## 更新说明

本文档包含了 FinSight 系统的完整示例数据，最新更新包括：

### 数据库设计更新 (2024-12-20)
- **数据源字段拆分**: 将原`source_type`字段拆分为`collection_method`(技术维度)和`content_category`(业务维度)
- **数据源表字段完善**: 新增`max_concurrent_tasks`, `use_proxy`, `request_delay_min/max`, `consecutive_error_count`, `avg_response_time_ms`, `current_config_version`, `created_by`, `tags`等字段
- **原始数据记录增强**: 新增`canonical_url`, `url_domain`, `content_hash`, `content_simhash`, `content_length`, `content_encoding`, `subcategory`, `social_metrics`, `lifecycle_management`等字段
- **财经事件表完善**: 新增`actual_time`, `time_zone`, `time_precision`, `market_impact`, `actual_value`, `revised_value`, `value_format`, `impact_direction`, `volatility_expected`, `event_description`, `impact_analysis`, `data_source`, `source_url`, `version`, `subscriber_count`, `notification_sent`等字段
- **用户兴趣标签增强**: 新增`daily_decay_rate`, `source`, `confidence`等字段，支持更精细的兴趣衰减和来源追踪
- **配置表结构优化**: `data_source_configs`表新增`javascript_config`, `anti_crawler_config`, `retry_config`, `is_validated`, `validation_result`等字段
- **认证凭证管理**: 独立的`data_source_credentials`表，支持加密存储和多种认证类型
- **事件驱动采集系统**: 完整的事件驱动采集表结构和示例数据
- **多维度分类**: 支持技术方式与业务类型的灵活组合

### 新增内容 (2024-12-20)
- **事件驱动采集系统示例数据**: 新增了事件驱动采集相关表的示例数据
- **数据源表更新**: 增加了采集模式、实时支持等新字段的示例
- **采集任务表增强**: 增加了事件驱动任务的示例数据
- **事件采集关联数据**: 展示了财经事件与采集任务的关联关系
- **调度器状态数据**: 提供了事件驱动调度器的运行状态示例
- **认证凭证管理**: 提供多种认证方式的加密存储示例，包括API密钥、用户名密码、OAuth令牌等

### 特色功能展示
- **多种采集模式**: interval定时、event_driven事件驱动、hybrid混合模式
- **多种数据源类型**: 支持web_scraping网页解析、api_json JSON接口、api_rss RSS订阅
- **多种认证方式**: none无认证、api_key API密钥、basic_auth基础认证、oauth_token OAuth令牌、custom_header自定义头
- **灵活触发机制**: 财经事件触发、时间基准触发、手动触发
- **完整业务场景**: 从美联储利率决议到非农就业数据的完整采集流程

## 1. 分类标签模块示例数据

### 1.1 tag_types (标签类型表)

```sql
INSERT INTO tag_types (id, type_code, type_name, description, icon, color, sort_order, is_active) VALUES
(1, 'general', '通用标签', '通用性质的标签，适用于各种场景', 'tag-icon', '#9E9E9E', 1, TRUE),
(2, 'entity', '实体标签', '具体的实体对象，如公司、机构、人物等', 'building-icon', '#2196F3', 2, TRUE),
(3, 'keyword', '关键词标签', '从内容中提取的关键词', 'key-icon', '#FF9800', 3, TRUE),
(4, 'concept', '概念标签', '抽象概念，如经济指标、金融概念等', 'lightbulb-icon', '#4CAF50', 4, TRUE),
(5, 'topic', '主题标签', '内容主题分类', 'topic-icon', '#9C27B0', 5, TRUE);
```

### 1.2 tag_categories (标签分类表)

```sql
INSERT INTO tag_categories (id, category_code, category_name, parent_id, level, description, icon, color, sort_order, is_active) VALUES
(1, 'organization', '机构组织', NULL, 1, '政府机构、金融机构、企业组织等', 'organization-icon', '#1976D2', 1, TRUE),
(2, 'company', '公司企业', NULL, 1, '上市公司、私企等各类企业实体', 'company-icon', '#388E3C', 2, TRUE),
(3, 'economic', '经济指标', NULL, 1, '各类经济数据和指标', 'chart-icon', '#F57C00', 3, TRUE),
(4, 'market', '市场相关', NULL, 1, '金融市场、交易市场相关概念', 'trending-icon', '#7B1FA2', 4, TRUE),
(5, 'industry', '行业分类', NULL, 1, '各个行业和产业分类', 'industry-icon', '#5D4037', 5, TRUE),
(6, 'cryptocurrency', '数字货币', NULL, 1, '比特币、以太坊等数字货币', 'currency-icon', '#FF5722', 6, TRUE),
(7, 'policy', '政策法规', NULL, 1, '政府政策、法律法规相关', 'policy-icon', '#607D8B', 7, TRUE),
(8, 'technology', '科技创新', NULL, 1, '科技公司、创新技术相关', 'tech-icon', '#00BCD4', 8, TRUE),
-- 子分类示例
(9, 'central_bank', '央行', 1, 2, '各国央行和货币政策机构', 'bank-icon', '#1976D2', 1, TRUE),
(10, 'commercial_bank', '商业银行', 1, 2, '商业银行和金融服务机构', 'bank-icon', '#1976D2', 2, TRUE),
(11, 'tech_company', '科技公司', 2, 2, '科技类上市公司', 'tech-icon', '#388E3C', 1, TRUE),
(12, 'auto_company', '汽车公司', 2, 2, '汽车制造和相关公司', 'car-icon', '#388E3C', 2, TRUE);
```

### 1.3 classification_dimensions (分类维度表)

```sql
INSERT INTO classification_dimensions (id, dimension_name, display_name, description, is_active, sort_order) VALUES
(1, 'industry', '行业分类', '金融相关行业分类', TRUE, 1),
(2, 'sentiment', '情感倾向', '内容情感分析维度', TRUE, 2),
(3, 'urgency', '紧急程度', '事件紧急性等级', TRUE, 3),
(4, 'region', '地区分类', '地理区域分类', TRUE, 4),
(5, 'topic', '主题分类', '内容主题分类', TRUE, 5);
```

### 1.2 classification_values (分类值表)

```sql
INSERT INTO classification_values (id, dimension_id, value_code, display_name, description, parent_id, level, sort_order) VALUES
-- 行业分类
(1, 1, 'banking', '银行业', '银行及相关金融服务', NULL, 1, 1),
(2, 1, 'securities', '证券业', '证券交易及投资服务', NULL, 1, 2),
(3, 1, 'insurance', '保险业', '保险及再保险服务', NULL, 1, 3),
(4, 1, 'fintech', '金融科技', '金融科技公司及服务', NULL, 1, 4),
(5, 1, 'commercial_bank', '商业银行', '商业银行子分类', 1, 2, 1),
(6, 1, 'investment_bank', '投资银行', '投资银行子分类', 1, 2, 2),

-- 情感倾向
(7, 2, 'positive', '积极', '正面情感', NULL, 1, 1),
(8, 2, 'negative', '消极', '负面情感', NULL, 1, 2),
(9, 2, 'neutral', '中性', '中性情感', NULL, 1, 3),

-- 紧急程度
(10, 3, 'urgent', '紧急', '需要立即关注', NULL, 1, 1),
(11, 3, 'normal', '普通', '正常优先级', NULL, 1, 2),
(12, 3, 'low', '较低', '较低优先级', NULL, 1, 3),

-- 地区分类
(13, 4, 'asia_pacific', '亚太地区', '亚洲太平洋地区', NULL, 1, 1),
(14, 4, 'north_america', '北美地区', '北美洲地区', NULL, 1, 2),
(15, 4, 'europe', '欧洲地区', '欧洲地区', NULL, 1, 3),
(16, 4, 'china', '中国', '中华人民共和国', 13, 2, 1),
(17, 4, 'japan', '日本', '日本国', 13, 2, 2),
(18, 4, 'us', '美国', '美利坚合众国', 14, 2, 1),

-- 主题分类
(19, 5, 'macro_economy', '宏观经济', '宏观经济政策及数据', NULL, 1, 1),
(20, 5, 'monetary_policy', '货币政策', '央行货币政策', NULL, 1, 2),
(21, 5, 'market_analysis', '市场分析', '金融市场分析', NULL, 1, 3),
(22, 5, 'company_news', '公司新闻', '上市公司相关新闻', NULL, 1, 4);
```

### 1.4 tags (标签表)

```sql
INSERT INTO tags (id, tag_name, tag_code, tag_slug, parent_id, level, path, tag_type_id, tag_category_id, color, base_weight, popularity_weight, usage_count) VALUES
(1, '美联储', 'federal_reserve', 'federal-reserve', NULL, 1, 'federal_reserve', 2, 9, '#FF5722', 1.00, 0.95, 15420),
(2, '中国人民银行', 'pboc', 'peoples-bank-of-china', NULL, 1, 'pboc', 2, 9, '#2196F3', 1.00, 0.88, 12680),
(3, '利率', 'interest_rate', 'interest-rate', NULL, 1, 'interest_rate', 4, 3, '#4CAF50', 0.95, 0.92, 18750),
(4, '通胀', 'inflation', 'inflation', NULL, 1, 'inflation', 4, 3, '#FF9800', 0.90, 0.85, 14230),
(5, 'GDP', 'gdp', 'gdp', NULL, 1, 'gdp', 4, 3, '#9C27B0', 0.95, 0.90, 16890),
(6, '就业', 'employment', 'employment', NULL, 1, 'employment', 4, 3, '#607D8B', 0.85, 0.78, 11340),
(7, '非农数据', 'nonfarm_payrolls', 'nonfarm-payrolls', 6, 2, 'employment.nonfarm_payrolls', 3, 3, '#795548', 0.90, 0.85, 9870),
(8, '失业率', 'unemployment_rate', 'unemployment-rate', 6, 2, 'employment.unemployment_rate', 3, 3, '#F44336', 0.88, 0.82, 8950),
(9, '苹果公司', 'apple_inc', 'apple-inc', NULL, 1, 'apple_inc', 2, 11, '#000000', 0.92, 0.88, 13560),
(10, '特斯拉', 'tesla', 'tesla', NULL, 1, 'tesla', 2, 12, '#DC143C', 0.88, 0.91, 11240),
(11, '比特币', 'bitcoin', 'bitcoin', NULL, 1, 'bitcoin', 2, 6, '#F7931A', 0.85, 0.93, 19850),
(12, '股市', 'stock_market', 'stock-market', NULL, 1, 'stock_market', 4, 4, '#1976D2', 0.90, 0.87, 16740),
(13, '期货', 'futures', 'futures', NULL, 1, 'futures', 4, 4, '#388E3C', 0.88, 0.83, 12450),
(14, '外汇', 'forex', 'forex', NULL, 1, 'forex', 4, 4, '#7B1FA2', 0.92, 0.89, 14680),
(15, '新能源', 'new_energy', 'new-energy', NULL, 1, 'new_energy', 5, 5, '#4CAF50', 0.87, 0.85, 10230);
```

### 1.5 tag_relationships (标签关系表)

```sql
INSERT INTO tag_relationships (id, tag_id, related_tag_id, relationship_type, strength, confidence, source, usage_count) VALUES
(1, 1, 3, 'implies', 0.90, 0.95, 'manual', 156),
(2, 2, 3, 'implies', 0.85, 0.90, 'manual', 142),
(3, 7, 6, 'parent_child', 1.00, 1.00, 'manual', 89),
(4, 8, 6, 'parent_child', 1.00, 1.00, 'manual', 78),
(5, 11, 12, 'related', 0.75, 0.80, 'ml_inferred', 234),
(6, 9, 12, 'related', 0.80, 0.85, 'manual', 189),
(7, 10, 15, 'related', 0.85, 0.90, 'manual', 167),
(8, 4, 5, 'related', 0.70, 0.75, 'computed', 198),
(9, 3, 4, 'related', 0.65, 0.70, 'computed', 145),
(10, 14, 12, 'related', 0.60, 0.65, 'ml_inferred', 123);
```

## 2. 财经日历模块示例数据

### 2.1 countries_regions (国家地区表)

```sql
INSERT INTO countries_regions (id, country_code, country_name, country_name_en, region, flag_icon, time_zone, primary_currency, currency_symbol, global_importance_score, financial_market_weight) VALUES
(1, 'US', '美国', 'United States', 'North America', 'flag-us.png', 'America/New_York', 'USD', '$', 1.00, 1.00),
(2, 'CN', '中国', 'China', 'Asia Pacific', 'flag-cn.png', 'Asia/Shanghai', 'CNY', '¥', 0.95, 0.90),
(3, 'JP', '日本', 'Japan', 'Asia Pacific', 'flag-jp.png', 'Asia/Tokyo', 'JPY', '¥', 0.85, 0.80),
(4, 'GB', '英国', 'United Kingdom', 'Europe', 'flag-gb.png', 'Europe/London', 'GBP', '£', 0.80, 0.75),
(5, 'DE', '德国', 'Germany', 'Europe', 'flag-de.png', 'Europe/Berlin', 'EUR', '€', 0.78, 0.72),
(6, 'FR', '法国', 'France', 'Europe', 'flag-fr.png', 'Europe/Paris', 'EUR', '€', 0.75, 0.70),
(7, 'CA', '加拿大', 'Canada', 'North America', 'flag-ca.png', 'America/Toronto', 'CAD', 'C$', 0.70, 0.65),
(8, 'AU', '澳大利亚', 'Australia', 'Asia Pacific', 'flag-au.png', 'Australia/Sydney', 'AUD', 'A$', 0.68, 0.62),
(9, 'CH', '瑞士', 'Switzerland', 'Europe', 'flag-ch.png', 'Europe/Zurich', 'CHF', 'CHF', 0.72, 0.68),
(10, 'KR', '韩国', 'South Korea', 'Asia Pacific', 'flag-kr.png', 'Asia/Seoul', 'KRW', '₩', 0.65, 0.60);
```

### 2.2 event_types (事件类型表)

```sql
INSERT INTO event_types (id, type_code, type_name, type_name_en, category, subcategory, icon, color, default_importance, typical_market_impact) VALUES
(1, 'central_bank_rate', '央行利率决议', 'Central Bank Rate Decision', 'monetary_policy', 'interest_rate', 'bank-icon', '#FF5722', 3, 'high'),
(2, 'employment_data', '就业数据', 'Employment Data', 'economic_indicator', 'labor_market', 'employment-icon', '#2196F3', 3, 'high'),
(3, 'inflation_data', '通胀数据', 'Inflation Data', 'economic_indicator', 'price_level', 'chart-icon', '#FF9800', 3, 'high'),
(4, 'gdp_data', 'GDP数据', 'GDP Data', 'economic_indicator', 'growth', 'growth-icon', '#4CAF50', 3, 'high'),
(5, 'trade_balance', '贸易平衡', 'Trade Balance', 'economic_indicator', 'trade', 'trade-icon', '#9C27B0', 2, 'medium'),
(6, 'manufacturing_data', '制造业数据', 'Manufacturing Data', 'economic_indicator', 'production', 'factory-icon', '#607D8B', 2, 'medium'),
(7, 'retail_sales', '零售销售', 'Retail Sales', 'economic_indicator', 'consumption', 'shopping-icon', '#795548', 2, 'medium'),
(8, 'housing_data', '房地产数据', 'Housing Data', 'economic_indicator', 'real_estate', 'house-icon', '#F44336', 2, 'medium'),
(9, 'speech', '官员讲话', 'Official Speech', 'central_bank', 'communication', 'microphone-icon', '#3F51B5', 2, 'medium'),
(10, 'policy_meeting', '政策会议', 'Policy Meeting', 'central_bank', 'meeting', 'meeting-icon', '#009688', 2, 'medium');
```

### 2.3 economic_indicators (经济指标表)

```sql
INSERT INTO economic_indicators (id, indicator_code, indicator_name, indicator_name_en, country_id, event_type_id, unit, data_type, release_agency, release_frequency, market_importance) VALUES
(1, 'FFR', '联邦基金利率', 'Federal Funds Rate', 1, 1, '%', 'percentage', 'Federal Reserve', 'monthly', 3),
(2, 'NFP', '非农就业人数', 'Non-Farm Payrolls', 1, 2, '千人', 'integer', 'Bureau of Labor Statistics', 'monthly', 3),
(3, 'UNEMP', '失业率', 'Unemployment Rate', 1, 2, '%', 'percentage', 'Bureau of Labor Statistics', 'monthly', 3),
(4, 'CPI', '消费者价格指数', 'Consumer Price Index', 1, 3, '%', 'percentage', 'Bureau of Labor Statistics', 'monthly', 3),
(5, 'GDP_US', '国内生产总值', 'Gross Domestic Product', 1, 4, '%', 'percentage', 'Bureau of Economic Analysis', 'quarterly', 3),
(6, 'LPR_1Y', '贷款市场报价利率(1年期)', 'Loan Prime Rate 1Y', 2, 1, '%', 'percentage', '中国人民银行', 'monthly', 3),
(7, 'CPI_CN', '居民消费价格指数', 'Consumer Price Index China', 2, 3, '%', 'percentage', '国家统计局', 'monthly', 3),
(8, 'GDP_CN', '国内生产总值', 'GDP China', 2, 4, '%', 'percentage', '国家统计局', 'quarterly', 3),
(9, 'PMI_CN', '制造业采购经理指数', 'Manufacturing PMI China', 2, 6, '点', 'decimal', '国家统计局', 'monthly', 2),
(10, 'BOJ_RATE', '政策利率', 'Policy Rate Japan', 3, 1, '%', 'percentage', 'Bank of Japan', 'monthly', 3);
```

### 2.4 financial_events (财经事件表)

```sql
INSERT INTO financial_events (id, event_title, event_title_en, event_type_id, country_id, indicator_id, scheduled_time, actual_time, time_zone, time_precision, importance_level, market_impact, previous_value, forecast_value, actual_value, revised_value, value_unit, value_format, impact_direction, volatility_expected, actual_market_impact, event_description, impact_analysis, event_status, data_source, source_url, version, subscriber_count, notification_sent) VALUES
(1, '美联储利率决议', 'Fed Interest Rate Decision', 1, 1, 1, '2024-12-18 19:00:00', NULL, 'America/New_York', 'minute', 3, 'high', 5.25, 5.25, NULL, NULL, '%', 'percentage', NULL, 0.85, NULL, '美联储12月议息会议将决定联邦基金利率走向，市场普遍预期降息25个基点', '利率决议将直接影响美元走势、美股表现和全球资本流动，对金融市场具有重大影响', 'scheduled', 'Fed官网', 'https://www.federalreserve.gov/', 1, 15420, FALSE),
(2, '美国11月非农就业人数', 'US Nov Non-Farm Payrolls', 2, 1, 2, '2024-12-06 21:30:00', 3, 12000, 200000, '千人', 'published'),
(3, '美国11月失业率', 'US Nov Unemployment Rate', 2, 1, 3, '2024-12-06 21:30:00', 3, 4.1, 4.2, '%', 'published'),
(4, '美国11月CPI年率', 'US Nov CPI YoY', 3, 1, 4, '2024-12-11 21:30:00', 3, 2.6, 2.7, '%', 'scheduled'),
(5, '中国11月CPI年率', 'China Nov CPI YoY', 3, 2, 7, '2024-12-09 09:30:00', 3, 0.3, 0.5, '%', 'published'),
(6, '中国第三季度GDP年率', 'China Q3 GDP YoY', 4, 2, 8, '2024-10-18 10:00:00', 3, 5.3, 4.8, '%', 'published'),
(7, '中国12月LPR利率', 'China Dec LPR Rate', 1, 2, 6, '2024-12-20 09:15:00', 3, 3.10, 3.10, '%', 'scheduled'),
(8, '日本央行利率决议', 'BOJ Rate Decision', 1, 3, 10, '2024-12-19 11:00:00', 3, 0.25, 0.25, '%', 'scheduled'),
(9, '欧央行利率决议', 'ECB Rate Decision', 1, 5, NULL, '2024-12-12 20:45:00', 3, 3.25, 3.00, '%', 'published'),
(10, '英国11月CPI年率', 'UK Nov CPI YoY', 3, 4, NULL, '2024-12-18 15:00:00', 2, 2.3, 2.6, '%', 'scheduled');
```

## 3. 数据采集模块示例数据

### 3.1 data_sources (数据源表)

```sql
INSERT INTO data_sources (id, name, collection_method, content_category, base_url, description, crawl_mode, crawl_interval, priority, max_concurrent_tasks, use_proxy, request_delay_min, request_delay_max, status, health_score, supports_realtime, event_driven_config, last_crawl_time, next_crawl_time, total_crawled_count, total_success_count, consecutive_error_count, max_consecutive_errors, avg_response_time_ms, current_config_version, created_by, tags) VALUES
(1, '金十数据', 'web_scraping', 'financial_news', 'https://www.jin10.com', '专业的财经资讯平台，实时更新财经快讯', 'interval', 300, 8, 2, FALSE, 2, 5, 'active', 0.95, FALSE, '{}', '2024-12-20 09:01:23', '2024-12-20 09:06:23', 156789, 148950, 0, 5, 1250, 1, 'SYSTEM_AUTO', ARRAY['财经新闻', '快讯', '实时']),

(2, '新浪财经', 'web_scraping', 'financial_news', 'https://finance.sina.com.cn', '新浪财经频道，综合财经资讯', 'interval', 600, 7, 2, FALSE, 3, 8, 'active', 0.88, FALSE, '{}', '2024-12-20 09:06:45', '2024-12-20 09:16:45', 234567, 206789, 1, 10, 1580, 1, 'SYSTEM_AUTO', ARRAY['财经新闻', '市场分析']),

(3, '东方财富', 'web_scraping', 'financial_news', 'https://www.eastmoney.com', '东方财富网，专业金融信息服务', 'interval', 900, 6, 1, FALSE, 4, 10, 'active', 0.92, FALSE, '{}', '2024-12-20 09:15:06', '2024-12-20 09:30:06', 189345, 174221, 0, 8, 1420, 1, 'SYSTEM_AUTO', ARRAY['财经新闻', '股票', '基金']),

(4, '财联社', 'web_scraping', 'financial_news', 'https://www.cls.cn', '财联社快讯，专业财经新闻机构', 'interval', 180, 9, 3, FALSE, 1, 3, 'active', 0.97, FALSE, '{}', '2024-12-20 09:03:28', '2024-12-20 09:06:28', 98765, 95642, 0, 3, 890, 1, 'SYSTEM_AUTO', ARRAY['快讯', '财经新闻', '专业']),

(5, '华尔街见闻', 'web_scraping', 'financial_news', 'https://wallstreetcn.com', '华尔街见闻，全球财经资讯', 'interval', 420, 7, 2, FALSE, 3, 7, 'active', 0.89, FALSE, '{}', '2024-12-20 09:13:28', '2024-12-20 09:20:28', 145623, 129657, 2, 10, 1680, 1, 'SYSTEM_AUTO', ARRAY['全球财经', '深度分析']),

(6, '央行官网', 'web_scraping', 'official_data', 'http://www.pbc.gov.cn', '中国人民银行官方网站，权威货币政策发布', 'event_driven', 3600, 10, 1, FALSE, 5, 15, 'active', 0.99, TRUE, '{"trigger_before_minutes": 30, "trigger_after_minutes": 5, "max_retry_attempts": 3, "priority_boost": 5}', '2024-12-20 08:46:35', NULL, 8934, 8901, 0, 3, 2150, 1, 'SYSTEM_AUTO', ARRAY['央行', '货币政策', '官方数据']),

(7, 'Fed官网', 'web_scraping', 'official_data', 'https://www.federalreserve.gov', '美联储官方网站，美国货币政策权威发布', 'event_driven', 3600, 10, 1, FALSE, 8, 20, 'active', 0.98, TRUE, '{"trigger_before_minutes": 30, "trigger_after_minutes": 5, "max_retry_attempts": 3, "priority_boost": 5}', '2024-12-18 19:07:45', NULL, 7845, 7712, 0, 5, 2890, 1, 'SYSTEM_AUTO', ARRAY['美联储', '货币政策', '官方数据']),

(8, '国家统计局', 'web_scraping', 'official_data', 'http://www.stats.gov.cn', '国家统计局官方网站，经济数据权威发布', 'hybrid', 7200, 9, 1, FALSE, 5, 15, 'active', 0.96, TRUE, '{"trigger_before_minutes": 15, "trigger_after_minutes": 2, "priority_boost": 3}', '2024-12-20 06:03:45', '2024-12-20 12:03:45', 5632, 5456, 0, 5, 3250, 1, 'SYSTEM_AUTO', ARRAY['统计局', '经济数据', '官方发布']),



(9, 'Bloomberg', 'web_scraping', 'financial_news', 'https://www.bloomberg.com', '彭博财经新闻，国际财经资讯', 'interval', 600, 8, 2, TRUE, 5, 12, 'active', 0.91, FALSE, '{}', NULL, '2024-12-20 09:22:00', 345678, 314711, 1, 10, 2150, 1, 'SYSTEM_AUTO', ARRAY['国际财经', '彭博', '专业分析']),

(10, '劳工部官网', 'web_scraping', 'official_data', 'https://www.bls.gov', '美国劳工统计局官方网站，就业数据发布', 'event_driven', 7200, 9, 1, FALSE, 3, 8, 'active', 0.97, TRUE, '{"trigger_before_minutes": 0, "trigger_after_minutes": 2, "max_retry_attempts": 5, "priority_boost": 4}', '2024-12-06 21:33:55', NULL, 3421, 3387, 0, 3, 1890, 1, 'SYSTEM_AUTO', ARRAY['劳工部', '就业数据', '非农']),

(11, '欧央行官网', 'web_scraping', 'official_data', 'https://www.ecb.europa.eu', '欧洲央行官方网站，欧元区货币政策', 'event_driven', 3600, 10, 1, FALSE, 8, 18, 'active', 0.96, TRUE, '{"trigger_before_minutes": 45, "trigger_after_minutes": 10, "max_retry_attempts": 3, "priority_boost": 5}', '2024-12-12 20:56:41', NULL, 4567, 4398, 0, 5, 2560, 1, 'SYSTEM_AUTO', ARRAY['欧央行', '货币政策', '欧元区']),



(12, 'Reuters RSS', 'api_rss', 'financial_news', 'https://www.reuters.com/tools/rss', '路透社财经新闻RSS订阅，国际新闻', 'interval', 1800, 7, 1, FALSE, 5, 10, 'active', 0.89, FALSE, '{}', '2024-12-20 08:30:15', '2024-12-20 09:00:15', 123456, 112234, 1, 8, 1250, 1, 'SYSTEM_AUTO', ARRAY['RSS', '路透社', '国际新闻']),

(13, 'CNN Business RSS', 'api_rss', 'financial_news', 'https://rss.cnn.com/rss/money_news_international.rss', 'CNN商业新闻RSS订阅，美国商业资讯', 'interval', 3600, 5, 1, FALSE, 8, 15, 'active', 0.82, FALSE, '{}', '2024-12-20 07:45:20', '2024-12-20 08:45:20', 98765, 88987, 3, 10, 1680, 1, 'SYSTEM_AUTO', ARRAY['RSS', 'CNN', '商业新闻']);
```

### 3.2 event_driven_crawl_rules (事件驱动采集规则表)

```sql
INSERT INTO event_driven_crawl_rules (id, source_id, rule_name, trigger_type, trigger_config, advance_minutes, delay_minutes, repeat_interval_minutes, max_repeat_count, country_filter, importance_filter, event_type_filter, custom_task_config, priority_boost, is_active, trigger_count, success_count, last_triggered_at) VALUES
(1, 6, '央行利率决议采集', 'financial_event', '{"event_types": ["central_bank_rate"], "countries": ["CN"], "min_importance": 3}', 30, 5, 15, 4, ARRAY['CN'], ARRAY[3], ARRAY['central_bank_rate'], '{"extraction_rules": {"rate_selector": ".rate-info .current-rate", "announcement_selector": ".announcement-content"}}', 5, TRUE, 8, 7, '2024-12-20 09:15:00'),
(2, 7, '美联储利率决议采集', 'financial_event', '{"event_types": ["central_bank_rate"], "countries": ["US"], "min_importance": 3}', 30, 5, 10, 6, ARRAY['US'], ARRAY[3], ARRAY['central_bank_rate'], '{"extraction_rules": {"rate_selector": ".federal-funds-rate", "statement_selector": ".fomc-statement"}}', 5, TRUE, 12, 11, '2024-12-18 19:00:00'),
(3, 11, '非农就业数据采集', 'financial_event', '{"event_types": ["employment_data"], "indicators": ["NFP"], "countries": ["US"]}', 0, 2, 5, 3, ARRAY['US'], ARRAY[2, 3], ARRAY['employment_data'], '{"extraction_rules": {"nfp_selector": ".nonfarm-payrolls-data", "unemployment_selector": ".unemployment-rate"}}', 3, TRUE, 15, 14, '2024-12-06 21:30:00'),
(4, 8, '经济数据发布采集', 'financial_event', '{"event_types": ["gdp_data", "inflation_data"], "countries": ["CN"]}', 15, 3, 10, 2, ARRAY['CN'], ARRAY[2, 3], ARRAY['gdp_data', 'inflation_data'], '{"extraction_rules": {"data_selector": ".economic-data", "analysis_selector": ".data-analysis"}}', 2, TRUE, 6, 6, '2024-12-09 09:30:00'),
(5, 12, '欧央行决议采集', 'financial_event', '{"event_types": ["central_bank_rate"], "countries": ["DE", "FR"], "min_importance": 3}', 45, 10, 20, 3, ARRAY['DE', 'FR'], ARRAY[3], ARRAY['central_bank_rate'], '{"extraction_rules": {"rate_selector": ".ecb-rates", "press_conference": ".press-conference-link"}}', 4, TRUE, 5, 5, '2024-12-12 20:45:00'),
(6, 7, '美联储官员讲话采集', 'financial_event', '{"event_types": ["speech"], "countries": ["US"], "min_importance": 2}', 10, 5, NULL, 1, ARRAY['US'], ARRAY[2, 3], ARRAY['speech'], '{"extraction_rules": {"speech_content": ".speech-transcript", "key_points": ".speech-highlights"}}', 2, TRUE, 18, 16, '2024-12-19 15:30:00'),
(7, 8, '定时统计数据采集', 'time_based', '{"schedule": "0 10 15 * *", "timezone": "Asia/Shanghai", "description": "每月15日上午10点采集月度统计数据"}', 0, 0, NULL, 1, ARRAY['CN'], ARRAY[2, 3], ARRAY['gdp_data', 'inflation_data'], '{}', 1, TRUE, 3, 3, '2024-11-15 10:00:00'),
(8, 11, '就业报告定时采集', 'time_based', '{"schedule": "30 21 * * 5", "timezone": "America/New_York", "description": "每周五晚9:30采集就业报告"}', 0, 0, NULL, 1, ARRAY['US'], ARRAY[2, 3], ARRAY['employment_data'], '{}', 1, TRUE, 4, 4, '2024-12-06 21:30:00');
```

### 3.3 event_crawl_associations (事件采集关联表)

```sql
INSERT INTO event_crawl_associations (id, event_id, rule_id, source_id, scheduled_crawl_times, actual_crawl_times, association_status, crawl_results, created_at, updated_at) VALUES
(1, 1, 2, 7, ARRAY['2024-12-18 18:30:00', '2024-12-18 19:05:00', '2024-12-18 19:15:00'], ARRAY['2024-12-18 18:30:05', '2024-12-18 19:05:12'], 'active', '{"tasks_created": 2, "tasks_completed": 1, "success_rate": 0.5}', '2024-12-17 10:00:00', '2024-12-18 19:05:12'),
(2, 2, 3, 11, ARRAY['2024-12-06 21:32:00', '2024-12-06 21:37:00'], ARRAY['2024-12-06 21:32:08', '2024-12-06 21:37:15'], 'completed', '{"tasks_created": 2, "tasks_completed": 2, "success_rate": 1.0}', '2024-12-05 08:00:00', '2024-12-06 21:37:15'),
(3, 3, 3, 11, ARRAY['2024-12-06 21:32:00'], ARRAY['2024-12-06 21:32:08'], 'completed', '{"tasks_created": 1, "tasks_completed": 1, "success_rate": 1.0}', '2024-12-05 08:00:00', '2024-12-06 21:32:08'),
(4, 5, 4, 8, ARRAY['2024-12-09 09:45:00', '2024-12-09 09:33:00'], ARRAY['2024-12-09 09:33:05', '2024-12-09 09:45:12'], 'completed', '{"tasks_created": 2, "tasks_completed": 2, "success_rate": 1.0}', '2024-12-08 12:00:00', '2024-12-09 09:45:12'),
(5, 7, 1, 6, ARRAY['2024-12-20 08:45:00', '2024-12-20 09:25:00'], ARRAY['2024-12-20 08:45:03'], 'active', '{"tasks_created": 2, "tasks_completed": 1, "success_rate": 0.5}', '2024-12-19 14:00:00', '2024-12-20 08:45:03'),
(6, 8, 5, 12, ARRAY['2024-12-19 10:15:00', '2024-12-19 11:10:00'], ARRAY[], 'scheduled', '{"tasks_created": 2, "tasks_completed": 0, "success_rate": 0}', '2024-12-18 16:00:00', '2024-12-18 16:00:00'),
(7, 9, 5, 12, ARRAY['2024-12-12 19:00:00', '2024-12-12 20:55:00'], ARRAY['2024-12-12 19:00:15', '2024-12-12 20:55:08'], 'completed', '{"tasks_created": 2, "tasks_completed": 2, "success_rate": 1.0}', '2024-12-11 10:00:00', '2024-12-12 20:55:08');
```

### 3.4 event_crawl_scheduler_status (事件驱动采集调度器状态表)

```sql
INSERT INTO event_crawl_scheduler_status (id, scheduler_name, last_scan_time, next_scan_time, scan_interval_seconds, processed_events_count, generated_tasks_count, scheduler_status, last_error_message, error_count, created_at, updated_at) VALUES
(1, 'main_event_scheduler', '2024-12-20 09:15:00', '2024-12-20 09:20:00', 300, 1247, 3821, 'active', NULL, 0, '2024-12-01 00:00:00', '2024-12-20 09:15:00'),
(2, 'backup_event_scheduler', '2024-12-20 09:10:00', '2024-12-20 09:25:00', 900, 423, 1289, 'paused', 'Network timeout during event scan', 2, '2024-12-01 00:00:00', '2024-12-20 09:10:00'),
(3, 'financial_event_scheduler', '2024-12-20 09:12:00', '2024-12-20 09:17:00', 300, 856, 2567, 'active', NULL, 0, '2024-12-01 00:00:00', '2024-12-20 09:12:00');
```

### 3.5 crawl_tasks (采集任务表) - 增加事件驱动支持

```sql
INSERT INTO crawl_tasks (id, source_id, task_type, trigger_type, related_event_id, trigger_rule_id, target_url, scheduled_time, started_time, completed_time, status, progress, items_found, items_processed, items_success, duration_seconds) VALUES
-- 定时采集任务
(1, 1, 'news_list', 'interval', NULL, NULL, 'https://www.jin10.com/news', '2024-12-20 09:00:00', '2024-12-20 09:00:05', '2024-12-20 09:02:15', 'completed', 100, 45, 45, 43, 130),
(2, 2, 'news_list', 'interval', NULL, NULL, 'https://finance.sina.com.cn/roll/', '2024-12-20 09:05:00', '2024-12-20 09:05:08', '2024-12-20 09:07:22', 'completed', 100, 38, 38, 36, 134),
(3, 4, 'flash_news', 'interval', NULL, NULL, 'https://www.cls.cn/telegraph', '2024-12-20 09:03:00', '2024-12-20 09:03:02', '2024-12-20 09:03:45', 'completed', 100, 12, 12, 12, 43),
(4, 3, 'news_list', 'interval', NULL, NULL, 'https://www.eastmoney.com/news/', '2024-12-20 09:15:00', '2024-12-20 09:15:06', NULL, 'running', 75, 52, 39, 35, NULL),
(5, 5, 'analysis', 'interval', NULL, NULL, 'https://wallstreetcn.com/articles/today', '2024-12-20 09:10:00', '2024-12-20 09:10:15', '2024-12-20 09:13:28', 'completed', 100, 28, 28, 26, 193),

(7, 10, 'breaking_news', 'interval', NULL, NULL, 'https://www.bloomberg.com/markets', '2024-12-20 09:12:00', NULL, NULL, 'failed', 0, 0, 0, 0, NULL),

-- 事件驱动采集任务
(8, 7, 'financial_event', 'event', 1, 2, 'https://www.federalreserve.gov/newsevents/pressreleases/monetary/', '2024-12-18 18:30:00', '2024-12-18 18:30:05', '2024-12-18 18:32:18', 'completed', 100, 3, 3, 3, 133),
(9, 7, 'financial_event', 'event', 1, 2, 'https://www.federalreserve.gov/newsevents/pressreleases/monetary/', '2024-12-18 19:05:00', '2024-12-18 19:05:12', '2024-12-18 19:07:45', 'completed', 100, 2, 2, 2, 153),
(10, 11, 'financial_event', 'event', 2, 3, 'https://www.bls.gov/news.release/empsit.nr0.htm', '2024-12-06 21:32:00', '2024-12-06 21:32:08', '2024-12-06 21:33:55', 'completed', 100, 4, 4, 4, 107),
(11, 11, 'financial_event', 'event', 3, 3, 'https://www.bls.gov/news.release/empsit.nr0.htm', '2024-12-06 21:32:00', '2024-12-06 21:32:08', '2024-12-06 21:33:22', 'completed', 100, 2, 2, 2, 74),
(12, 8, 'financial_event', 'event', 5, 4, 'http://www.stats.gov.cn/sj/zxfb/', '2024-12-09 09:33:00', '2024-12-09 09:33:05', '2024-12-09 09:35:12', 'completed', 100, 3, 3, 3, 127),
(13, 6, 'financial_event', 'event', 7, 1, 'http://www.pbc.gov.cn/goutongjiaoliu/113456/113469/', '2024-12-20 08:45:00', '2024-12-20 08:45:03', '2024-12-20 08:46:35', 'completed', 100, 2, 2, 2, 92),
(14, 12, 'financial_event', 'event', 9, 5, 'https://www.ecb.europa.eu/press/pr/date/2024/html/', '2024-12-12 19:00:00', '2024-12-12 19:00:15', '2024-12-12 19:02:48', 'completed', 100, 5, 5, 5, 153),
(15, 12, 'financial_event', 'event', 9, 5, 'https://www.ecb.europa.eu/press/pr/date/2024/html/', '2024-12-12 20:55:00', '2024-12-12 20:55:08', '2024-12-12 20:56:41', 'completed', 100, 3, 3, 3, 93),

-- 手动触发任务
(16, 6, 'policy_data', 'manual', NULL, NULL, 'http://www.pbc.gov.cn/goutongjiaoliu/113456/113469/', '2024-12-20 08:00:00', '2024-12-20 08:00:12', '2024-12-20 08:02:45', 'completed', 100, 3, 3, 3, 153),
(17, 8, 'statistics', 'manual', NULL, NULL, 'http://www.stats.gov.cn/sj/', '2024-12-20 06:00:00', '2024-12-20 06:00:08', '2024-12-20 06:03:45', 'completed', 100, 8, 8, 8, 217),

-- 待执行的事件驱动任务
(18, 7, 'financial_event', 'event', 1, 2, 'https://www.federalreserve.gov/newsevents/pressreleases/monetary/', '2024-12-18 19:15:00', NULL, NULL, 'pending', 0, 0, 0, 0, NULL),
(19, 6, 'financial_event', 'event', 7, 1, 'http://www.pbc.gov.cn/goutongjiaoliu/113456/113469/', '2024-12-20 09:25:00', NULL, NULL, 'pending', 0, 0, 0, 0, NULL),
(20, 12, 'financial_event', 'event', 8, 5, 'https://www.ecb.europa.eu/press/pr/date/2024/html/', '2024-12-19 10:15:00', NULL, NULL, 'pending', 0, 0, 0, 0, NULL);
```

### 3.6 raw_data_records (原始数据记录表)

```sql
INSERT INTO raw_data_records (id, task_id, source_id, source_url, canonical_url, url_hash, url_domain, content_hash, content_simhash, content_length, content_encoding, title, author, publish_time, crawl_time, mongodb_id, mongodb_collection, content_type, processing_status, processing_priority, quality_score, category, subcategory, language, view_count, like_count, comment_count, share_count, retention_policy, archive_after_days, delete_after_days, version, parent_record_id) VALUES
(1, 1, 1, 'https://www.jin10.com/news/detail/20241220090001', 'https://www.jin10.com/news/detail/20241220090001', '7f8e9d0c1b2a3456789abcdef0*********0abcd', 'jin10.com', 'a1b2c3d4e5f6789abc123def456', *********0123, 856, 'utf-8', '美联储12月议息会议今日举行 市场预期降息25个基点', '金十数据', '2024-12-20 08:45:00', '2024-12-20 09:01:23', '67656f4b2c3a1b2d3e4f5678', 'raw_content', 'text/html', 'processed', 8, 0.92, 'monetary_policy', 'federal_reserve', 'zh', 15420, 234, 56, 12, 'standard', 365, 1095, 1, NULL),
(2, 2, 2, 'https://finance.sina.com.cn/money/nmoney/2024-12-20/doc-inc8912345.shtml', 'b2c3d4e5f6g7', '中国11月工业增加值同比增长5.4% 超预期', '新浪财经', '2024-12-20 08:30:00', '2024-12-20 09:06:45', '67656f4b2c3a1b2d3e4f5679', 'processed', 0.88, 'economic_data', 'zh', 8760),
(3, 3, 4, 'https://www.cls.cn/detail/1234567', 'c3d4e5f6g7h8', '【快讯】美股期货走高 道指期货涨超100点', '财联社', '2024-12-20 09:02:00', '2024-12-20 09:03:12', '67656f4b2c3a1b2d3e4f567a', 'processed', 0.85, 'market_news', 'zh', 6890),
(4, 4, 6, 'http://www.pbc.gov.cn/goutongjiaoliu/113456/113469/5678901/index.html', 'd4e5f6g7h8i9', '中国人民银行发布2024年第三季度货币政策执行报告', '中国人民银行', '2024-12-19 16:00:00', '2024-12-20 08:01:56', '67656f4b2c3a1b2d3e4f567b', 'processed', 0.95, 'official_policy', 'zh', 12340),
(5, 7, 5, 'https://wallstreetcn.com/articles/3789123', 'e5f6g7h8i9j0', '美联储官员：通胀仍是政策制定的关键考量因素', '华尔街见闻', '2024-12-20 07:15:00', '2024-12-20 09:11:34', '67656f4b2c3a1b2d3e4f567c', 'processing', 0.90, 'central_bank', 'zh', 9870),
(6, 8, 8, 'http://www.stats.gov.cn/sj/zxfb/202412/t20241216_1234567.html', 'f6g7h8i9j0k1', '2024年11月份国民经济主要指标数据发布', '国家统计局', '2024-12-16 10:00:00', '2024-12-20 06:02:15', '67656f4b2c3a1b2d3e4f567d', 'processed', 0.97, 'economic_data', 'zh', 18950),
(7, 9, 9, 'https://finance.yahoo.com/news/fed-meeting-rates-*********.html', 'g7h8i9j0k1l2', 'Federal Reserve Meeting: What to Expect from December Decision', 'Yahoo Finance', '2024-12-19 22:30:00', '2024-12-20 09:08:45', '67656f4b2c3a1b2d3e4f567e', 'processed', 0.83, 'monetary_policy', 'en', 5670),
(8, 1, 1, 'https://www.jin10.com/news/detail/**************', 'h8i9j0k1l2m3', '欧洲央行降息25个基点 符合市场预期', '金十数据', '2024-12-20 08:30:00', '2024-12-20 09:01:45', '67656f4b2c3a1b2d3e4f567f', 'processed', 0.89, 'monetary_policy', 'zh', 7890),
(9, 2, 2, 'https://finance.sina.com.cn/stock/usstock/c/2024-12-20/doc-inc8912346.shtml', 'i9j0k1l2m3n4', '特斯拉股价盘前涨超3% 市值重回1万亿美元', '新浪财经', '2024-12-20 08:00:00', '2024-12-20 09:07:12', '67656f4b2c3a1b2d3e4f5680', 'processed', 0.86, 'company_news', 'zh', 11230),
(10, 3, 4, 'https://www.cls.cn/detail/1234568', 'j0k1l2m3n4o5', '【快讯】比特币突破108000美元 创历史新高', '财联社', '2024-12-20 08:45:00', '2024-12-20 09:03:28', '67656f4b2c3a1b2d3e4f5681', 'processed', 0.91, 'cryptocurrency', 'zh', 16540);
```

### 3.7 content_tags (内容标签关联表)

```sql
INSERT INTO content_tags (id, content_id, tag_id, relevance_score, confidence_score, importance_score, extraction_method, extractor_name, mention_count) VALUES
(1, 1, 1, 0.95, 0.90, 0.85, 'auto', 'NLP_Extractor_v2.1', 3),
(2, 1, 3, 0.90, 0.85, 0.80, 'auto', 'NLP_Extractor_v2.1', 5),
(3, 2, 5, 0.88, 0.92, 0.75, 'auto', 'NLP_Extractor_v2.1', 2),
(4, 3, 12, 0.85, 0.80, 0.70, 'auto', 'NLP_Extractor_v2.1', 4),
(5, 4, 2, 0.95, 0.95, 0.90, 'manual', 'human_editor', 6),
(6, 5, 1, 0.90, 0.88, 0.80, 'auto', 'NLP_Extractor_v2.1', 2),
(7, 5, 4, 0.85, 0.83, 0.75, 'auto', 'NLP_Extractor_v2.1', 3),
(8, 6, 5, 0.92, 0.90, 0.85, 'auto', 'NLP_Extractor_v2.1', 4),
(9, 7, 1, 0.88, 0.85, 0.80, 'auto', 'NLP_Extractor_v2.1', 2),
(10, 8, 1, 0.90, 0.88, 0.82, 'auto', 'NLP_Extractor_v2.1', 3),
(11, 9, 9, 0.95, 0.92, 0.88, 'auto', 'NLP_Extractor_v2.1', 5),
(12, 9, 10, 0.85, 0.80, 0.75, 'auto', 'NLP_Extractor_v2.1', 2),
(13, 10, 11, 0.98, 0.95, 0.92, 'auto', 'NLP_Extractor_v2.1', 7),
(14, 2, 6, 0.80, 0.78, 0.70, 'auto', 'NLP_Extractor_v2.1', 1),
(15, 3, 14, 0.82, 0.79, 0.72, 'auto', 'NLP_Extractor_v2.1', 2);
```

### 3.8 data_source_configs (数据源配置表)

```sql
INSERT INTO data_source_configs (id, source_id, version, selector_config, headers_config, extraction_rules, validation_rules, javascript_config, anti_crawler_config, retry_config, is_active, is_validated, validation_result, change_reason, changed_by) VALUES
-- 网页解析类型配置
(1, 1, 1, 
 '{"title": ".news-title, h1", "content": ".news-content, .article-body", "time": ".news-time, .publish-time", "author": ".author-name"}',
 '{"Accept": "text/html,application/xhtml+xml", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}',
 '{"encoding": "utf-8", "timeout": 30}',
 '{"required_fields": ["title", "content"], "max_content_length": 50000}',
 '{"render_js": false, "wait_for_element": null}',
 '{"user_agent_rotation": true, "request_delay": true}',
 '{"max_retries": 3, "backoff_factor": 2}',
 TRUE, TRUE, '{"validation_passed": true, "test_date": "2024-12-20"}', 'Initial configuration for Jin10 News', 'SYSTEM_AUTO'),

(2, 2, 1,
 '{"title": ".main-title, h1", "content": ".article-content", "time": ".time", "author": ".author"}',
 '{"Accept": "text/html,application/xhtml+xml", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}',
 '{"encoding": "utf-8", "timeout": 30}',
 '{"required_fields": ["title", "content"], "max_content_length": 50000}',
 '{"render_js": false, "wait_for_element": null}',
 '{"user_agent_rotation": true, "request_delay": true}',
 '{"max_retries": 3, "backoff_factor": 2}',
 TRUE, TRUE, '{"validation_passed": true, "test_date": "2024-12-20"}', 'Initial configuration for Sina Finance', 'SYSTEM_AUTO'),

-- API JSON类型配置
(3, 9, 1, NULL,
 '{"Accept": "application/json", "Content-Type": "application/json", "User-Agent": "FinSight-Crawler/1.0"}',
 '{"response_format": "json", "data_path": "$.data", "title_field": "title", "content_field": "description", "time_field": "timestamp"}',
 '{"required_fields": ["symbol", "price"], "max_response_size": 1000000}',
 '{}',
 '{"rate_limiting": true, "respect_robots_txt": true}',
 '{"max_retries": 5, "backoff_factor": 1.5, "timeout": 30}',
 TRUE, TRUE, '{"validation_passed": true, "test_date": "2024-12-20", "api_response_valid": true}', 'Initial configuration for Yahoo Finance API', 'SYSTEM_AUTO'),

(4, 13, 1, NULL,
 '{"Accept": "application/json", "Content-Type": "application/json"}',
 '{"response_format": "json", "data_path": "$.Time Series (Daily)", "symbol_field": "Meta Data.2. Symbol"}',
 '{"required_fields": ["Meta Data", "Time Series (Daily)"], "max_response_size": 2000000}',
 '{"auth_type": "api_key", "api_key_header": "Authorization", "api_key_env": "ALPHA_VANTAGE_API_KEY", "api_key_prefix": "Bearer"}',
 TRUE, 'Initial configuration for Alpha Vantage API', 'SYSTEM_AUTO'),

(5, 14, 1, NULL,
 '{"Accept": "application/json", "Content-Type": "application/json"}',
 '{"response_format": "json", "data_path": "$", "price_field": "current_price", "symbol_field": "symbol"}',
 '{"required_fields": ["current_price", "market_cap"], "max_response_size": 500000}',
 '{"auth_type": "none", "note": "CoinGecko public API, no authentication required"}',
 TRUE, 'Initial configuration for CoinGecko API', 'SYSTEM_AUTO'),

-- RSS订阅类型配置
(6, 15, 1, NULL,
 '{"Accept": "application/rss+xml, application/xml, text/xml", "User-Agent": "FinSight-RSS-Reader/1.0"}',
 '{"format": "rss", "item_path": "//item", "title_field": "title", "content_field": "description", "time_field": "pubDate", "url_field": "link"}',
 '{"required_fields": ["title"], "max_items_per_feed": 100}',
 '{"auth_type": "none", "note": "Public RSS feed, no authentication required"}',
 TRUE, 'Initial configuration for Reuters RSS', 'SYSTEM_AUTO'),

(7, 16, 1, NULL,
 '{"Accept": "application/rss+xml, application/xml, text/xml", "User-Agent": "FinSight-RSS-Reader/1.0"}',
 '{"format": "rss", "item_path": "//item", "title_field": "title", "content_field": "description", "time_field": "pubDate"}',
 '{"required_fields": ["title"], "max_items_per_feed": 50}',
 '{"auth_type": "none", "note": "Public RSS feed, no authentication required"}',
 TRUE, 'Initial configuration for CNN Business RSS', 'SYSTEM_AUTO'),

-- 需要认证的官方数据源配置
(8, 6, 1,
 '{"title": ".article-title, h1", "content": ".article-content, .main-content", "time": ".pub-time"}',
 '{"Accept": "text/html,application/xhtml+xml", "User-Agent": "Mozilla/5.0 (compatible; FinSight-Bot/1.0)"}',
 '{"encoding": "utf-8", "timeout": 45}',
 '{"required_fields": ["title"], "max_content_length": 100000}',
 '{"auth_type": "basic_auth", "username_env": "PBOC_USERNAME", "password_env": "PBOC_PASSWORD", "note": "PBOC official website access"}',
 TRUE, 'Initial configuration for PBOC website', 'SYSTEM_AUTO'),

(9, 7, 1,
 '{"title": ".title, h1", "content": ".content, .press-release-content", "time": ".date"}',
 '{"Accept": "text/html,application/xhtml+xml", "User-Agent": "Mozilla/5.0 (compatible; FinSight-Bot/1.0)"}',
 '{"encoding": "utf-8", "timeout": 45}',
 '{"required_fields": ["title"], "max_content_length": 150000}',
 '{"auth_type": "custom_header", "custom_headers": {"X-API-Token": "FED_ACCESS_TOKEN"}, "token_env": "FED_ACCESS_TOKEN"}',
 TRUE, 'Initial configuration for Federal Reserve website', 'SYSTEM_AUTO');
```

### 3.9 data_source_credentials (数据源认证凭证表)

```sql
-- 注意：以下示例使用模拟的加密数据，实际使用时应使用真实的加密算法和密钥管理
INSERT INTO data_source_credentials (id, source_id, credential_type, encrypted_data, encryption_method, salt, is_active, expires_at, validation_status, last_validated) VALUES
-- API密钥类型认证
(1, 9, 'api_key', 
 decode('1A2B3C4D5E6F7A8B9C0D1E2F3A4B5C6D7E8F9A0B1C2D3E4F5A6B7C8D9E0F1A2B', 'hex'),
 'AES-256-GCM', 
 decode('ABCDEF*********0FEDCBA0987654321', 'hex'),
 TRUE, NULL, 'valid', '2024-12-20 08:00:00'),

(2, 13, 'api_key', 
 decode('2B3C4D5E6F7A8B9C0D1E2F3A4B5C6D7E8F9A0B1C2D3E4F5A6B7C8D9E0F1A2B3C', 'hex'),
 'AES-256-GCM', 
 decode('BCDEF*********0AFEDCBA0987654321B', 'hex'),
 TRUE, '2025-12-31 23:59:59', 'valid', '2024-12-20 08:00:00'),

-- 用户名密码类型认证
(3, 6, 'username_password', 
 decode('3C4D5E6F7A8B9C0D1E2F3A4B5C6D7E8F9A0B1C2D3E4F5A6B7C8D9E0F1A2B3C4D', 'hex'),
 'AES-256-GCM', 
 decode('CDEF*********0ABFEDCBA0987654321BC', 'hex'),
 TRUE, NULL, 'valid', '2024-12-20 07:30:00'),

(4, 8, 'username_password', 
 decode('4D5E6F7A8B9C0D1E2F3A4B5C6D7E8F9A0B1C2D3E4F5A6B7C8D9E0F1A2B3C4D5E', 'hex'),
 'AES-256-GCM', 
 decode('DEF*********0ABCFEDCBA0987654321BCD', 'hex'),
 TRUE, NULL, 'valid', '2024-12-20 07:30:00'),

-- OAuth令牌类型认证
(5, 7, 'oauth_token', 
 decode('5E6F7A8B9C0D1E2F3A4B5C6D7E8F9A0B1C2D3E4F5A6B7C8D9E0F1A2B3C4D5E6F', 'hex'),
 'AES-256-GCM', 
 decode('EF*********0ABCDFEDCBA0987654321BCDE', 'hex'),
 TRUE, '2024-12-31 23:59:59', 'valid', '2024-12-20 08:00:00'),

(6, 11, 'oauth_token', 
 decode('6F7A8B9C0D1E2F3A4B5C6D7E8F9A0B1C2D3E4F5A6B7C8D9E0F1A2B3C4D5E6F7A', 'hex'),
 'AES-256-GCM', 
 decode('F*********0ABCDEFEDCBA0987654321BCDEF', 'hex'),
 TRUE, '2024-12-31 23:59:59', 'valid', '2024-12-20 08:00:00'),

-- 证书认证类型
(7, 12, 'certificate', 
 decode('7A8B9C0D1E2F3A4B5C6D7E8F9A0B1C2D3E4F5A6B7C8D9E0F1A2B3C4D5E6F7A8B', 'hex'),
 'AES-256-GCM', 
 decode('*********0ABCDEFFEDCBA0987654321BCDEF1', 'hex'),
 TRUE, '2025-06-30 23:59:59', 'valid', '2024-12-20 08:00:00'),

-- 已过期的认证凭证示例
(8, 14, 'api_key', 
 decode('8B9C0D1E2F3A4B5C6D7E8F9A0B1C2D3E4F5A6B7C8D9E0F1A2B3C4D5E6F7A8B9C', 'hex'),
 'AES-256-GCM', 
 decode('234567890ABCDEF1FEDCBA0987654321BCDEF12', 'hex'),
 FALSE, '2024-11-30 23:59:59', 'expired', '2024-11-30 23:59:59'),

-- 验证失败的认证凭证示例
(9, 16, 'api_key', 
 decode('9C0D1E2F3A4B5C6D7E8F9A0B1C2D3E4F5A6B7C8D9E0F1A2B3C4D5E6F7A8B9C0D', 'hex'),
 'AES-256-GCM', 
 decode('34567890ABCDEF12FEDCBA0987654321BCDEF123', 'hex'),
 TRUE, NULL, 'invalid', '2024-12-19 15:30:00');
```

**认证凭证表说明：**

1. **加密存储**: 所有敏感信息都使用AES-256-GCM算法加密存储
2. **盐值管理**: 每个凭证都有独特的盐值增强安全性
3. **过期管理**: 支持凭证过期时间设置和验证状态跟踪
4. **类型支持**: 支持api_key、username_password、oauth_token、certificate等多种认证类型
5. **验证状态**: 记录凭证的验证状态（valid/invalid/expired/unknown）

### 3.10 content_classifications (内容分类关联表)

```sql
INSERT INTO content_classifications (id, content_id, dimension_id, value_id, confidence_score, source) VALUES
(1, 1, 2, 9, 0.85, 'auto'),        -- 美联储新闻 -> 中性情感
(2, 1, 3, 10, 0.90, 'auto'),       -- 美联储新闻 -> 普通紧急程度
(3, 1, 4, 18, 0.95, 'auto'),       -- 美联储新闻 -> 美国地区
(4, 1, 5, 20, 0.88, 'auto'),       -- 美联储新闻 -> 货币政策主题
(5, 2, 2, 7, 0.82, 'auto'),        -- 工业数据 -> 积极情感
(6, 2, 4, 16, 0.92, 'auto'),       -- 工业数据 -> 中国地区
(7, 2, 5, 19, 0.90, 'auto'),       -- 工业数据 -> 宏观经济主题
(8, 3, 2, 7, 0.80, 'auto'),        -- 美股期货 -> 积极情感
(9, 3, 3, 11, 0.85, 'auto'),       -- 美股期货 -> 普通紧急程度
(10, 3, 5, 21, 0.88, 'auto'),      -- 美股期货 -> 市场分析主题
(11, 4, 2, 9, 0.90, 'auto'),       -- 央行报告 -> 中性情感
(12, 4, 4, 16, 0.95, 'auto'),      -- 央行报告 -> 中国地区
(13, 4, 5, 20, 0.92, 'auto'),      -- 央行报告 -> 货币政策主题
(14, 9, 2, 7, 0.85, 'auto'),       -- 特斯拉新闻 -> 积极情感
(15, 9, 5, 22, 0.90, 'auto'),      -- 特斯拉新闻 -> 公司新闻主题
(16, 10, 2, 7, 0.88, 'auto'),      -- 比特币新闻 -> 积极情感
(17, 10, 3, 10, 0.82, 'auto'),     -- 比特币新闻 -> 普通紧急程度
(18, 5, 2, 9, 0.85, 'auto'),       -- 美联储官员讲话 -> 中性情感
(19, 6, 2, 9, 0.90, 'auto'),       -- 统计局数据 -> 中性情感
(20, 8, 2, 9, 0.88, 'auto');       -- 欧央行新闻 -> 中性情感
```

## 4. 财经事件关联数据

### 4.1 financial_event_tags (事件标签关联表)

```sql
INSERT INTO financial_event_tags (id, event_id, tag_id, relevance_score, confidence_score, source) VALUES
(1, 1, 1, 1.00, 0.95, 'manual'),   -- 美联储利率决议 -> 美联储标签
(2, 1, 3, 0.95, 0.90, 'manual'),   -- 美联储利率决议 -> 利率标签
(3, 2, 7, 1.00, 0.95, 'manual'),   -- 非农数据 -> 非农数据标签
(4, 2, 6, 0.90, 0.88, 'manual'),   -- 非农数据 -> 就业标签
(5, 3, 8, 1.00, 0.95, 'manual'),   -- 失业率 -> 失业率标签
(6, 3, 6, 0.90, 0.88, 'manual'),   -- 失业率 -> 就业标签
(7, 4, 4, 1.00, 0.95, 'manual'),   -- CPI -> 通胀标签
(8, 5, 4, 1.00, 0.95, 'manual'),   -- 中国CPI -> 通胀标签
(9, 5, 2, 0.85, 0.80, 'auto'),     -- 中国CPI -> 中国人民银行标签
(10, 6, 5, 1.00, 0.95, 'manual'),  -- 中国GDP -> GDP标签
(11, 7, 2, 1.00, 0.95, 'manual'),  -- 中国LPR -> 中国人民银行标签
(12, 7, 3, 0.90, 0.88, 'manual'),  -- 中国LPR -> 利率标签
(13, 8, 3, 0.95, 0.90, 'manual'),  -- 日本央行 -> 利率标签
(14, 9, 3, 0.95, 0.90, 'manual'),  -- 欧央行 -> 利率标签
(15, 10, 4, 1.00, 0.95, 'manual'); -- 英国CPI -> 通胀标签
```

### 4.2 financial_event_classifications (事件分类关联表)

```sql
INSERT INTO financial_event_classifications (id, event_id, dimension_id, value_id, confidence_score, source) VALUES
(1, 1, 3, 10, 0.95, 'auto'),       -- 美联储利率决议 -> 紧急程度：普通
(2, 1, 4, 18, 1.00, 'manual'),     -- 美联储利率决议 -> 地区：美国
(3, 1, 5, 20, 1.00, 'manual'),     -- 美联储利率决议 -> 主题：货币政策
(4, 2, 3, 10, 0.95, 'auto'),       -- 非农数据 -> 紧急程度：普通
(5, 2, 4, 18, 1.00, 'manual'),     -- 非农数据 -> 地区：美国
(6, 2, 5, 19, 0.95, 'auto'),       -- 非农数据 -> 主题：宏观经济
(7, 3, 4, 18, 1.00, 'manual'),     -- 失业率 -> 地区：美国
(8, 3, 5, 19, 0.95, 'auto'),       -- 失业率 -> 主题：宏观经济
(9, 4, 4, 18, 1.00, 'manual'),     -- 美国CPI -> 地区：美国
(10, 4, 5, 19, 0.95, 'auto'),      -- 美国CPI -> 主题：宏观经济
(11, 5, 4, 16, 1.00, 'manual'),    -- 中国CPI -> 地区：中国
(12, 5, 5, 19, 0.95, 'auto'),      -- 中国CPI -> 主题：宏观经济
(13, 6, 4, 16, 1.00, 'manual'),    -- 中国GDP -> 地区：中国
(14, 6, 5, 19, 1.00, 'manual'),    -- 中国GDP -> 主题：宏观经济
(15, 7, 4, 16, 1.00, 'manual'),    -- 中国LPR -> 地区：中国
(16, 7, 5, 20, 1.00, 'manual'),    -- 中国LPR -> 主题：货币政策
(17, 8, 4, 17, 1.00, 'manual'),    -- 日本央行 -> 地区：日本
(18, 8, 5, 20, 1.00, 'manual'),    -- 日本央行 -> 主题：货币政策
(19, 9, 4, 15, 1.00, 'manual'),    -- 欧央行 -> 地区：欧洲
(20, 9, 5, 20, 1.00, 'manual');    -- 欧央行 -> 主题：货币政策
```

### 4.3 event_affected_markets (事件影响市场表)

```sql
INSERT INTO event_affected_markets (id, event_id, market_type, impact_level, expected_volatility, actual_volatility) VALUES
(1, 1, 'USD', 'high', 0.85, NULL),         -- 美联储决议影响美元
(2, 1, 'US_STOCK', 'high', 0.80, NULL),    -- 美联储决议影响美股
(3, 1, 'US_BOND', 'high', 0.90, NULL),     -- 美联储决议影响美债
(4, 2, 'USD', 'high', 0.75, 0.68),         -- 非农数据影响美元
(5, 2, 'US_STOCK', 'medium', 0.60, 0.55),  -- 非农数据影响美股
(6, 3, 'USD', 'medium', 0.50, 0.45),       -- 失业率影响美元
(7, 5, 'CNY', 'medium', 0.40, 0.35),       -- 中国CPI影响人民币
(8, 5, 'CN_STOCK', 'medium', 0.45, 0.38),  -- 中国CPI影响A股
(9, 6, 'CNY', 'high', 0.70, 0.65),         -- 中国GDP影响人民币
(10, 6, 'CN_STOCK', 'high', 0.75, 0.72),   -- 中国GDP影响A股
(11, 7, 'CNY', 'medium', 0.50, NULL),      -- 中国LPR影响人民币
(12, 8, 'JPY', 'high', 0.80, NULL),        -- 日本央行影响日元
(13, 8, 'JP_STOCK', 'high', 0.75, NULL),   -- 日本央行影响日股
(14, 9, 'EUR', 'high', 0.85, 0.82),        -- 欧央行影响欧元
(15, 9, 'EU_STOCK', 'high', 0.70, 0.68);   -- 欧央行影响欧股
```

## 5. 用户画像示例数据

### 5.1 user_interest_tags (用户兴趣标签表)

```sql
INSERT INTO user_interest_tags (id, user_id, tag_id, explicit_interest, implicit_interest, last_reinforced_at, reinforcement_count, daily_decay_rate, click_count, view_time_seconds, share_count, source, confidence) VALUES
(1, 1001, 1, 0.90, 0.85, '2024-12-20 09:15:00', 45, 0.9990, 128, 15640, 12, 'behavior', 0.95),    -- 用户1001对美联储高度感兴趣
(2, 1001, 3, 0.85, 0.80, '2024-12-20 08:45:00', 38, 95, 12890, 8),      -- 用户1001对利率感兴趣
(3, 1001, 12, 0.75, 0.70, '2024-12-20 07:30:00', 22, 67, 8970, 5),      -- 用户1001对股市感兴趣
(4, 1002, 2, 0.95, 0.90, '2024-12-20 09:00:00', 52, 145, 18750, 15),    -- 用户1002对中国人民银行高度感兴趣
(5, 1002, 5, 0.88, 0.82, '2024-12-20 08:20:00', 31, 89, 11240, 7),      -- 用户1002对GDP感兴趣
(6, 1002, 4, 0.80, 0.75, '2024-12-20 07:45:00', 28, 76, 9680, 6),       -- 用户1002对通胀感兴趣
(7, 1003, 11, 0.92, 0.88, '2024-12-20 08:55:00', 67, 189, 22340, 23),   -- 用户1003对比特币高度感兴趣
(8, 1003, 9, 0.85, 0.78, '2024-12-20 08:10:00', 34, 98, 13560, 11),     -- 用户1003对苹果公司感兴趣
(9, 1003, 10, 0.82, 0.76, '2024-12-20 07:55:00', 29, 81, 10890, 9),     -- 用户1003对特斯拉感兴趣
(10, 1004, 14, 0.88, 0.83, '2024-12-20 08:35:00', 41, 112, 14230, 10),  -- 用户1004对外汇感兴趣
(11, 1004, 13, 0.75, 0.68, '2024-12-20 07:20:00', 19, 54, 7850, 4),     -- 用户1004对期货感兴趣
(12, 1005, 6, 0.90, 0.85, '2024-12-20 08:50:00', 48, 134, 16780, 13),   -- 用户1005对就业感兴趣
(13, 1005, 7, 0.85, 0.80, '2024-12-20 08:25:00', 35, 92, 12450, 8),     -- 用户1005对非农数据感兴趣
(14, 1005, 8, 0.80, 0.74, '2024-12-20 07:40:00', 26, 71, 9120, 5),      -- 用户1005对失业率感兴趣
(15, 1006, 15, 0.87, 0.81, '2024-12-20 08:40:00', 39, 107, 13890, 9);   -- 用户1006对新能源感兴趣
```

### 5.2 user_classification_preferences (用户分类偏好表)

```sql
INSERT INTO user_classification_preferences (id, user_id, dimension_id, value_id, preference_score, source, last_updated) VALUES
(1, 1001, 4, 18, 0.90, 'behavior', '2024-12-20 09:15:00'),   -- 用户1001偏好美国地区内容
(2, 1001, 5, 20, 0.85, 'behavior', '2024-12-20 09:10:00'),   -- 用户1001偏好货币政策主题
(3, 1001, 2, 9, 0.75, 'behavior', '2024-12-20 09:05:00'),    -- 用户1001偏好中性情感内容
(4, 1002, 4, 16, 0.95, 'behavior', '2024-12-20 09:00:00'),   -- 用户1002偏好中国地区内容
(5, 1002, 5, 19, 0.88, 'behavior', '2024-12-20 08:55:00'),   -- 用户1002偏好宏观经济主题
(6, 1002, 2, 7, 0.80, 'behavior', '2024-12-20 08:50:00'),    -- 用户1002偏好积极情感内容
(7, 1003, 5, 22, 0.90, 'behavior', '2024-12-20 08:55:00'),   -- 用户1003偏好公司新闻主题
(8, 1003, 2, 7, 0.85, 'behavior', '2024-12-20 08:45:00'),    -- 用户1003偏好积极情感内容
(9, 1003, 4, 18, 0.70, 'behavior', '2024-12-20 08:40:00'),   -- 用户1003对美国地区有一定偏好
(10, 1004, 5, 21, 0.92, 'behavior', '2024-12-20 08:35:00'),  -- 用户1004偏好市场分析主题
(11, 1004, 2, 9, 0.78, 'behavior', '2024-12-20 08:30:00'),   -- 用户1004偏好中性情感内容
(12, 1004, 4, 15, 0.75, 'behavior', '2024-12-20 08:25:00'),  -- 用户1004偏好欧洲地区内容
(13, 1005, 5, 19, 0.93, 'behavior', '2024-12-20 08:50:00'),  -- 用户1005偏好宏观经济主题
(14, 1005, 4, 18, 0.88, 'behavior', '2024-12-20 08:45:00'),  -- 用户1005偏好美国地区内容
(15, 1005, 2, 9, 0.82, 'behavior', '2024-12-20 08:40:00'),   -- 用户1005偏好中性情感内容
(16, 1006, 5, 22, 0.89, 'behavior', '2024-12-20 08:40:00'),  -- 用户1006偏好公司新闻主题
(17, 1006, 1, 4, 0.85, 'behavior', '2024-12-20 08:35:00'),   -- 用户1006偏好金融科技行业
(18, 1006, 2, 7, 0.80, 'behavior', '2024-12-20 08:30:00');   -- 用户1006偏好积极情感内容
```

### 5.3 user_profile_snapshots (用户画像快照表)

```sql
INSERT INTO user_profile_snapshots (id, user_id, snapshot_date, top_interests, interest_categories, behavioral_patterns, recommendation_weights) VALUES
(1, 1001, '2024-12-20', 
 '{"美联储": 0.90, "利率": 0.85, "股市": 0.75, "货币政策": 0.70, "通胀": 0.65}',
 '{"monetary_policy": 0.85, "market_analysis": 0.70, "macro_economy": 0.60}',
 '{"reading_time_avg": 180, "peak_hours": ["09:00-11:00", "15:00-17:00"], "preferred_content_length": "medium"}',
 '{"freshness": 0.3, "relevance": 0.4, "quality": 0.2, "popularity": 0.1}'
),
(2, 1002, '2024-12-20',
 '{"中国人民银行": 0.95, "GDP": 0.88, "通胀": 0.80, "宏观经济": 0.75, "利率": 0.70}',
 '{"macro_economy": 0.90, "monetary_policy": 0.80, "economic_data": 0.75}',
 '{"reading_time_avg": 240, "peak_hours": ["08:00-10:00", "14:00-16:00"], "preferred_content_length": "long"}',
 '{"freshness": 0.25, "relevance": 0.45, "quality": 0.25, "popularity": 0.05}'
),
(3, 1003, '2024-12-20',
 '{"比特币": 0.92, "苹果公司": 0.85, "特斯拉": 0.82, "公司新闻": 0.78, "新能源": 0.70}',
 '{"company_news": 0.85, "cryptocurrency": 0.90, "technology": 0.75}',
 '{"reading_time_avg": 120, "peak_hours": ["21:00-23:00", "07:00-09:00"], "preferred_content_length": "short"}',
 '{"freshness": 0.4, "relevance": 0.35, "quality": 0.15, "popularity": 0.1}'
),
(4, 1004, '2024-12-20',
 '{"外汇": 0.88, "期货": 0.75, "市场分析": 0.85, "欧洲": 0.70, "交易": 0.68}',
 '{"market_analysis": 0.90, "forex": 0.85, "derivatives": 0.70}',
 '{"reading_time_avg": 200, "peak_hours": ["16:00-18:00", "22:00-24:00"], "preferred_content_length": "medium"}',
 '{"freshness": 0.35, "relevance": 0.4, "quality": 0.2, "popularity": 0.05}'
),
(5, 1005, '2024-12-20',
 '{"就业": 0.90, "非农数据": 0.85, "失业率": 0.80, "宏观经济": 0.85, "美国": 0.75}',
 '{"macro_economy": 0.88, "employment": 0.90, "economic_indicators": 0.82}',
 '{"reading_time_avg": 160, "peak_hours": ["08:30-10:30", "21:30-22:30"], "preferred_content_length": "medium"}',
 '{"freshness": 0.3, "relevance": 0.4, "quality": 0.25, "popularity": 0.05}'
);
```

## 6. 数据关联性说明

### 6.1 标签与内容关联
- `content_tags` 表将新闻内容与标签关联，反映内容的主题属性
- `financial_event_tags` 表将财经事件与标签关联，实现事件标签化

### 6.2 分类与内容关联  
- `content_classifications` 表将内容按维度分类
- `financial_event_classifications` 表将财经事件按维度分类

### 6.3 用户画像关联
- `user_interest_tags` 表记录用户对标签的兴趣度
- `user_classification_preferences` 表记录用户对分类的偏好
- `user_profile_snapshots` 表存储用户画像的周期性快照

### 6.4 事件驱动采集关联
- `event_driven_crawl_rules` 表定义事件触发采集的规则
- `event_crawl_associations` 表关联财经事件与采集规则，跟踪执行状态
- `crawl_tasks` 表的事件驱动任务通过 `related_event_id` 和 `trigger_rule_id` 与财经事件关联
- `event_crawl_scheduler_status` 表监控事件驱动调度器的运行状态

### 6.5 事件驱动采集示例场景

#### 6.5.1 美联储利率决议采集流程
1. **事件录入**: financial_events 表中录入美联储利率决议事件 (id=1)
2. **规则匹配**: event_driven_crawl_rules 表中的规则 (id=2) 匹配该事件
3. **关联创建**: event_crawl_associations 表创建关联记录 (id=1)
4. **任务生成**: 系统生成多个 crawl_tasks (id=8,9,18) 在不同时间点采集
5. **数据采集**: 采集到的数据存入 raw_data_records 表

#### 6.5.2 非农就业数据采集流程
1. **事件录入**: 录入非农就业数据发布事件 (id=2,3)
2. **规则触发**: 规则 (id=3) 匹配就业数据类型事件
3. **即时采集**: 在数据发布时刻立即开始采集任务 (id=10,11)
4. **数据处理**: 采集的就业数据进入处理流程

### 6.6 数据一致性检查

这些示例数据具有以下特点：
1. **ID关联性**: 所有外键关系正确，确保数据完整性
2. **时间逻辑性**: 时间戳符合逻辑顺序，事件时间与采集时间保持合理关系
3. **业务真实性**: 使用真实的财经新闻、事件和数据
4. **权重合理性**: 各种评分和权重在合理范围内
5. **标签一致性**: 标签在不同模块间保持一致使用
6. **事件驱动一致性**: 事件驱动采集的时间安排符合实际业务需求

### 6.7 测试数据覆盖范围

#### 6.7.1 采集模式覆盖
- **定时采集**: 新闻网站、市场数据等常规采集
- **事件驱动**: 央行决议、经济数据发布等精确时点采集
- **混合模式**: 统计局网站既定时采集又响应重要数据发布

#### 6.7.2 触发类型覆盖
- **financial_event**: 基于财经事件的触发
- **time_based**: 基于时间的定时触发  
- **manual**: 手动触发的任务

#### 6.7.3 数据源类型覆盖
- **官方数据源**: 央行、统计局等权威机构
- **财经媒体**: 专业财经新闻平台
- **市场数据**: 股票、外汇等交易数据

这套示例数据可以直接用于系统测试和功能验证，覆盖了事件驱动采集的各种应用场景。

## 7. 认证管理系统说明

### 7.1 认证管理架构

FinSight系统采用双重认证管理架构：

1. **数据库加密存储** (`data_source_credentials`表): 用于存储长期稳定的认证凭证，如API密钥、证书等
2. **环境变量配置**: 用于开发测试环境和临时配置，便于快速调试和部署

### 7.2 认证凭证使用流程

```mermaid
graph TD
    A[采集任务启动] --> B{检查认证类型}
    B -->|需要认证| C[查询data_source_credentials表]
    C --> D[解密凭证数据]
    D --> E[使用凭证进行请求]
    B -->|无需认证| F[直接发起请求]
    E --> G[记录验证状态]
    F --> H[完成采集]
    G --> H
```

### 7.3 环境变量配置示例

以下环境变量主要用于开发测试环境：

### 7.4 API密钥类环境变量

```bash
# Yahoo Finance API
YAHOO_FINANCE_API_KEY=your_yahoo_finance_api_key_here

# Alpha Vantage API  
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key_here

# 通用API密钥模板
API_SOURCE_KEY=bearer_token_value_here
```

### 7.5 基础认证类环境变量

```bash
# 央行官网访问
PBOC_USERNAME=your_pboc_username
PBOC_PASSWORD=your_pboc_password

# 其他官方网站访问
FED_USERNAME=your_fed_username  
FED_PASSWORD=your_fed_password
```

### 7.6 自定义头认证类环境变量

```bash
# 美联储网站访问令牌
FED_ACCESS_TOKEN=your_fed_access_token

# 欧央行数据访问
ECB_API_TOKEN=your_ecb_api_token

# 其他自定义认证头
CUSTOM_AUTH_HEADER=your_custom_auth_value
```

### 7.7 Docker Compose 环境变量配置

```yaml
# docker-compose.yml
version: '3.8'
services:
  finsight-backend:
    image: finsight/backend:latest
    environment:
      # API认证
      - YAHOO_FINANCE_API_KEY=${YAHOO_FINANCE_API_KEY}
      - ALPHA_VANTAGE_API_KEY=${ALPHA_VANTAGE_API_KEY}
      - COINGECKO_API_KEY=${COINGECKO_API_KEY}
      
      # 官方网站认证
      - PBOC_USERNAME=${PBOC_USERNAME}
      - PBOC_PASSWORD=${PBOC_PASSWORD}
      - FED_ACCESS_TOKEN=${FED_ACCESS_TOKEN}
      
      # RSS订阅认证（如需要）
      - REUTERS_API_KEY=${REUTERS_API_KEY}
      
      # 数据库配置
      - DATABASE_URL=${DATABASE_URL}
      - MONGODB_URI=${MONGODB_URI}
    env_file:
      - .env.production
```

### 7.8 Kubernetes Secrets 配置

```yaml
# k8s-secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: finsight-api-keys
type: Opaque
stringData:
  yahoo-finance-api-key: "your_yahoo_finance_api_key"
  alpha-vantage-api-key: "your_alpha_vantage_api_key"
  pboc-username: "your_pboc_username"  
  pboc-password: "your_pboc_password"
  fed-access-token: "your_fed_access_token"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: finsight-backend
spec:
  template:
    spec:
      containers:
      - name: backend
        image: finsight/backend:latest
        env:
        - name: YAHOO_FINANCE_API_KEY
          valueFrom:
            secretKeyRef:
              name: finsight-api-keys
              key: yahoo-finance-api-key
        - name: ALPHA_VANTAGE_API_KEY
          valueFrom:
            secretKeyRef:
              name: finsight-api-keys
              key: alpha-vantage-api-key
        - name: PBOC_USERNAME
          valueFrom:
            secretKeyRef:
              name: finsight-api-keys
              key: pboc-username
        - name: PBOC_PASSWORD
          valueFrom:
            secretKeyRef:
              name: finsight-api-keys
              key: pboc-password
```

### 7.9 开发环境配置示例

```bash
# .env.develop
# API服务密钥
YAHOO_FINANCE_API_KEY=demo_api_key_for_testing
ALPHA_VANTAGE_API_KEY=demo_alpha_vantage_key  
COINGECKO_API_KEY=  # 公开API，无需密钥

# 官方网站认证（测试环境使用模拟值）
PBOC_USERNAME=test_user
PBOC_PASSWORD=test_password
FED_ACCESS_TOKEN=test_token_fed

# RSS订阅认证
REUTERS_API_KEY=  # 公开RSS，无需密钥
CNN_API_KEY=      # 公开RSS，无需密钥

# 数据库连接
DATABASE_URL=postgresql://username:password@localhost:5432/finsight_dev
MONGODB_URI=mongodb://localhost:27017/finsight_content_dev

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 日志级别
LOG_LEVEL=DEBUG
```

### 7.10 生产环境安全注意事项

1. **密钥轮换**: 定期轮换API密钥和访问令牌
2. **权限最小化**: 只授予必要的API访问权限
3. **加密存储**: 使用云服务的密钥管理系统
4. **访问审计**: 记录密钥使用情况和API调用日志
5. **监控告警**: 监控API配额使用和异常访问

### 7.11 认证配置映射关系

| 数据源ID | 数据源名称 | 认证类型 | 环境变量 | 描述 |
|---------|-----------|---------|----------|------|
| 9 | Yahoo Finance API | api_key | YAHOO_FINANCE_API_KEY | Yahoo财经API密钥 |
| 13 | Alpha Vantage API | api_key | ALPHA_VANTAGE_API_KEY | Alpha Vantage API密钥 |
| 14 | CoinGecko API | none | - | 公开API，无需认证 |
| 15 | Reuters RSS | none | - | 公开RSS，无需认证 |
| 16 | CNN Business RSS | none | - | 公开RSS，无需认证 |
| 6 | 央行官网 | basic_auth | PBOC_USERNAME, PBOC_PASSWORD | 央行网站用户名密码 |
| 7 | Fed官网 | custom_header | FED_ACCESS_TOKEN | 美联储访问令牌 |

### 7.12 认证管理最佳实践

#### 7.12.1 生产环境推荐方案

**优先级顺序**：
1. **数据库加密存储** - 用于长期稳定的生产环境凭证
2. **云服务密钥管理** - AWS Secrets Manager、Azure Key Vault等
3. **环境变量** - 仅用于开发测试环境

#### 7.12.2 认证凭证生命周期管理

```sql
-- 认证凭证状态查询
SELECT 
    ds.name as data_source_name,
    dsc.credential_type,
    dsc.validation_status,
    dsc.expires_at,
    dsc.last_validated,
    CASE 
        WHEN dsc.expires_at IS NULL THEN 'permanent'
        WHEN dsc.expires_at > NOW() THEN 'valid'
        ELSE 'expired'
    END as current_status
FROM data_source_credentials dsc
JOIN data_sources ds ON dsc.source_id = ds.id
WHERE dsc.is_active = TRUE
ORDER BY dsc.expires_at ASC NULLS LAST;

-- 即将过期的凭证监控
SELECT 
    ds.name,
    dsc.credential_type,
    dsc.expires_at,
    (dsc.expires_at - NOW()) as time_until_expiry
FROM data_source_credentials dsc
JOIN data_sources ds ON dsc.source_id = ds.id
WHERE dsc.is_active = TRUE 
  AND dsc.expires_at IS NOT NULL
  AND dsc.expires_at < NOW() + INTERVAL '30 days'
ORDER BY dsc.expires_at ASC;
```

#### 7.12.3 安全策略建议

1. **加密算法**: 使用AES-256-GCM或更强的加密算法
2. **密钥管理**: 加密密钥与应用代码分离存储
3. **访问控制**: 最小权限原则，限制凭证访问权限
4. **审计日志**: 记录所有凭证使用和修改操作
5. **定期轮换**: 建立凭证定期轮换机制

#### 7.12.4 认证配置优先级

当系统中同时存在多种认证配置时，优先级顺序为：

1. `data_source_credentials`表中的加密凭证（最高优先级）
2. `data_source_configs`表中的`auth_config`配置
3. 环境变量配置（最低优先级，仅开发环境）

通过这种分层的认证管理方式，可以确保生产环境的安全性，同时为开发测试提供灵活性。

## 8. 字段更新对照表

### 8.1 主要表字段变更对照

| 表名 | 原字段 | 新字段/更新 | 说明 |
|------|--------|-------------|------|
| data_sources | source_type | collection_method + content_category | 拆分为技术维度和业务维度 |
| data_sources | - | max_concurrent_tasks, use_proxy, request_delay_min/max | 新增并发控制和反爬虫配置 |
| data_sources | - | consecutive_error_count, avg_response_time_ms | 新增错误统计和性能监控 |
| data_sources | - | current_config_version, created_by, tags | 新增版本管理和标签支持 |
| raw_data_records | - | canonical_url, url_domain, content_hash | 新增URL标准化和内容哈希 |
| raw_data_records | - | content_simhash, content_length, content_encoding | 新增内容相似性和编码信息 |
| raw_data_records | - | subcategory, social_metrics (view/like/comment/share_count) | 新增子分类和社交媒体统计 |
| raw_data_records | - | retention_policy, archive_after_days, delete_after_days | 新增数据生命周期管理 |
| financial_events | - | actual_time, time_zone, time_precision | 新增时间精确度管理 |
| financial_events | - | market_impact, actual_value, revised_value | 新增市场影响和数据修正 |
| financial_events | - | impact_direction, volatility_expected | 新增影响方向和波动性预期 |
| financial_events | - | event_description, impact_analysis | 新增事件描述和影响分析 |
| financial_events | - | data_source, source_url, version | 新增数据来源和版本管理 |
| financial_events | - | subscriber_count, notification_sent | 新增订阅和通知功能 |
| user_interest_tags | - | daily_decay_rate, source, confidence | 新增兴趣衰减和来源追踪 |
| data_source_configs | auth_config | javascript_config, anti_crawler_config, retry_config | 配置细分和功能增强 |
| data_source_configs | - | is_validated, validation_result | 新增配置验证状态 |

### 8.2 新增表结构

| 表名 | 用途 | 关键字段 |
|------|------|----------|
| event_driven_crawl_rules | 事件驱动采集规则 | trigger_type, trigger_config, advance_minutes |
| event_crawl_associations | 事件采集关联 | event_id, rule_id, scheduled_crawl_times |
| event_crawl_scheduler_status | 采集调度器状态 | scheduler_name, last_scan_time, scheduler_status |
| data_source_credentials | 认证凭证管理 | credential_type, encrypted_data, validation_status |

### 8.3 字段类型和约束更新

| 表.字段 | 原类型 | 新类型 | 约束变更 |
|---------|--------|--------|----------|
| data_sources.collection_method | - | VARCHAR(30) NOT NULL | 新增枚举约束 |
| data_sources.content_category | - | VARCHAR(30) NOT NULL | 新增枚举约束 |
| data_sources.crawl_mode | - | VARCHAR(20) DEFAULT 'interval' | 新增模式约束 |
| raw_data_records.content_simhash | - | BIGINT | 新增SimHash支持 |
| raw_data_records.retention_policy | - | VARCHAR(20) DEFAULT 'standard' | 新增生命周期策略 |
| financial_events.time_precision | - | VARCHAR(10) DEFAULT 'minute' | 新增时间精度控制 |
| financial_events.value_format | - | VARCHAR(20) DEFAULT 'decimal' | 新增数值格式控制 |

### 8.4 索引更新建议

根据新增字段，建议添加以下索引：

```sql
-- 数据源采集模式和状态索引
CREATE INDEX idx_data_sources_method_category ON data_sources(collection_method, content_category, status);

-- 原始数据内容哈希去重索引
CREATE INDEX idx_raw_data_content_hash ON raw_data_records(content_hash) WHERE processing_status = 'processed';

-- 财经事件时区和精度索引
CREATE INDEX idx_financial_events_timezone ON financial_events(time_zone, scheduled_time);

-- 事件驱动规则触发类型索引
CREATE INDEX idx_event_rules_trigger ON event_driven_crawl_rules(trigger_type, is_active);

-- 用户兴趣来源和置信度索引
CREATE INDEX idx_user_interests_source ON user_interest_tags(source, confidence DESC);
```

### 8.5 数据迁移注意事项

1. **source_type拆分**: 需要运行数据迁移脚本将现有的source_type值正确拆分到collection_method和content_category
2. **时间字段**: 确保所有时间字段正确设置时区信息
3. **认证数据**: 敏感认证信息需要使用适当的加密算法进行加密存储
4. **索引重建**: 添加新索引可能需要较长时间，建议在维护窗口执行
5. **应用程序适配**: 相关的API和服务需要同步更新以支持新字段结构

通过这次全面的字段更新，FinSight系统的数据库设计更加完善，支持更丰富的功能和更精细的数据管理。