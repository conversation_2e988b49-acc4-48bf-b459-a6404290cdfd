# 标签分类系统使用指南

## 概述

标签分类系统是一个完整的标签管理、用户画像分析和内容分类解决方案，支持动态权重计算、多维度分类和智能推荐功能。

## 核心功能

### 1. 标签管理
- 标签的创建、更新、删除和查询
- 层次化标签结构支持
- 动态权重系统（基础权重、热度权重、质量权重、时效权重）
- 标签关系管理（同义词、父子关系、相关性等）

### 2. 用户画像
- 用户兴趣标签跟踪
- 行为统计分析
- 兴趣衰减算法
- 用户画像快照生成

### 3. 内容分类
- 内容标签关联
- 多维度分类系统
- 自动分类和人工标注支持
- 分类质量评估

## API路由器

标签分类系统提供了完整的REST API接口，包含以下主要端点：

### 路由器信息
- **路径前缀**: `/api/v1/tags`
- **标签**: `标签分类系统`
- **端点数量**: 25个

### 主要API端点分类

#### 1. 标签类型管理
- `POST /api/v1/tags/types` - 创建标签类型
- `GET /api/v1/tags/types` - 获取标签类型列表
- `GET /api/v1/tags/types/{type_id}` - 获取标签类型详情

#### 2. 标签分类管理
- `POST /api/v1/tags/categories` - 创建标签分类
- `GET /api/v1/tags/categories` - 获取标签分类列表

#### 3. 标签管理
- `POST /api/v1/tags` - 创建标签
- `GET /api/v1/tags` - 搜索标签
- `GET /api/v1/tags/{tag_id}` - 获取标签详情
- `PUT /api/v1/tags/{tag_id}` - 更新标签
- `DELETE /api/v1/tags/{tag_id}` - 删除标签

#### 4. 标签关系管理
- `POST /api/v1/tags/{tag_id}/relationships` - 创建标签关系
- `GET /api/v1/tags/{tag_id}/relationships` - 获取标签关系列表

#### 5. 权重和统计
- `PUT /api/v1/tags/{tag_id}/weight` - 更新标签权重
- `GET /api/v1/tags/{tag_id}/stats` - 获取标签统计信息

#### 6. 分类系统
- `POST /api/v1/tags/classifications/dimensions` - 创建分类维度
- `GET /api/v1/tags/classifications/dimensions` - 获取分类维度列表
- `POST /api/v1/tags/classifications/values` - 创建分类值
- `GET /api/v1/tags/classifications/dimensions/{dimension_id}/values` - 获取分类值列表

#### 7. 用户画像
- `POST /api/v1/tags/user-interests` - 添加用户兴趣标签
- `GET /api/v1/tags/user-interests` - 获取用户兴趣标签列表
- `POST /api/v1/tags/user-behaviors/{action}` - 记录用户行为
- `GET /api/v1/tags/user-profile` - 获取用户画像
- `POST /api/v1/tags/user-profile/snapshot` - 生成用户画像快照

#### 8. 系统管理
- `POST /api/v1/tags/system/update-weights` - 批量更新标签权重
- `POST /api/v1/tags/system/cleanup-inactive` - 清理非活跃标签

## 基本使用示例

### 1. 编程方式使用服务

```python
from src.services.tag_classification_service import TagService, UserProfileService
from src.core.database import get_db

# 获取数据库会话
db = next(get_db())

# 初始化服务
tag_service = TagService(db)
user_profile_service = UserProfileService(db)

# 创建标签类型
tag_type = await tag_service.create_tag_type({
    "type_code": "industry",
    "type_name": "行业标签",
    "description": "行业相关的标签类型"
})

# 创建标签
tag = await tag_service.create_tag({
    "tag_name": "人工智能",
    "tag_code": "artificial_intelligence",
    "tag_slug": "artificial-intelligence",
    "tag_type_id": tag_type.id,
    "description": "人工智能相关内容",
    "base_weight": 0.8
})

# 记录用户兴趣
await user_profile_service.record_user_behavior(
    user_id=123,
    tag_id=tag.id,
    action="click"
)

# 生成用户画像快照
snapshot = await user_profile_service.generate_profile_snapshot(user_id=123)

db.close()
```

## 数据模型概览

### 核心表结构

1. **标签类型表 (tag_types)**
   - 定义标签的基本类型（通用、实体、关键词等）

2. **标签分类表 (tag_categories)**
   - 定义标签的业务分类，支持层次结构

3. **标签表 (tags)**
   - 系统核心表，管理所有标签信息
   - 支持动态权重计算和生命周期管理

4. **标签关系表 (tag_relationships)**
   - 定义标签间的各种关系

5. **用户兴趣标签表 (user_interest_tags)**
   - 跟踪用户对标签的兴趣程度

6. **用户画像快照表 (user_profile_snapshots)**
   - 保存用户画像的定期快照

## 权重系统

标签系统采用多维度动态权重计算：

- **基础权重 (base_weight)**: 标签的基础重要程度
- **热度权重 (popularity_weight)**: 基于使用频率的动态权重
- **质量权重 (quality_weight)**: 基于用户反馈的质量评估
- **时效权重 (temporal_weight)**: 基于时间相关性的权重

综合权重计算公式：
```
computed_weight = base_weight * 0.4 + popularity_weight * 0.3 + 
                 quality_weight * 0.2 + temporal_weight * 0.1
```

## 服务类说明

### TagService
- 标签的 CRUD 操作
- 标签搜索和过滤
- 权重管理和统计更新

### UserProfileService
- 用户兴趣管理
- 行为记录和分析
- 画像快照生成

### ClassificationService
- 分类维度管理
- 分类值的层次结构处理

## 集成说明

该系统设计为与现有服务无缝集成：

1. **与用户服务集成**: 复用现有用户数据，扩展用户画像功能
2. **与内容服务集成**: 为内容添加智能标签和分类
3. **与推荐服务集成**: 提供用户画像数据支持个性化推荐
4. **与搜索服务集成**: 支持基于标签的智能搜索

### 2. HTTP API使用示例

#### 启动开发服务器

```bash
# 激活虚拟环境
source venv/finsight/bin/activate

# 启动开发服务器
uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload
```

#### API文档访问

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

#### 使用cURL示例

```bash
# 1. 获取标签类型列表
curl -X GET "http://localhost:8000/api/v1/tags/types" \
     -H "accept: application/json"

# 2. 创建标签类型（需要认证）
curl -X POST "http://localhost:8000/api/v1/tags/types" \
     -H "accept: application/json" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{
       "type_code": "finance",
       "type_name": "金融标签",
       "description": "金融相关的标签类型",
       "color": "#1E88E5"
     }'

# 3. 搜索标签
curl -X GET "http://localhost:8000/api/v1/tags?keyword=股票&page=1&size=20" \
     -H "accept: application/json"

# 4. 创建标签（需要认证）
curl -X POST "http://localhost:8000/api/v1/tags" \
     -H "accept: application/json" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{
       "tag_name": "A股",
       "tag_code": "a_share",
       "tag_slug": "a-share",
       "tag_type_id": 1,
       "description": "中国A股市场相关内容"
     }'

# 5. 获取用户兴趣标签（需要认证）
curl -X GET "http://localhost:8000/api/v1/tags/user-interests" \
     -H "accept: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN"

# 6. 记录用户行为（需要认证）
curl -X POST "http://localhost:8000/api/v1/tags/user-behaviors/click?tag_id=1" \
     -H "accept: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

#### 使用Python requests

```python
import requests

BASE_URL = "http://localhost:8000"
headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer YOUR_TOKEN"  # 需要先登录获取token
}

# 1. 获取标签类型列表
response = requests.get(f"{BASE_URL}/api/v1/tags/types")
tag_types = response.json()
print("标签类型:", tag_types)

# 2. 创建标签
tag_data = {
    "tag_name": "科技股",
    "tag_code": "tech_stock",
    "tag_slug": "tech-stock",
    "tag_type_id": 1,
    "description": "科技行业股票"
}
response = requests.post(f"{BASE_URL}/api/v1/tags", json=tag_data, headers=headers)
new_tag = response.json()
print("新创建的标签:", new_tag)

# 3. 搜索标签
params = {"keyword": "股票", "page": 1, "size": 10}
response = requests.get(f"{BASE_URL}/api/v1/tags", params=params)
search_results = response.json()
print("搜索结果:", search_results)

# 4. 获取用户画像
response = requests.get(f"{BASE_URL}/api/v1/tags/user-profile", headers=headers)
user_profile = response.json()
print("用户画像:", user_profile)
```

## 注意事项

1. **数据库要求**: 需要 PostgreSQL 数据库支持
2. **索引优化**: 系统已预定义关键索引，注意定期维护
3. **权重更新**: 建议定期运行权重更新任务
4. **画像快照**: 建议每日生成用户画像快照以跟踪变化趋势
5. **API认证**: 大部分API端点需要用户认证，请先通过用户服务获取token
6. **错误处理**: API返回统一的错误格式，注意处理不同的HTTP状态码 