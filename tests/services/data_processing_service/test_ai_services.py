"""
测试AI内容分析服务
"""

import pytest
import json
from unittest.mock import Mock, AsyncMock, patch
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from src.core.database import Base
from src.services.data_processing_service.ai_services import AIContentAnalyzer
from src.services.tag_classification_service.models import Tag, TagType


@pytest.fixture
def db_session():
    """创建测试数据库会话"""
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(engine)
    SessionLocal = sessionmaker(bind=engine)
    session = SessionLocal()
    yield session
    session.close()


@pytest.fixture
def sample_content():
    """示例内容数据"""
    return {
        "title": "央行宣布降准0.5个百分点",
        "content": "中国人民银行今日宣布，决定于2025年2月1日降准0.5个百分点，释放长期资金约1万亿元。此次降准旨在保持银行体系流动性合理充裕，支持实体经济发展。市场分析师认为，此举将对股市产生积极影响，特别是银行股和地产股。",
        "author": "央行新闻发言人",
        "publish_time": "2025-01-15 14:30:00"
    }


@pytest.fixture
def mock_ai_client():
    """模拟AI客户端"""
    mock_client = Mock()
    mock_client.chat_completion = AsyncMock()
    return mock_client


class TestAIContentAnalyzer:
    """测试AI内容分析器"""

    @pytest.fixture(autouse=True)
    def setup(self, mock_ai_client):
        """设置测试环境"""
        with patch('src.services.data_processing_service.ai_services.get_ai_client') as mock_get_client:
            mock_get_client.return_value = mock_ai_client
            self.analyzer = AIContentAnalyzer()
            self.mock_ai_client = mock_ai_client

    async def test_analyze_content_full(self, sample_content):
        """测试完整的内容分析"""
        
        # 模拟AI响应
        self.mock_ai_client.chat_completion.side_effect = [
            # 摘要生成
            {"content": "央行降准释放流动性，支持实体经济发展"},
            # 标签提取
            {"content": "央行,降准,货币政策,流动性,银行,股市"},
            # 内容分类
            {"content": '{"primary_category": "monetary_policy", "impact_scope": "domestic", "urgency_level": "high", "industry": "banking"}'},
            # 实体识别
            {"content": '{"companies": ["中国人民银行"], "people": [], "locations": ["中国"], "stocks": [], "currencies": ["人民币"]}'},
            # 情感分析
            {"content": '{"sentiment": "positive", "market_sentiment": "bullish", "confidence": 0.8}'}
        ]
        
        config = {
            "generate_summary": True,
            "extract_tags": True,
            "classify_content": True,
            "extract_entities": True,
            "sentiment_analysis": True,
            "importance_scoring": True
        }
        
        result = await self.analyzer.analyze_content(sample_content, config)
        
        # 验证结果
        assert "summary" in result
        assert result["summary"] == "央行降准释放流动性，支持实体经济发展"
        
        assert "ai_extracted_tags" in result
        assert "央行" in result["ai_extracted_tags"]
        assert "降准" in result["ai_extracted_tags"]
        
        assert "ai_classifications" in result
        assert result["ai_classifications"]["primary_category"] == "monetary_policy"
        
        assert "mentioned_companies" in result
        assert "中国人民银行" in result["mentioned_companies"]
        
        assert "sentiment_analysis" in result
        assert result["sentiment_analysis"]["sentiment"] == "positive"
        
        assert "importance_score" in result
        assert isinstance(result["importance_score"], float)

    async def test_generate_summary(self, sample_content):
        """测试摘要生成"""
        
        self.mock_ai_client.chat_completion.return_value = {
            "content": "央行降准释放流动性，支持实体经济发展"
        }
        
        summary = await self.analyzer._generate_summary(sample_content)
        
        assert summary == "央行降准释放流动性，支持实体经济发展"
        
        # 验证AI调用
        self.mock_ai_client.chat_completion.assert_called_once()
        call_args = self.mock_ai_client.chat_completion.call_args
        assert "央行宣布降准0.5个百分点" in call_args[1]["messages"][0]["content"]

    async def test_extract_tags(self, sample_content):
        """测试标签提取"""
        
        self.mock_ai_client.chat_completion.return_value = {
            "content": "央行,降准,货币政策,流动性,银行股,实体经济"
        }
        
        tags = await self.analyzer._extract_tags(sample_content)
        
        expected_tags = ["央行", "降准", "货币政策", "流动性", "银行股", "实体经济"]
        assert tags == expected_tags

    async def test_classify_content(self, sample_content):
        """测试内容分类"""
        
        classification_response = {
            "primary_category": "monetary_policy",
            "impact_scope": "domestic", 
            "urgency_level": "high",
            "industry": "banking"
        }
        
        self.mock_ai_client.chat_completion.return_value = {
            "content": json.dumps(classification_response)
        }
        
        classifications = await self.analyzer._classify_content(sample_content)
        
        assert classifications == classification_response
        assert classifications["primary_category"] == "monetary_policy"
        assert classifications["urgency_level"] == "high"

    async def test_extract_entities(self, sample_content):
        """测试实体识别"""
        
        entities_response = {
            "companies": ["中国人民银行", "银行"],
            "people": ["央行新闻发言人"],
            "locations": ["中国"],
            "stocks": ["银行股"],
            "currencies": ["人民币"]
        }
        
        self.mock_ai_client.chat_completion.return_value = {
            "content": json.dumps(entities_response)
        }
        
        entities = await self.analyzer._extract_entities(sample_content)
        
        assert "mentioned_companies" in entities
        assert "中国人民银行" in entities["mentioned_companies"]
        assert "mentioned_people" in entities
        assert "央行新闻发言人" in entities["mentioned_people"]

    async def test_analyze_sentiment(self, sample_content):
        """测试情感分析"""
        
        sentiment_response = {
            "sentiment": "positive",
            "market_sentiment": "bullish",
            "confidence": 0.85
        }
        
        self.mock_ai_client.chat_completion.return_value = {
            "content": json.dumps(sentiment_response)
        }
        
        sentiment = await self.analyzer._analyze_sentiment(sample_content)
        
        assert sentiment == sentiment_response
        assert sentiment["sentiment"] == "positive"
        assert sentiment["market_sentiment"] == "bullish"

    async def test_calculate_importance(self, sample_content):
        """测试重要性评分"""
        
        # 测试包含高重要性关键词的内容
        high_importance_content = {
            "title": "央行突发重大政策",
            "content": "央行宣布重大货币政策调整，立即生效"
        }
        
        score = await self.analyzer._calculate_importance(high_importance_content)
        
        assert score > 0.7  # 应该是高分
        
        # 测试普通内容
        normal_content = {
            "title": "市场分析",
            "content": "今日市场表现平稳"
        }
        
        score = await self.analyzer._calculate_importance(normal_content)
        
        assert 0.3 <= score <= 0.7  # 应该是中等分数

    async def test_match_tags_to_standard(self, db_session):
        """测试AI标签匹配到标准标签"""
        
        # 创建标签类型和标签
        tag_type = TagType(
            type_code="general",
            type_name="通用标签"
        )
        db_session.add(tag_type)
        db_session.commit()
        
        # 创建标准标签
        tag1 = Tag(
            tag_name="央行",
            tag_code="central_bank",
            tag_slug="central-bank",
            tag_type_id=tag_type.id,
            synonyms=["中央银行", "人民银行"]
        )
        tag2 = Tag(
            tag_name="货币政策",
            tag_code="monetary_policy",
            tag_slug="monetary-policy",
            tag_type_id=tag_type.id
        )
        db_session.add_all([tag1, tag2])
        db_session.commit()
        
        # 模拟SessionLocal
        with patch('src.services.data_processing_service.ai_services.SessionLocal') as mock_session:
            mock_session.return_value = db_session
            
            ai_tags = ["央行", "人民银行", "货币政策", "新标签"]
            
            matched_tags = await self.analyzer.match_tags_to_standard(
                ai_tags, "flash_news", 123
            )
            
            # 验证匹配结果
            assert len(matched_tags) == 4
            
            # 精确匹配
            央行_match = next(m for m in matched_tags if m["ai_tag"] == "央行")
            assert 央行_match["confidence"] == 1.0
            assert 央行_match["match_method"] == "exact"
            
            # 同义词匹配
            人民银行_match = next(m for m in matched_tags if m["ai_tag"] == "人民银行")
            assert 人民银行_match["confidence"] == 0.9
            assert 人民银行_match["match_method"] == "synonym"
            
            # 新标签
            新标签_match = next(m for m in matched_tags if m["ai_tag"] == "新标签")
            assert 新标签_match["standard_tag_id"] is None
            assert 新标签_match["match_method"] == "new"

    async def test_ai_service_error_handling(self, sample_content):
        """测试AI服务错误处理"""
        
        # 模拟AI服务异常
        self.mock_ai_client.chat_completion.side_effect = Exception("AI服务不可用")
        
        config = {"generate_summary": True}
        
        # 应该不抛出异常，返回原始数据
        result = await self.analyzer.analyze_content(sample_content, config)
        
        assert result == sample_content  # 应该返回原始数据
        assert "summary" not in result  # 不应该有AI生成的字段

    async def test_invalid_json_response(self, sample_content):
        """测试无效JSON响应处理"""
        
        # 模拟返回无效JSON
        self.mock_ai_client.chat_completion.return_value = {
            "content": "这不是有效的JSON"
        }
        
        classifications = await self.analyzer._classify_content(sample_content)
        
        assert classifications == {}  # 应该返回空字典

    async def test_empty_content_handling(self):
        """测试空内容处理"""
        
        empty_content = {"title": "", "content": ""}
        
        # 测试摘要生成
        summary = await self.analyzer._generate_summary(empty_content)
        assert summary is None
        
        # 测试标签提取
        tags = await self.analyzer._extract_tags(empty_content)
        assert tags == []
        
        # 测试实体识别
        entities = await self.analyzer._extract_entities(empty_content)
        assert entities == {}

    async def test_partial_config(self, sample_content):
        """测试部分配置"""
        
        self.mock_ai_client.chat_completion.return_value = {
            "content": "测试摘要"
        }
        
        # 只启用摘要生成
        config = {
            "generate_summary": True,
            "extract_tags": False,
            "classify_content": False
        }
        
        result = await self.analyzer.analyze_content(sample_content, config)
        
        assert "summary" in result
        assert "ai_extracted_tags" not in result
        assert "ai_classifications" not in result
