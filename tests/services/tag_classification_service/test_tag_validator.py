"""
测试TagValidator标签验证器
"""

import pytest
from decimal import Decimal

from src.services.tag_classification_service.domain.tag_validator import TagValidator


class TestTagValidator:
    """测试标签验证器"""

    def test_validate_basic_fields_valid_tag(self, db_session, sample_tag):
        """测试有效标签的基础字段验证"""
        validator = TagValidator(db_session)
        
        errors = validator.validate_basic_fields(sample_tag)
        assert len(errors) == 0

    def test_validate_basic_fields_empty_name(self, db_session, sample_tag):
        """测试空标签名称的验证"""
        validator = TagValidator(db_session)
        
        sample_tag.tag_name = ""
        errors = validator.validate_basic_fields(sample_tag)
        assert any("Tag name cannot be empty" in error for error in errors)

    def test_validate_basic_fields_empty_code(self, db_session, sample_tag):
        """测试空标签代码的验证"""
        validator = TagValidator(db_session)
        
        sample_tag.tag_code = ""
        errors = validator.validate_basic_fields(sample_tag)
        assert any("Tag code cannot be empty" in error for error in errors)

    def test_validate_basic_fields_invalid_code_pattern(self, db_session, sample_tag):
        """测试无效标签代码格式的验证"""
        validator = TagValidator(db_session)
        
        sample_tag.tag_code = "Invalid Code!"  # 包含空格和特殊字符
        errors = validator.validate_basic_fields(sample_tag)
        assert any("invalid characters" in error for error in errors)

    def test_validate_basic_fields_name_too_long(self, db_session, sample_tag):
        """测试标签名称过长的验证"""
        validator = TagValidator(db_session)
        
        sample_tag.tag_name = "a" * 101  # 超过100字符
        errors = validator.validate_basic_fields(sample_tag)
        assert any("Tag name too long" in error for error in errors)

    def test_validate_basic_fields_invalid_color(self, db_session, sample_tag):
        """测试无效颜色格式的验证"""
        validator = TagValidator(db_session)
        
        sample_tag.color = "red"  # 不是hex格式
        errors = validator.validate_basic_fields(sample_tag)
        assert any("Color must be in hex format" in error for error in errors)

    def test_validate_basic_fields_invalid_lifecycle_stage(self, db_session, sample_tag):
        """测试无效生命周期阶段的验证"""
        validator = TagValidator(db_session)
        
        sample_tag.lifecycle_stage = "invalid_stage"
        errors = validator.validate_basic_fields(sample_tag)
        assert any("Invalid lifecycle stage" in error for error in errors)

    def test_validate_tag_hierarchy_valid(self, db_session, sample_tag, sample_parent_tag):
        """测试有效层次结构的验证"""
        validator = TagValidator(db_session)
        
        # 保存父标签
        db_session.add(sample_parent_tag)
        db_session.commit()
        
        # 设置正确的层次结构
        sample_tag.parent_id = sample_parent_tag.id
        sample_tag.level = sample_parent_tag.level + 1
        sample_tag.path = f"{sample_parent_tag.path}.{sample_tag.tag_code}"
        
        errors = validator.validate_tag_hierarchy(sample_tag)
        assert len(errors) == 0

    def test_validate_tag_hierarchy_too_deep(self, db_session, sample_tag):
        """测试层次过深的验证"""
        validator = TagValidator(db_session)
        
        sample_tag.level = 6  # 超过最大深度5
        errors = validator.validate_tag_hierarchy(sample_tag)
        assert any("hierarchy too deep" in error for error in errors)

    def test_validate_tag_hierarchy_inconsistent_path(self, db_session, sample_tag, sample_parent_tag):
        """测试路径不一致的验证"""
        validator = TagValidator(db_session)
        
        # 保存父标签
        db_session.add(sample_parent_tag)
        db_session.commit()
        
        # 设置不一致的路径
        sample_tag.parent_id = sample_parent_tag.id
        sample_tag.level = sample_parent_tag.level + 1
        sample_tag.path = "wrong.path"  # 错误的路径
        
        errors = validator.validate_tag_hierarchy(sample_tag)
        assert any("Path inconsistent" in error for error in errors)

    def test_validate_tag_hierarchy_nonexistent_parent(self, db_session, sample_tag):
        """测试不存在父标签的验证"""
        validator = TagValidator(db_session)
        
        sample_tag.parent_id = 99999  # 不存在的父标签ID
        errors = validator.validate_tag_hierarchy(sample_tag)
        assert any("Parent tag" in error and "not found" in error for error in errors)

    def test_validate_weight_consistency_valid_weights(self, db_session, sample_tag):
        """测试有效权重的一致性验证"""
        validator = TagValidator(db_session)
        
        sample_tag.base_weight = Decimal('0.5')
        sample_tag.popularity_weight = Decimal('0.3')
        sample_tag.quality_weight = Decimal('0.8')
        sample_tag.temporal_weight = Decimal('0.1')
        sample_tag.positive_feedback_count = 5
        sample_tag.negative_feedback_count = 2
        sample_tag.usage_count = 10
        
        errors = validator.validate_weight_consistency(sample_tag)
        assert len(errors) == 0

    def test_validate_weight_consistency_out_of_range(self, db_session, sample_tag):
        """测试权重超出范围的验证"""
        validator = TagValidator(db_session)
        
        sample_tag.base_weight = Decimal('1.5')  # 超出范围
        errors = validator.validate_weight_consistency(sample_tag)
        assert any("out of range" in error for error in errors)

    def test_validate_weight_consistency_negative_feedback(self, db_session, sample_tag):
        """测试负反馈数的验证"""
        validator = TagValidator(db_session)
        
        sample_tag.positive_feedback_count = -1  # 负数
        errors = validator.validate_weight_consistency(sample_tag)
        assert any("cannot be negative" in error for error in errors)

    def test_validate_tag_uniqueness_unique_tag(self, db_session, sample_tag):
        """测试唯一标签的验证"""
        validator = TagValidator(db_session)
        
        errors = validator.validate_tag_uniqueness(sample_tag)
        assert len(errors) == 0

    def test_validate_tag_uniqueness_duplicate_code(self, db_session, sample_tag, sample_tags):
        """测试重复标签代码的验证"""
        validator = TagValidator(db_session)
        
        # 保存一个标签
        existing_tag = sample_tags[0]
        db_session.add(existing_tag)
        db_session.commit()
        
        # 创建具有相同代码的新标签
        sample_tag.tag_code = existing_tag.tag_code
        sample_tag.id = None  # 确保是新标签
        
        errors = validator.validate_tag_uniqueness(sample_tag)
        assert any("already exists" in error for error in errors)

    def test_validate_business_rules_system_tag_protection(self, db_session, sample_tag):
        """测试系统标签保护的业务规则"""
        validator = TagValidator(db_session)
        
        sample_tag.is_system = True
        errors = validator.validate_business_rules(sample_tag)
        # 注意：这个测试依赖于_is_system_operation的实现
        # 当前实现返回False，所以会有错误
        assert any("Cannot modify system tags" in error for error in errors)

    def test_validate_tag_creation_complete(self, db_session, sample_tag):
        """测试完整的标签创建验证"""
        validator = TagValidator(db_session)
        
        errors = validator.validate_tag_creation(sample_tag)
        # 应该没有错误（除了可能的系统标签保护）
        non_system_errors = [e for e in errors if "system tags" not in e]
        assert len(non_system_errors) == 0

    def test_validate_batch_operation(self, db_session, sample_tags):
        """测试批量验证操作"""
        validator = TagValidator(db_session)
        
        # 设置一些无效的标签
        sample_tags[0].tag_name = ""  # 无效
        sample_tags[1].tag_code = "Invalid Code!"  # 无效
        # sample_tags[2] 保持有效
        
        results = validator.validate_batch_operation(sample_tags)
        
        assert results['total_errors'] > 0
        assert len(results['invalid_tags']) >= 2
        assert len(results['valid_tags']) >= 1
        assert results['validation_summary']['total_tags'] == len(sample_tags)

    def test_get_validation_rules(self, db_session):
        """测试获取验证规则"""
        validator = TagValidator(db_session)
        
        rules = validator.get_validation_rules()
        assert isinstance(rules, dict)
        assert 'max_hierarchy_depth' in rules
        assert 'min_tag_name_length' in rules
        assert 'max_tag_name_length' in rules

    def test_update_validation_rules(self, db_session):
        """测试更新验证规则"""
        validator = TagValidator(db_session)
        
        original_depth = validator.validation_rules['max_hierarchy_depth']
        
        new_rules = {'max_hierarchy_depth': 10}
        validator.update_validation_rules(new_rules)
        
        assert validator.validation_rules['max_hierarchy_depth'] == 10
        assert validator.validation_rules['max_hierarchy_depth'] != original_depth

    def test_validate_synonyms_valid(self, db_session, sample_tag):
        """测试有效同义词的验证"""
        validator = TagValidator(db_session)
        
        import json
        sample_tag.synonyms = json.dumps(["synonym1", "synonym2"])
        
        errors = validator.validate_business_rules(sample_tag)
        synonym_errors = [e for e in errors if "synonym" in e.lower()]
        assert len(synonym_errors) == 0

    def test_validate_synonyms_too_many(self, db_session, sample_tag):
        """测试同义词过多的验证"""
        validator = TagValidator(db_session)
        
        import json
        # 创建超过限制的同义词
        synonyms = [f"synonym{i}" for i in range(15)]  # 超过默认限制10个
        sample_tag.synonyms = json.dumps(synonyms)
        
        errors = validator.validate_business_rules(sample_tag)
        assert any("Too many synonyms" in error for error in errors)

    def test_validate_synonyms_duplicate(self, db_session, sample_tag):
        """测试重复同义词的验证"""
        validator = TagValidator(db_session)
        
        import json
        sample_tag.synonyms = json.dumps(["synonym1", "synonym1"])  # 重复
        
        errors = validator.validate_business_rules(sample_tag)
        assert any("Duplicate synonyms" in error for error in errors)

    def test_validate_synonyms_same_as_name(self, db_session, sample_tag):
        """测试同义词与标签名相同的验证"""
        validator = TagValidator(db_session)
        
        import json
        sample_tag.synonyms = json.dumps([sample_tag.tag_name])  # 与标签名相同
        
        errors = validator.validate_business_rules(sample_tag)
        assert any("cannot be the same as tag name" in error for error in errors)
