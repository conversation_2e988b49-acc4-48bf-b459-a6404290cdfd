"""
测试TagWeightCalculator权重计算器
"""

import pytest
from datetime import datetime, timedelta
from decimal import Decimal

from src.services.tag_classification_service.domain.tag_weight_calculator import TagWeightCalculator


class TestTagWeightCalculator:
    """测试标签权重计算器"""

    def test_calculate_popularity_weight_zero_usage(self, db_session, sample_tag):
        """测试零使用次数的热度权重计算"""
        calculator = TagWeightCalculator(db_session)
        
        # 设置零使用次数
        sample_tag.usage_count = 0
        sample_tag.daily_usage_count = 0
        
        weight = calculator.calculate_popularity_weight(sample_tag)
        assert weight == Decimal('0.0')

    def test_calculate_popularity_weight_normal_usage(self, db_session, sample_tag):
        """测试正常使用次数的热度权重计算"""
        calculator = TagWeightCalculator(db_session)
        
        # 设置正常使用次数
        sample_tag.usage_count = 100
        sample_tag.daily_usage_count = 10
        
        weight = calculator.calculate_popularity_weight(sample_tag)
        assert 0 <= weight <= 1
        assert isinstance(weight, Decimal)

    def test_calculate_quality_weight_no_feedback(self, db_session, sample_tag):
        """测试无反馈的质量权重计算"""
        calculator = TagWeightCalculator(db_session)
        
        # 设置无反馈
        sample_tag.positive_feedback_count = 0
        sample_tag.negative_feedback_count = 0
        
        weight = calculator.calculate_quality_weight(sample_tag)
        assert weight == Decimal('0.5')  # 默认中性

    def test_calculate_quality_weight_with_feedback(self, db_session, sample_tag):
        """测试有反馈的质量权重计算"""
        calculator = TagWeightCalculator(db_session)
        
        # 设置正面反馈
        sample_tag.positive_feedback_count = 8
        sample_tag.negative_feedback_count = 2
        
        weight = calculator.calculate_quality_weight(sample_tag)
        assert 0.5 < weight <= 1  # 应该高于中性
        assert isinstance(weight, Decimal)

    def test_calculate_temporal_weight_no_usage(self, db_session, sample_tag):
        """测试从未使用的时效权重计算"""
        calculator = TagWeightCalculator(db_session)
        
        # 设置从未使用
        sample_tag.last_used_at = None
        
        weight = calculator.calculate_temporal_weight(sample_tag)
        assert weight == Decimal('0.0')

    def test_calculate_temporal_weight_recent_usage(self, db_session, sample_tag):
        """测试最近使用的时效权重计算"""
        calculator = TagWeightCalculator(db_session)
        
        # 设置最近使用
        sample_tag.last_used_at = datetime.now() - timedelta(days=1)
        
        weight = calculator.calculate_temporal_weight(sample_tag)
        assert 0.5 < weight <= 1  # 最近使用应该有较高权重
        assert isinstance(weight, Decimal)

    def test_calculate_temporal_weight_old_usage(self, db_session, sample_tag):
        """测试很久前使用的时效权重计算"""
        calculator = TagWeightCalculator(db_session)
        
        # 设置很久前使用
        sample_tag.last_used_at = datetime.now() - timedelta(days=400)
        
        weight = calculator.calculate_temporal_weight(sample_tag)
        assert 0 <= weight < 0.5  # 很久前使用应该有较低权重
        assert isinstance(weight, Decimal)

    def test_calculate_computed_weight(self, db_session, sample_tag):
        """测试综合权重计算"""
        calculator = TagWeightCalculator(db_session)
        
        # 设置各项权重
        sample_tag.base_weight = Decimal('0.8')
        sample_tag.usage_count = 50
        sample_tag.daily_usage_count = 5
        sample_tag.positive_feedback_count = 7
        sample_tag.negative_feedback_count = 3
        sample_tag.last_used_at = datetime.now() - timedelta(days=10)
        
        weight = calculator.calculate_computed_weight(sample_tag)
        assert 0 <= weight <= 1
        assert isinstance(weight, Decimal)
        
        # 验证权重是各项权重的加权平均
        expected_min = sample_tag.base_weight * Decimal('0.4')  # 至少包含基础权重的贡献
        assert weight >= expected_min

    def test_update_tag_weights_success(self, db_session, sample_tag):
        """测试成功更新标签权重"""
        calculator = TagWeightCalculator(db_session)
        
        # 保存标签到数据库
        db_session.add(sample_tag)
        db_session.commit()
        
        # 设置一些数据
        sample_tag.usage_count = 30
        sample_tag.positive_feedback_count = 5
        sample_tag.last_used_at = datetime.now() - timedelta(days=5)
        db_session.commit()
        
        result = calculator.update_tag_weights(sample_tag.id)
        assert result is True
        
        # 验证权重已更新
        db_session.refresh(sample_tag)
        assert sample_tag.popularity_weight is not None
        assert sample_tag.quality_weight is not None
        assert sample_tag.temporal_weight is not None
        assert sample_tag.computed_weight is not None

    def test_update_tag_weights_nonexistent_tag(self, db_session):
        """测试更新不存在标签的权重"""
        calculator = TagWeightCalculator(db_session)
        
        result = calculator.update_tag_weights(99999)
        assert result is False

    def test_batch_update_weights(self, db_session, sample_tags):
        """测试批量更新权重"""
        calculator = TagWeightCalculator(db_session)
        
        # 保存标签到数据库
        for tag in sample_tags:
            db_session.add(tag)
        db_session.commit()
        
        updated_count = calculator.batch_update_weights(limit=len(sample_tags))
        assert updated_count == len(sample_tags)

    def test_get_weight_analysis(self, db_session, sample_tag):
        """测试获取权重分析"""
        calculator = TagWeightCalculator(db_session)
        
        # 保存标签到数据库
        db_session.add(sample_tag)
        db_session.commit()
        
        # 设置一些数据
        sample_tag.usage_count = 25
        sample_tag.daily_usage_count = 3
        sample_tag.positive_feedback_count = 4
        sample_tag.negative_feedback_count = 1
        sample_tag.last_used_at = datetime.now() - timedelta(days=7)
        db_session.commit()
        
        analysis = calculator.get_weight_analysis(sample_tag.id)
        assert analysis is not None
        assert 'base_weight' in analysis
        assert 'popularity_weight' in analysis
        assert 'quality_weight' in analysis
        assert 'temporal_weight' in analysis
        assert 'computed_weight' in analysis
        assert 'usage_count' in analysis
        assert 'last_used_days_ago' in analysis

    def test_get_weight_analysis_nonexistent_tag(self, db_session):
        """测试获取不存在标签的权重分析"""
        calculator = TagWeightCalculator(db_session)
        
        analysis = calculator.get_weight_analysis(99999)
        assert analysis is None

    def test_update_config(self, db_session):
        """测试更新配置"""
        calculator = TagWeightCalculator(db_session)
        
        original_config = calculator.get_config()
        
        new_config = {
            'base_weight_factor': 0.5,
            'popularity_weight_factor': 0.25,
            'temporal_decay_days': 300
        }
        
        calculator.update_config(new_config)
        updated_config = calculator.get_config()
        
        assert updated_config['base_weight_factor'] == 0.5
        assert updated_config['popularity_weight_factor'] == 0.25
        assert updated_config['temporal_decay_days'] == 300
        
        # 其他配置应该保持不变
        assert updated_config['quality_weight_factor'] == original_config['quality_weight_factor']

    def test_get_config(self, db_session):
        """测试获取配置"""
        calculator = TagWeightCalculator(db_session)
        
        config = calculator.get_config()
        assert isinstance(config, dict)
        assert 'base_weight_factor' in config
        assert 'popularity_weight_factor' in config
        assert 'quality_weight_factor' in config
        assert 'temporal_weight_factor' in config
        assert 'temporal_decay_days' in config

    def test_weight_calculation_consistency(self, db_session, sample_tag):
        """测试权重计算的一致性"""
        calculator = TagWeightCalculator(db_session)
        
        # 设置相同的数据
        sample_tag.base_weight = Decimal('0.7')
        sample_tag.usage_count = 40
        sample_tag.daily_usage_count = 4
        sample_tag.positive_feedback_count = 6
        sample_tag.negative_feedback_count = 2
        sample_tag.last_used_at = datetime.now() - timedelta(days=15)
        
        # 多次计算应该得到相同结果
        weight1 = calculator.calculate_computed_weight(sample_tag)
        weight2 = calculator.calculate_computed_weight(sample_tag)
        
        assert weight1 == weight2

    def test_weight_bounds(self, db_session, sample_tag):
        """测试权重边界值"""
        calculator = TagWeightCalculator(db_session)
        
        # 测试极端值
        sample_tag.base_weight = Decimal('1.0')
        sample_tag.usage_count = 10000
        sample_tag.daily_usage_count = 1000
        sample_tag.positive_feedback_count = 100
        sample_tag.negative_feedback_count = 0
        sample_tag.last_used_at = datetime.now()
        
        weight = calculator.calculate_computed_weight(sample_tag)
        assert 0 <= weight <= 1
        
        # 测试最小值
        sample_tag.base_weight = Decimal('0.0')
        sample_tag.usage_count = 0
        sample_tag.daily_usage_count = 0
        sample_tag.positive_feedback_count = 0
        sample_tag.negative_feedback_count = 0
        sample_tag.last_used_at = None
        
        weight = calculator.calculate_computed_weight(sample_tag)
        assert 0 <= weight <= 1
