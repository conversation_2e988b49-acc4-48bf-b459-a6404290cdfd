-- 金十数据快讯 data_source_configs 详细配置
-- 基于成功的采集测试结果生成的精确配置

-- 1. 首先创建数据源记录
INSERT INTO data_sources (
    name,
    collection_method,
    content_category,
    business_data_type,
    base_url,
    description,
    crawl_mode,
    crawl_interval,
    priority,
    max_concurrent_tasks,
    supports_realtime,
    use_proxy,
    request_delay_min,
    request_delay_max,
    status,
    created_by
) VALUES (
    '金十数据快讯',
    'web_dynamic',  -- 需要动态渲染
    'financial_news',
    'flash_news',
    'https://www.jin10.com',
    '金十数据财经快讯，提供实时财经资讯和市场动态',
    'interval',
    300,  -- 5分钟采集一次
    8,    -- 高优先级
    1,
    true,
    false,
    2,
    5,
    'active',
    'system'
) ON CONFLICT (name) DO UPDATE SET
    collection_method = EXCLUDED.collection_method,
    content_category = EXCLUDED.content_category,
    business_data_type = EXCLUDED.business_data_type,
    base_url = EXCLUDED.base_url,
    description = EXCLUDED.description,
    crawl_mode = EXCLUDED.crawl_mode,
    crawl_interval = EXCLUDED.crawl_interval,
    priority = EXCLUDED.priority,
    updated_at = NOW();

-- 2. 创建数据源配置记录（基于测试验证的配置）
INSERT INTO data_source_configs (
    source_id,
    version,
    selector_config,
    headers_config,
    javascript_config,
    anti_crawler_config,
    retry_config,
    is_active,
    change_reason,
    changed_by
) VALUES (
    (SELECT id FROM data_sources WHERE name = '金十数据快讯'),
    1,
    -- selector_config: 基于测试结果验证的选择器配置
    '{
        "flash_selector": "[data-id]",
        "flash_item_class": "jin-flash_item",
        "title_selector": ".J_flash_text",
        "content_selector": ".J_flash_text", 
        "time_selector": ".jin-flash_time",
        "container_selector": ".jin-flash_list",
        "backup_selectors": {
            "flash_items": [
                ".jin-flash_item",
                ".J_flash_item",
                "[class*=\"flash_item\"]",
                "[data-id]"
            ],
            "content": [
                ".J_flash_text",
                ".jin-flash_text-box p",
                ".flash-text",
                "p.J_flash_text"
            ],
            "time": [
                ".jin-flash_time", 
                "[class*=\"time\"]",
                ".time",
                ".jin-flash_h .jin-flash_time"
            ]
        },
        "data_attributes": {
            "unique_id": "data-id",
            "start_time": "data-starttime",
            "element_id": "id"
        }
    }',
    
    -- headers_config: 反检测请求头配置
    '{
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "DNT": "1",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Cache-Control": "max-age=0"
    }',
    
    -- javascript_config: 动态渲染配置
    '{
        "enabled": true,
        "wait_for_selector": "[data-id]",
        "wait_timeout": 10000,
        "scroll_config": {
            "enabled": true,
            "scroll_count": 3,
            "scroll_delay": 2000,
            "scroll_to_bottom": true,
            "scroll_step": 500
        },
        "network_idle_wait": 2000,
        "additional_wait": 3000,
        "viewport": {
            "width": 1920,
            "height": 1080
        },
        "browser_args": [
            "--no-sandbox",
            "--disable-setuid-sandbox",
            "--disable-dev-shm-usage",
            "--disable-web-security",
            "--disable-blink-features=AutomationControlled"
        ],
        "stealth_mode": {
            "enabled": true,
            "hide_webdriver": true,
            "fake_plugins": true,
            "fake_languages": ["zh-CN", "zh", "en"]
        }
    }',
    
    -- anti_crawler_config: 反爬虫配置
    '{
        "rate_limiting": {
            "enabled": true,
            "min_delay": 2000,
            "max_delay": 5000,
            "random_delay": true
        },
        "user_agent_rotation": {
            "enabled": false,
            "agents": []
        },
        "proxy_rotation": {
            "enabled": false
        },
        "session_management": {
            "enabled": true,
            "session_timeout": 300000,
            "max_requests_per_session": 50
        },
        "detection_avoidance": {
            "random_mouse_movements": false,
            "random_typing_delays": false,
            "fake_human_behavior": true
        }
    }',
    
    -- retry_config: 重试配置
    '{
        "max_retries": 3,
        "retry_delays": [2000, 5000, 10000],
        "retry_on_status_codes": [429, 500, 502, 503, 504],
        "retry_on_timeout": true,
        "retry_on_network_error": true,
        "exponential_backoff": true,
        "max_retry_delay": 30000
    }',
    
    true,
    '基于成功测试结果创建的精确配置，验证可采集20+条快讯数据',
    'system'
) ON CONFLICT (source_id, version) DO UPDATE SET
    selector_config = EXCLUDED.selector_config,
    headers_config = EXCLUDED.headers_config,
    javascript_config = EXCLUDED.javascript_config,
    anti_crawler_config = EXCLUDED.anti_crawler_config,
    retry_config = EXCLUDED.retry_config,
    is_active = EXCLUDED.is_active,
    change_reason = EXCLUDED.change_reason;

-- 3. 创建事件驱动采集规则
INSERT INTO event_driven_crawl_rules (
    source_id,
    rule_name,
    trigger_type,
    trigger_config,
    advance_minutes,
    delay_minutes,
    repeat_interval_minutes,
    max_repeat_count,
    custom_task_config,
    priority_boost,
    is_active
) VALUES (
    (SELECT id FROM data_sources WHERE name = '金十数据快讯'),
    '市场开盘前后密集采集',
    'time_based',
    '{
        "trigger_times": [
            "08:30",  
            "09:30",  
            "13:00",  
            "15:00",  
            "21:30",  
            "04:00"   
        ],
        "timezone": "Asia/Shanghai",
        "weekdays_only": false,
        "description": "在重要市场时间点进行密集采集"
    }',
    5,    -- 提前5分钟开始
    15,   -- 延后15分钟结束
    2,    -- 每2分钟重复一次
    10,   -- 最多重复10次
    '{
        "high_frequency": true,
        "priority_boost": 5,
        "max_concurrent": 1
    }',
    5,    -- 优先级提升5
    true
) ON CONFLICT (source_id, rule_name) DO UPDATE SET
    trigger_config = EXCLUDED.trigger_config,
    advance_minutes = EXCLUDED.advance_minutes,
    delay_minutes = EXCLUDED.delay_minutes,
    repeat_interval_minutes = EXCLUDED.repeat_interval_minutes,
    max_repeat_count = EXCLUDED.max_repeat_count,
    custom_task_config = EXCLUDED.custom_task_config,
    priority_boost = EXCLUDED.priority_boost,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- 4. 创建重要财经事件触发规则
INSERT INTO event_driven_crawl_rules (
    source_id,
    rule_name,
    trigger_type,
    trigger_config,
    advance_minutes,
    delay_minutes,
    repeat_interval_minutes,
    max_repeat_count,
    custom_task_config,
    priority_boost,
    is_active
) VALUES (
    (SELECT id FROM data_sources WHERE name = '金十数据快讯'),
    '重要财经事件触发采集',
    'financial_event',
    '{
        "event_types": [
            "central_bank_announcement",
            "economic_data_release",
            "market_volatility",
            "geopolitical_event"
        ],
        "importance_threshold": 7,
        "auto_trigger": true,
        "description": "重要财经事件发生时自动触发采集"
    }',
    2,    -- 提前2分钟
    30,   -- 延后30分钟
    1,    -- 每1分钟重复
    30,   -- 最多重复30次
    '{
        "emergency_mode": true,
        "priority_boost": 10,
        "max_concurrent": 2
    }',
    10,   -- 优先级提升10
    true
) ON CONFLICT (source_id, rule_name) DO UPDATE SET
    trigger_config = EXCLUDED.trigger_config,
    updated_at = NOW();

-- 5. 验证配置
SELECT 
    ds.name,
    ds.collection_method,
    ds.business_data_type,
    ds.crawl_interval,
    ds.priority,
    ds.status,
    dsc.version,
    dsc.is_active as config_active,
    COUNT(edcr.id) as event_rules_count
FROM data_sources ds
LEFT JOIN data_source_configs dsc ON ds.id = dsc.source_id AND dsc.is_active = true
LEFT JOIN event_driven_crawl_rules edcr ON ds.id = edcr.source_id AND edcr.is_active = true
WHERE ds.name = '金十数据快讯'
GROUP BY ds.id, ds.name, ds.collection_method, ds.business_data_type, ds.crawl_interval, ds.priority, ds.status, dsc.version, dsc.is_active;

-- 配置说明注释
/*
配置要点说明：

1. 数据源配置 (data_sources):
   - collection_method: web_dynamic - 需要JavaScript动态渲染
   - business_data_type: flash_news - 快讯类型
   - crawl_interval: 300秒 - 5分钟采集间隔
   - priority: 8 - 高优先级

2. 选择器配置 (selector_config):
   - 主选择器: [data-id] - 最可靠的快讯元素选择器
   - 内容选择器: .J_flash_text - 快讯文本内容
   - 时间选择器: .jin-flash_time - 发布时间
   - 备用选择器: 提供多层次备用方案

3. JavaScript配置 (javascript_config):
   - 等待元素: [data-id]
   - 滚动加载: 3次滚动，每次2秒间隔
   - 网络空闲等待: 确保动态内容完全加载
   - 反检测: 隐藏webdriver特征

4. 反爬虫配置 (anti_crawler_config):
   - 速率限制: 2-5秒随机延迟
   - 会话管理: 300秒超时，最多50个请求
   - 人类行为模拟: 避免被检测

5. 重试配置 (retry_config):
   - 最大重试: 3次
   - 指数退避: 2秒、5秒、10秒
   - 状态码重试: 429, 5xx错误

6. 事件驱动规则:
   - 市场时间密集采集: 开盘前后高频采集
   - 财经事件触发: 重要事件自动触发采集

使用方法：
1. 执行以上SQL创建完整配置
2. 系统会自动使用配置进行数据采集
3. 可通过crawl_tasks表监控采集任务状态
4. 处理后的数据会存储到flash_news表

注意事项：
- 配置基于实际测试验证，可采集20+条快讯
- 支持版本管理和配置更新
- 包含完整的反爬虫和容错机制
- 事件驱动规则可根据需要调整
*/
